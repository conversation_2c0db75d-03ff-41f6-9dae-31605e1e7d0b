<?php

namespace App\Http\Controllers\Api\V1\Setting;

use App\Http\Controllers\Controller;
use App\Models\Nationality;
use App\Http\Resources\CountryResource;
use App\Http\Resources\GovernorateResource;
use App\Http\Resources\CityResource;
use App\Http\Traits\Api_Trait;
use App\Models\Country;
use App\Models\Governorate;
use App\Models\City;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CityController extends Controller
{
    use Api_Trait;

    public function indexCountry()
    {
        $countries = Country::get();
        return $this->returnData(CountryResource::collection($countries), [helperTrans('api.country data')], 200);
    }

    public function indexNationality()
    {
        $country = Nationality::get();
        return $this->returnData(CountryResource::collection($country), [helperTrans('api.country data')], 200);
    }

    public function indexGovernorate(Request $request)
    {
        $country_id = $request->input('country_id');

        if (!$country_id) {
            return $this->returnError([helperTrans('api.country id required')], 400);
        }

        $governorates = Governorate::where('nationality_id', $country_id)->get();
        return $this->returnData(GovernorateResource::collection($governorates)
            ->prepend([
                'id' => null,
                'name' => $request->header('lang') == 'ar' ? 'الجميع' : 'All',
            ]), [helperTrans('api.governorates data')], 200);
    }

    public function index(Request $request)
    {
        $filter = $request->input('filter.governorate_id');
        $cities = City::when((int)$filter, function ($query, $governorateId) {
            return $query->where('governorate_id', $governorateId);
        })->get();
        return $this->returnData((CityResource::collection($cities) // Convert collection to an array
            ->prepend([
                'id' => null,
                'name' => $request->header('lang') == 'ar' ? 'الجميع' : 'All',
            ])), [helperTrans('api.cities data')], 200);
    }
}
