<?php

namespace App\Http\Controllers\Dashboard;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Http\Requests\FileRequest;
use App\Http\Resources\Dashboard\ContractResource;
use App\Http\Traits\Api_Trait;
use App\Imports\Dashboard\ContractImport;
use App\Models\Contract;
use App\Models\ImportedFile;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ContractController extends Controller
{
    use Api_Trait;
    public function index(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'provider_id'   => 'required|exists:providers,id',
            ],
            []
        );

        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }
        $contracts = Contract::where('provider_id', $request->provider_id)->get();
        return $this->returnData(ContractResource::collection($contracts), [helperTrans('api.Contracts Data')], 200);
    }
    public function store(Request $request)
    {
        DB::beginTransaction();
        try {
            $validated = $request->validate([
                'admin_id' => 'required|exists:admins,id',
                'provider_id' => 'required|exists:providers,id',
                'need_approve' => 'required|boolean',
                'official_full_name' => 'required|string',
                'commercial_registration_number' => 'nullable|string',
                'national_id' => 'nullable|string',
                'tax_card_number' => 'nullable|string',
                'tax_card_due_date' => 'nullable|date',
                'insurance_company_ids' => 'nullable|array',
                'insurance_company_ids.*' => 'exists:insurance_companies,id',
                'other_place_ids' => 'nullable|array',
                'other_place_ids.*' => 'exists:other_places,id',
                'private_phone_number' => 'nullable|string',
                'private_email' => 'nullable|email',
                'representative_full_name' => 'nullable|string',
                'position' => 'nullable|string',
                'phone' => 'nullable|string',
                'email' => 'nullable|email',
                'facebook_link' => 'nullable|url',
                'instagram_link' => 'nullable|url',
                'contract_start_date' => 'required|date',
                'contract_end_date' => 'required|date',
                'payment_method_id' => 'nullable|exists:payment_methods,id',
                'claims_due_date' => 'nullable|integer',
                'admin_fees' => 'nullable|numeric',
                'notes' => 'nullable|string',
                'attachments' => 'nullable|array',
                'attachments.*.id' => 'exists:attachments,id',
                'attachments.*.title' => 'nullable|string',
                'banks' => 'nullable|array',
                'wallets' => 'nullable|array',
                'instapays' => 'nullable|array',
                'delegate_manager_ids' => 'nullable|array',
                'delegate_manager_ids.*' => 'exists:delegate_managers,id',
            ]);

            $contract = Contract::create($validated);

            // Attach insurance companies
            if ($request->has('insurance_company_ids')) {
                $contract->insuranceCompanies()->sync($request->insurance_company_ids);
            }

            // Attach other places
            if ($request->has('other_place_ids')) {
                $contract->otherPlaces()->sync($request->other_place_ids);
            }
            if ($request->has('delegate_manager_ids')) {
                $contract->delegateManagers()->sync($request->delegate_manager_ids);
            }

            if ($request->has('attachments')) {
                foreach ($request->attachments as $attachment) {
                    $contract->attachments()->create([
                        'attachment_id' => $attachment['id'],
                        'title' => $attachment['title']
                    ]);
                }
            }

            if ($request->has('banks')) {
                foreach ($request->banks as $bank) {
                    $contract->banks()->create($bank);
                }
            }

            if ($request->has('wallets')) {
                foreach ($request->wallets as $wallet) {
                    $contract->wallets()->create($wallet);
                }
            }

            if ($request->has('instapays')) {
                foreach ($request->instapays as $instapay) {
                    $contract->instapays()->create($instapay);
                }
            }

            DB::commit();
            return $this->returnData(new ContractResource($contract), [helperTrans('api.Contract created')], 201);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError($e->getMessage(), 500);
        }
    }
    public function show(Contract $contract)
    {
        return $this->returnData(new ContractResource($contract), [helperTrans('api.Contract Data')], 200);
    }
    public function update(Request $request, Contract $contract)
    {
        try {
            DB::beginTransaction();

            $validated = $request->validate([
                'admin_id' => 'required|exists:admins,id',
                'provider_id' => 'required|exists:providers,id',
                'need_approve' => 'required|boolean',
                'official_full_name' => 'required|string',
                'commercial_registration_number' => 'nullable|string',
                'national_id' => 'nullable|string',
                'tax_card_number' => 'nullable|string',
                'tax_card_due_date' => 'nullable|date',
                'insurance_company_ids' => 'nullable|array',
                'insurance_company_ids.*' => 'exists:insurance_companies,id',
                'other_place_ids' => 'nullable|array',
                'other_place_ids.*' => 'exists:other_places,id',
                'private_phone_number' => 'nullable|string',
                'private_email' => 'nullable|email',
                'representative_full_name' => 'nullable|string',
                'position' => 'nullable|string',
                'phone' => 'nullable|string',
                'email' => 'nullable|email',
                'facebook_link' => 'nullable|url',
                'instagram_link' => 'nullable|url',
                'contract_start_date' => 'required|date',
                'contract_end_date' => 'required|date',
                'payment_method_id' => 'nullable|exists:payment_methods,id',
                'claims_due_date' => 'nullable|integer',
                'admin_fees' => 'nullable|numeric',
                'notes' => 'nullable|string',
                'attachments' => 'nullable|array',
                'attachments.*.id' => 'exists:attachments,id',
                'attachments.*.title' => 'nullable|string',
                'banks' => 'nullable|array',
                'wallets' => 'nullable|array',
                'instapays' => 'nullable|array',
                'delegate_manager_ids' => 'nullable|array',
                'delegate_manager_ids.*' => 'exists:delegate_managers,id',
            ]);

            $contract->update($validated);

            // Sync pivot relationships
            $contract->insuranceCompanies()->sync($request->insurance_company_ids ?? []);
            $contract->otherPlaces()->sync($request->other_place_ids ?? []);
            $contract->delegateManagers()->sync($request->delegate_manager_ids ?? []);
            // Sync attachments (detach old first)
            if ($request->has('attachments')) {
                $contract->attachments()->delete(); // Remove old
                foreach ($request->attachments as $attachment) {
                    $contract->attachments()->create([
                        'attachment_id' => $attachment['id'],
                        'title' => $attachment['title']
                    ]);
                }
            }

            // Update banks
            if ($request->has('banks')) {
                $contract->banks()->delete();
                foreach ($request->banks as $bank) {
                    $contract->banks()->create($bank);
                }
            }

            // Update wallets
            if ($request->has('wallets')) {
                $contract->wallets()->delete();
                foreach ($request->wallets as $wallet) {
                    $contract->wallets()->create($wallet);
                }
            }

            // Update instapays
            if ($request->has('instapays')) {
                $contract->instapays()->delete();
                foreach ($request->instapays as $instapay) {
                    $contract->instapays()->create($instapay);
                }
            }

            DB::commit();
            return $this->returnData(new ContractResource($contract), [helperTrans('api.Contract updated')], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError($e->getMessage(), 500);
        }
    }
    public function destroy(Contract $contract)
    {
        try {
            DB::beginTransaction();

            // Optionally delete relations
            $contract->insuranceCompanies()->detach();
            $contract->otherPlaces()->detach();
            $contract->delegateManagers()->detach();
            $contract->attachments()->delete();
            $contract->banks()->delete();
            $contract->wallets()->delete();
            $contract->instapays()->delete();

            $contract->delete();

            DB::commit();
            return $this->returnData(null, [helperTrans('api.Contract deleted')], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError($e->getMessage(), 500);
        }
    }

    public function importContract(FileRequest $request)
    {
        try {
            $importedFile = ImportedFile::saveFile(
                $request->file('file'),
                auth('sanctum')->id()
            );

            $import = new ContractImport($importedFile->id);
            Excel::import($import, $importedFile->path);

            $response = [
                'success_count' => $import->getSuccessCount(),
                'error_count' => $import->getErrorCount(),
                'errors' => $import->getErrors(),
                'imported_file_id' => $importedFile->id,
                'filename' => $importedFile->file_name
            ];

            if ($import->getErrorCount() > 0) {
                return $this->returnData([
                    $response,
                    helperTrans('api.Contract Template Has Errors'),
                    "errors Count: {$import->getErrorCount()}"
                ], 422);
            }

            return $this->returnData($response, [
                helperTrans('api.Contract imported successfully'),
                "Successfully imported {$import->getSuccessCount()} contracts"
            ], 200);
        } catch (\Exception $e) {
            return $this->returnErrorImport([
                'message' => 'Contract import failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
