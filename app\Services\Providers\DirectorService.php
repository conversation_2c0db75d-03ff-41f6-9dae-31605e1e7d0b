<?php

namespace App\Services\Providers;

use App\Exceptions\DoctoriaException;
use App\Models\{Director, DirectorBranch};
use Illuminate\Support\Facades\Hash;

class DirectorService
{
    public function createDirector($branch, $request, $importedFile = null)
    {

        $roles = [
            'assistant',
            'outpatient_clinics_manager',
            'inpatient_clinics_manager',
            'pharmacy_director',
            'radiology_director',
            'laboratory_director',
        ];
        foreach ($roles as $role) {
            $nameKey = "{$role}_name";
            $phoneKey = "{$role}_phone";
            $emailKey = "{$role}_email";
            $passwordKey = "{$role}_password";
            // throw new DoctoriaException([
            //     $nameKey,
            //     $request[$nameKey],
            //     $phoneKey,
            //     // $request->$phoneKey,
            //     $emailKey,
            //     // $request->$emailKey,
            //     $passwordKey,
            //     // $request->$passwordKey,
            // ]);

            if (
                !empty($request[$nameKey]) &&
                !empty($request[$phoneKey]) &&
                !empty($request[$emailKey]) &&
                !empty($request[$passwordKey])
            ) {
                $director = Director::firstOrCreate(
                    [
                        'name' => $request[$nameKey],
                        'phone' => $request[$phoneKey],
                        'email' => $request[$emailKey], // use unique key to avoid duplicates
                    ],
                    [
                        'password' => Hash::make($request[$passwordKey]),
                        'role_type' => $role,
                        'imported_file_id' => $importedFile,
                    ]
                );

                DirectorBranch::firstOrCreate([
                    'director_id' => $director->id,
                    'provider_branch_id' => $branch->id,
                    'imported_file_id' => $importedFile,
                ]);
            }
        }
    }
}
