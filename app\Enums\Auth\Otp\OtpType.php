<?php
namespace App\Enums\Auth\Otp;
enum OtpType: string {
    case REGISTER = 'register';
    case FORGOT_PASSWORD = 'forgot-password';
    public static function status($value): string {
        if ($value == self::REGISTER) {
            return '<span class="badge badge-success">Register</span>';
        } elseif ($value == self::FORGOT_PASSWORD) {
            return '<span class="badge badge-primary">Forgot-Password</span>';
        } else {
            return '<span class="badge badge-default">' . trans('general.not_status_assigned') . '</span>';
        }
    }
}
