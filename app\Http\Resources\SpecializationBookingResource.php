<?php

namespace App\Http\Resources;

use App\Models\PromoCode;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SpecializationBookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id'            => (int)$this->id,
            'date'          => $this->date,
            'time'          => $this->time,
            'from'          => $this->from,
            'description'   => $this->description,
            'booking_type'  => $this->booking_type,
            'referral_code' => $this->referral_code,
            'to'            => $this->to,
            'location'      => $this->location,
            'visit'         => $this->visit,
            'price'         => $this->price,
            'relative_id'   => $this->relative_id,
            'docs'          => $this->docs,
            'promo_code'    => $this->promo_code_id ? new PromoCodeResource(PromoCode::find($this->promo_code_id)) : null,
            'phone'         => $this->phone,
            'clinic'        => new DoctorClinicResource($this->clinic),
            'status'        => $this->status,
            'patient'       => new PatientResource($this->whenLoaded('patient')),
            'branch'        => new DoctorBranchResource($this->whenLoaded('branch')),
            'doctor'        => new DoctorResource($this->whenLoaded('doctor')),
            'created_at'    => $this->created_at->diffForHumans(),
            'latitude'      => $this->latitude,
            'longitude'     =>  $this->longitude,
            'governorate_id' => $this->governorate_id,
            'city_id'       => $this->city_id,
            'street'        => $this->street,
            'invoice_id'    => $this->invoice_id,
        ];
    }
}
