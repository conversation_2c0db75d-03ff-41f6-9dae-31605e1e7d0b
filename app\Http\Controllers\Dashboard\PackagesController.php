<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\PackageResource;
use App\Http\Requests\Dashboard\PackageRequest;
use App\Http\Traits\Api_Trait;
use App\Models\Package;
use Illuminate\Http\Request;

class PackagesController extends Controller
{
    use Api_Trait;

    /**
     * Display a listing of packages with filtering and pagination
     */
    public function index(Request $request)
    {
        $packages = Package::with(['partners', 'patientSubscribes', 'packageRequests', 'mainServicesPackage.mainService', 'elements', 'services'])
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                      ->orWhere('description', 'like', '%' . $request->search . '%');
                });
            })
            ->when($request->name, function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->name . '%');
            })
            ->when($request->min_price, function ($query) use ($request) {
                $query->where('price', '>=', $request->min_price);
            })
            ->when($request->max_price, function ($query) use ($request) {
                $query->where('price', '<=', $request->max_price);
            })
            ->when($request->show_in_app !== null, function ($query) use ($request) {
                $query->where('show_in_app', $request->show_in_app);
            })
            ->orderBy($request->sort_by ?? 'name', $request->sort_direction ?? 'asc')
            ->paginate($request->per_page ?? 25);

        return $this->paginatedResponse(
            PackageResource::collection($packages),
            helperTrans('api.Packages Data'),
            200
        );
    }

    /**
     * Store a newly created package
     */
    public function store(PackageRequest $request)
    {
        $data = $request->validated();
        
        $package = Package::create($data);

        return $this->returnData(
            new PackageResource($package->load(['partners', 'patientSubscribes', 'packageRequests', 'mainServicesPackage.mainService', 'elements', 'services'])),
            [helperTrans('api.Package created successfully')], 201
        );
    }

    /**
     * Display the specified package
     */
    public function show($id)
    {
        $package = Package::find($id);
        if (!$package) {
            return $this->returnError(helperTrans('api.Package Not Found'), 404);
        }
        
        $package->load(['partners', 'patientSubscribes', 'packageRequests', 'mainServicesPackage.mainService', 'elements', 'services']);
        
        return $this->returnData(
            new PackageResource($package),
            [helperTrans('api.Package Data')], 200
        );
    }

    /**
     * Update the specified package
     */
    public function update(PackageRequest $request, $id)
    {
        $package = Package::find($id);
        if (!$package) {
            return $this->returnError(helperTrans('api.Package Not Found'), 404);
        }
        
        $data = $request->validated();
        
        $package->update($data);

        return $this->returnData(
            new PackageResource($package->load(['partners', 'patientSubscribes', 'packageRequests', 'mainServicesPackage.mainService', 'elements', 'services'])),
            [helperTrans('api.Package updated successfully')], 200
        );
    }

    /**
     * Remove the specified package
     */
    public function destroy($id)
    {
        $package = Package::find($id);
        if (!$package) {
            return $this->returnError(helperTrans('api.Package Not Found'), 404);
        }
        
        // Check if package has associated records
        if ($package->partners()->exists() || 
            $package->patientSubscribes()->exists() || 
            $package->packageRequests()->exists()) {
            return $this->returnError([
                'message' => 'Cannot delete package that has associated partners, subscriptions, or requests'
            ], 422);
        }

        $package->delete();

        return $this->returnData(
            null,
            [helperTrans('api.Package deleted successfully')],
            200
        );
    }
}
