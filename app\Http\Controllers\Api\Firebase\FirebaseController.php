<?php

namespace App\Http\Controllers\Api\Firebase;

use App\Http\Controllers\Controller;
use App\Http\Traits\Api_Trait;
use App\Models\FirebaseToken;
use App\Models\Patient;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Notification;
use App\Notifications\PushFireBaseNotification;



class FirebaseController extends Controller
{
    use Api_Trait;

    //
    public function update_firebase_token(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'user_type' => 'required|in:patient,doctor,provider',
                'type' => 'required|in:ios,android',
                'token' => 'required',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnError(collect($validator->errors())->flatten(1), 403);
        }

        if ($request->user_type == 'doctor') {
            $user = auth('doctor')->user();
        } else if ($request->user_type == 'provider') {
            $user = auth('provider')->user();
        } else {
            $user = auth('patient')->user();
        }
        if (!$user) {
            return $this->returnError([helperTrans('api.Unauthorized')], 401);
        }

        $token = FirebaseToken::where('type', $request->type)->where('user_type', $request->user_type)->where('user_id', $user->id)->where('token', $request->token)->first();

        if (!$token) {

            $token = FirebaseToken::create([
                'user_id' => $user->id,
                'user_type' => $request->user_type,
                'type' => $request->type,
                'token' => $request->token,
                'lang' => $request->header('lang') ?? 'en',
            ]);
        }


        return  $this->returnSuccessMessage([helperTrans('api.token updated successfully')]);
    }


    public function push_notify(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'patient_id' => 'required|exists:patients,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnError(collect($validator->errors())->flatten(1), 403);
        }

        // $this->sendFCMNotification($parent_ides, 'patient', $notificationObject);

        Notification::send(Patient::find($request->patient_id), new PushFireBaseNotification([
            'title' => 'notification title',
            'body' => 'notification body ',
        ]));

        return $this->returnSuccessMessage("notification sent successfully !!");
    }
}
