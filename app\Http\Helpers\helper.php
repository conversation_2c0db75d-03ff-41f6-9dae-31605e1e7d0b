<?php

use App\Exceptions\DoctoriaException;
use App\Exceptions\FileNotFoundException;
use App\Models\Booking;
use App\Models\Doctor;
use App\Models\FirebaseToken;
use App\Models\Notification;
use App\Models\Package;
use App\Models\Patient;
use App\Models\Review;
use App\Models\PromoCode;
use App\Models\SpecializationBooking;
use Carbon\Carbon;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use Stichoza\GoogleTranslate\GoogleTranslate;

if (!function_exists('admin')) {
    function admin()
    {
        return auth()->guard('admin');
    }
}

function patient()
{
    return auth()->guard('patient');
}

function doctor()
{
    return auth()->guard('doctor');
}

function provider()
{
    return auth()->guard('provider');
}


if (!function_exists('is_json')) {
    function is_json($string)
    {
        json_decode($string);
        return (json_last_error() == JSON_ERROR_NONE);
    }
}


if (!function_exists('get_user_data')) {
    function get_user_data()
    {
        $guards = ['admin', 'web', 'patient', 'doctor'];
        foreach ($guards as $guard) {
            if (auth($guard)->check()) {
                return (object)[
                    'user' => auth($guard)->user(),
                    'guard' => ucfirst($guard)
                ];
            }
        }
        return null;
    }
}

function remove_invalid_characters($str): array|string
{
    return str_ireplace(['\'', '"', ',', ';', '<', '>'], ' ', $str);
}

function setting()
{
    return \App\Models\Setting::firstorCreate([]);
}
function languages()
{
    return \App\Models\Language::where('status', 1)->get();
}
if (!function_exists('helperTrans')) {
    function helperTrans($str): array|string|\Illuminate\Contracts\Translation\Translator|\Illuminate\Contracts\Foundation\Application|null
    {

        $arrayOfKeys = explode('.', $str);
        $file = $arrayOfKeys[0] ?? 'file';
        $key = $arrayOfKeys[1] ?? '';

        $local = 'en';

        \Illuminate\Support\Facades\App::setLocale(session_lang());


        try {
            $lang_array = include(resource_path("lang/$local/$file.php"));

            $processed_key = ucfirst(str_replace('_', ' ', remove_invalid_characters($key)));

            if (!array_key_exists($key, $lang_array)) {
                $lang_array[$key] = $processed_key;
                $str = "<?php return " . var_export($lang_array, true) . ";";
                file_put_contents(resource_path("lang/$local/$file.php"), $str);
                $result = $processed_key;
            } else {
                $result = __("$file.$key");
            }
        } catch (\Exception $exception) {
            $result = __("$file.$key");
        }

        return $result;
    }
}


if (!function_exists('saas')) {
    function saas()
    {
        return auth()->guard('saas');
    }
}

if (!function_exists('teacher')) {
    function teacher()
    {
        return auth()->guard('teacher');
    }
}

if (!function_exists('moderator')) {
    function moderator()
    {
        return auth()->guard('moderator');
    }
}



if (!function_exists('setting')) {
    function setting()
    {
        return \App\Models\Setting::firstorFail();
    }
}


if (!function_exists('get_file')) {
    function get_file($file = null)
    {
        // Storage::exists( $file )
        if (filter_var($file, FILTER_VALIDATE_URL)) {
            $file_path = $file;
        } elseif ($file) {
            $file_path = asset('storage/uploads') . '/' . $file;
        } else {
            $file_path = asset('assets/default/imgs/default-img.png');
        }
        return $file_path;
    } //end
}

if (!function_exists('new_get_file')) {
    function new_get_file($file = null, $type = 'default')
    {
        // Storage::exists( $file )

        // switch ($type) {
        //     case 'person':
        //         $file_path = asset('assets/default/imgs/providers/person.png');
        //         break;
        //     case 'lab':
        //         $file_path = asset('assets/default/imgs/providers/lab.png');
        //         break;
        //     case 'center':
        //         $file_path = asset('assets/default/imgs/providers/center.png');
        //         break;
        //     case 'pharmacy':
        //         $file_path = asset('assets/default/imgs/providers/pharmacy.png');
        //         break;
        //     default:
        //         $file_path = asset('assets/default/imgs/default-img.png');
        //         break;
        // }
        if (filter_var($file, FILTER_VALIDATE_URL)) {
            $file_path = $file;
        } elseif ($file) {
            $file_path = asset('storage/uploads') . '/' . $file;
        } elseif ($type == 'person') {
            $file_path = asset('assets/default/imgs/providers/person.png');
        } elseif ($type == 'lab') {
            $file_path = asset('assets/default/imgs/providers/lab.png');
        } elseif ($type == 'center') {
            $file_path = asset('assets/default/imgs/providers/center.png');
        } elseif ($type == 'pharmacy') {
            $file_path = asset('assets/default/imgs/providers/pharmacy.png');
        }
        // elseif($type=='hospital') {
        //     $file_path = asset('assets/default/imgs/hospital.png');
        // }
        else {
            $file_path = asset('assets/default/imgs/default-img.png');
        }

        return $file_path;
    } //end
}


if (!function_exists('get_lang')) {
    function get_lang()
    {
        return \LaravelLocalization::setLocale() ?? 'en';
    }
}


if (!function_exists('session_lang')) {
    function session_lang()
    {
        $lang = 'ar';
        /*if (session()->get('lang') && in_array(session()->get('lang'), ['ar', 'en'])) {
            $lang = session()->get('lang') ? session()->get('lang') : 'default';
        }*/

        if (get_lang() && in_array(get_lang(), ['ar', 'en'])) {
            $lang = get_lang();
        }

        if (request()->get('lang') && in_array(request()->get('lang'), ['ar', 'en'])) {
            $lang = request()->get('lang');
        }

        if (request()->post('lang') && in_array(request()->post('lang'), ['ar', 'en'])) {
            $lang = request()->post('lang');
        }

        if (request()->header('lang') && in_array(request()->header('lang'), ['ar', 'en'])) {
            $lang = request()->header('lang');
        }
        return $lang;
    }

    if (!function_exists('uploadFile')) {
        function uploadFile(string $directory, $file, $oldFile = null): ?string
        {
            if ($file) {
                // Delete the old file if it exists
                if ($oldFile && Storage::exists($oldFile)) {
                    Storage::delete($oldFile);
                }

                // Store the new file
                return $file->store($directory, 'public');
            }
            return null;
        }
    }
}


### Ensure that the booking is belongs to the logged in patient
if (!function_exists('get_booking')) {
    function get_booking($id, $status = [])
    {
        if (!count($status)) {

            $booking = Booking::find($id);
            if (!$booking) {
                $booking = SpecializationBooking::find($id);
                if (!$booking) {
                    return null;
                }
            }
        } else {
            $booking = Booking::whereIn('status', $status)->find($id);
            if (!$booking) {
                $booking = SpecializationBooking::whereIn('status', $status)->find($id);
                if (!$booking) {
                    return null;
                }
            }
        }

        return $booking;
    }
}

if (!function_exists('create_ref')) {
    function create_ref()
    {
        return Carbon::now()->format('YmdHis') . Str::random(5);
    }
}

function getNextSharedId()
{
    return DB::table('shared_ids')->insertGetId([]) + 5000;
}

if (!function_exists('get_doctor_rate')) {
    function get_doctor_rate($doctor_id)
    {
        $doctor = Doctor::find($doctor_id);
        if (!$doctor) return null;

        $sumRating = Review::where('doctor_id', $doctor_id)->sum('rating');
        $countRating = Review::where('doctor_id', $doctor_id)->count();

        $total_rate = round(($doctor->initial_rate * 20 + $sumRating) / ($countRating + 20), 1, PHP_ROUND_HALF_UP); //initial rateing deal like 20 review


        return $total_rate;
    }
}

if (!function_exists('run_push_notification')) {

    function run_push_notification($user_id, $user_type, $info)
    {
        $tokens = FirebaseToken::where([
            'user_id' => $user_id,
            'user_type' => $user_type
        ])->pluck('token');



        // dd($tokens);
        $firebase = (new \Kreait\Firebase\Factory())->withServiceAccount(public_path('firebase.json'))
            ->createMessaging();

        foreach ($tokens as $token) {
            try {
                $lang = FirebaseToken::where([
                    'token' => $token,
                ])->first()->lang ?? 'ar';
                //google translate
                $translator = new GoogleTranslate($lang);
                $info['title'] = $translator->translate($info['title']);
                $info['body'] = $translator->translate($info['body']);

                $message = \Kreait\Firebase\Messaging\CloudMessage::withTarget('token', $token)
                    ->withNotification($info)
                    ->withData([]);

                $firebase->send($message);
            } catch (\Exception $e) {
                continue;
            }
        }



        Notification::create([
            'title' => $info['title'],
            'body' => $info['body'],
            'user_type' => $user_type,
            'user_id' => $user_id,
            'type' => "booking",
        ]);
    }
}

if (!function_exists('send_notification_to_general_doctors')) {
    function send_notification_to_general_doctors($info)
    {
        $doctors = Doctor::where('doctor_type', 'general')->get();
        foreach ($doctors as $doctor) {
            run_push_notification($doctor->id, 'doctor', $info);
        }
    }
}

if (!function_exists('price_after_promocode')) {
    function price_after_promocode($price, $promocode_id)
    {
        $promocode = PromoCode::find($promocode_id);
        $discount = 0;
        if ($promocode) {

            if ($promocode->discount_type == 'fixed_amount')
                $discount = $promocode->discount_value;

            else if ($promocode->discount_type == 'percentage') {
                $discount = $price * ($promocode->discount_value / 100);
            }
        }
        return $price - $discount;
    }
}

function get_full_client_no($client_no, $type = "IND")
{
    return $client_no . '-' . $type . '-' . $client_no;
}

function generate_individual_card_id($package_id)
{
    $package = Package::find($package_id);

    $last_card_id = Patient::whereNotNull('card_id')->orderBy('id', 'desc')->first()->card_id;

    $num1 = substr($last_card_id, 0, 6);
    $new_num1 = str_pad((int)$num1 + 1, strlen($num1), '0', STR_PAD_LEFT);

    $num2 = substr($last_card_id, 6, 6);
    $new_num2 = str_pad((int)$num2 + 1, strlen($num2), '0', STR_PAD_LEFT);

    $card_id = $new_num1 . $new_num2 . '00' . ($package->code ?? '00');

    return $card_id;
}
function getFileAttributes($filename)
{
    // $form_name = explode("_", $filename)[0];
    $file_path = storage_path() . "/app/downloads/" . $filename;

    // Determine content type based on file extension
    $extension = pathinfo($filename, PATHINFO_EXTENSION);
    $contentType = match (strtolower($extension)) {
        'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'xls' => 'application/vnd.ms-excel',
        'csv' => 'text/csv',
        default => 'application/octet-stream'
    };

    $headers = [
        'Content-Type' => $contentType,
        'Content-Disposition' => 'attachment; filename=' . $filename,
    ];

    if (!file_exists($file_path)) {
        throw new FileNotFoundException($filename, 404);
    }
    return ['path' => $file_path, 'headers' => $headers];
}

function haversine($lat1, $lon1, $lat2, $lon2)
{
    $earthRadius = 6371; // in kilometers
    $latFrom = deg2rad($lat1);
    $lonFrom = deg2rad($lon1);
    $latTo = deg2rad($lat2);
    $lonTo = deg2rad($lon2);

    $latDelta = $latTo - $latFrom;
    $lonDelta = $lonTo - $lonFrom;

    $angle = 2 * asin(sqrt(pow(sin($latDelta / 2), 2) +
        cos($latFrom) * cos($latTo) * pow(sin($lonDelta / 2), 2)));

    return $earthRadius * $angle;
}
