<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractAttachment extends Model
{
    use HasFactory;
    protected $table = 'contract_attachments';
    protected $fillable = [
        'contract_id',
        'attachment_id',
        'title',
    ];
    protected $guarded = [];
    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }
    public function attachment()
    {
        return $this->belongsTo(Attachment::class);
    }
}
