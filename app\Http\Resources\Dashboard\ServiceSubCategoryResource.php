<?php

namespace App\Http\Resources\Dashboard;

use App\Http\Resources\Dashboard\ServiceCategoryResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceSubCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'service_category' => ServiceCategoryResource::make($this->service_category),
        ];
    }
}
