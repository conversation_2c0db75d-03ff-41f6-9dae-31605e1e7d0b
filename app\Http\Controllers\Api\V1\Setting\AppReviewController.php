<?php

namespace App\Http\Controllers\Api\V1\Setting;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\AppReviewRequest;
use App\Http\Traits\Api_Trait;
use App\Models\AppReview;
use Illuminate\Http\Request;

class AppReviewController extends Controller
{
    use Api_Trait;

    function save(AppReviewRequest $request)
    {

        $patient = patient()->user()?->id;
        $doctor = doctor()->user()?->id;

        $user_type = $patient ? 'patient' : 'doctor';
        $user_id = $patient ?? $doctor;

        $review = AppReview::create([
            ...$request->validated(),
            'user_type' => $user_type,
            'user_id'   => $user_id,
        ]);

        if ($review) {
            return $this->returnSuccessMessage([helperTrans('api.review_saved_successfully')]);
        }

        return $this->returnError([helperTrans('api.something went wrong')], 500);
    }
}
