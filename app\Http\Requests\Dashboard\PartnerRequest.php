<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Validation\Rule;

class PartnerRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'contact_person' => ['required', 'string', 'max:255'],
            'contact_person_position' => ['nullable', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'address' => ['nullable', 'string'],
            'status' => ['nullable', 'in:active,inactive'],
            'contract_start_date' => ['nullable', 'date'],
            'contract_end_date' => ['nullable', 'date'],

            // Foreign keys
            'promo_code_id' => ['nullable', 'exists:promo_codes,id'],
            'package_id' => ['nullable', 'exists:packages,id'],
            'country_id' => ['nullable', 'exists:countries,id'],
            'doctoria_employee_id' => ['nullable', 'exists:doctoria_employees,id'],
        ];

        // Handle unique fields for create vs update
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            // For update requests
            $rules['client_no'] = [
                'required',
                'string',
                'max:255',
                Rule::unique('partners', 'client_no')->ignore($this->route('partner'))
            ];

            $rules['email'] = [
                'nullable',
                'email',
                'max:255',
                Rule::unique('partners', 'email')->ignore($this->route('partner'))
            ];
        } else {
            // For create requests
            $rules['client_no'] = ['required', 'string', 'max:255', 'unique:partners,client_no'];
            $rules['email'] = ['nullable', 'email', 'max:255', 'unique:partners,email'];
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'client_no.required' => 'Client number is required',
            'client_no.unique' => 'This client number is already taken',
            'contact_person.required' => 'Contact person is required',
            'email.email' => 'Please provide a valid email address',
            'email.unique' => 'This email is already registered',
            'status.in' => 'Status must be either active or inactive',
            'promo_code_id.exists' => 'Selected promo code does not exist',
            'package_id.exists' => 'Selected package does not exist',
        ];
    }
}
