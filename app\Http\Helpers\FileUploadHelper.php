<?php

namespace App\Http\Helpers;

use Illuminate\Support\Facades\Storage;

class FileUploadHelper
{
    public static function uploadFiles(string $directory, $file, $previousFile = null): string
    {
        // Delete previous file if exists
        if ($previousFile && Storage::exists($previousFile)) {
            Storage::delete($previousFile);
        }

        // Store the new file
        return $file->store($directory, 'public');
    }
}
