<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Traits\Api_Trait;
use App\Models\Service;
use App\Http\Resources\Dashboard\ServiceResource;
use Illuminate\Http\Request;

class ServiceController extends Controller
{
    use Api_Trait;

    public function index(Request $request)
    {
        $services = Service::with('service_category', 'service_sub_category')->when($request->service_category_id, function ($query) use ($request) {
            $query->where('service_category_id', $request->service_category_id);
        })->when($request->service_sub_category_id, function ($query) use ($request) {
            $query->where('service_sub_category_id', $request->service_sub_category_id);
        })->orderBy('id', 'desc')->paginate($request->per_page ?? 25);
        return $this->paginatedResponse(
            ServiceResource::collection($services),
            helperTrans('api.Service Data'),
            200
        );
    }
    public function show($id)
    {
        $service = Service::with('service_category', 'service_sub_category')->find($id);
        if (!$service) {
            return $this->returnError(helperTrans('api.Service Not Found'), 404);
        }
        return $this->returnData(ServiceResource::make($service), [helperTrans('api.Service Data')], 200);
    }
    public function store(Request $request)
    {
        $service = Service::create($request->all());
        return $this->returnData(ServiceResource::make($service), [helperTrans('api.Service Data')], 200);
    }
    public function update(Request $request, $id)
    {
        $service = Service::with('service_category', 'service_sub_category')->find($id);
        if (!$service) {
            return $this->returnError(helperTrans('api.Service Not Found'), 404);
        }
        $service->update($request->all());
        return $this->returnData(ServiceResource::make($service), [helperTrans('api.Service Data')], 200);
    }
    public function destroy($id)
    {
        $service = Service::with('service_category', 'service_sub_category')->find($id);
        if (!$service) {
            return $this->returnError(helperTrans('api.Service Not Found'), 404);
        }
        $service->delete();
        return $this->returnData(ServiceResource::make($service), [helperTrans('api.Service Data')], 200);
    }
}
