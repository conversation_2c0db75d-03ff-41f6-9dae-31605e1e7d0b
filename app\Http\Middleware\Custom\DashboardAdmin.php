<?php

namespace App\Http\Middleware\Custom;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class DashboardAdmin
{
    /**
     * Handle an incoming request.
     *
     * @param \Closure(\Illuminate\Http\Request): (\Symfony\Component\HttpFoundation\Response) $next
     */
    public function handle(Request $request, Closure $next)
    {
        $user = auth()->user();

        // Debug information for development/staging
        if (config('app.debug')) {
            Log::info('DashboardAdmin Middleware Debug', [
                'user' => $user,
                'user_type' => $user ? get_class($user) : null,
                'token_present' => $request->bearerToken() ? 'yes' : 'no',
            ]);
        }

        if ($user instanceof \App\Models\Admin) {
            return $next($request);
        }

        // More descriptive error messages
        if (!$user) {
            return response()->json([
                'message' => 'Unauthenticated',
                'error' => 'No authenticated user found'
            ], 401);
        }

        return response()->json([
            'message' => 'Unauthorized',
            'error' => 'User is not an admin'
        ], 403);
    }
}
