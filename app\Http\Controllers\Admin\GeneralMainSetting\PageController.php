<?php
namespace App\Http\Controllers\Admin\GeneralMainSetting;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Traits\ResponseTrait;
use App\Models\Page;
use Yajra\DataTables\DataTables;
use App\Http\Traits\Upload_Files;
class PageController extends Controller {
    use  ResponseTrait, Upload_Files;
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $pages = Page::query()->latest();
            return DataTables::of($pages)
                ->addColumn('action', function ($page) {
                    $delete = '';
                    return '
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $page->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';
                })
                ->editColumn('image', function ($page) {
                    return '
                            <a data-fancybox="" href="' . get_file($page->image) . '">
                                <img height="60px" src="' . get_file($page->image) . '">
                            </a>
                             ';
                })
                ->editColumn('icon', function ($page) {
                    return '
                            <a data-fancybox="" href="' . get_file($page->icon) . '">
                                <img height="60px" src="' . get_file($page->icon) . '">
                            </a>
                             ';
                })
                ->editColumn('status', function ($page) {
                    $status = $page->status;
                    $badgeClass = $status === 'Active' ? 'badge-success' : 'badge-danger';
                    return '<span class="badge ' . $badgeClass . '">' . $status . '</span>';
                })
                ->editColumn('name', function ($page) {
                    $nameAr = $page->getTranslation('name', 'ar', false) ?? 'N/A';
                    $nameEn = $page->getTranslation('name', 'en', false) ?? 'N/A';
                    return '<span style="color:red;">' . htmlspecialchars($nameAr) . '</span> / <span style="color:blue;">' . htmlspecialchars($nameEn) . '</span>';
                })
                ->editColumn('created_at', function ($page) {
                    return date('Y/m/d', strtotime($page->created_at));
                })
                ->escapeColumns([])
                ->make(true);
        }
        return view('Admin.CRUDS.pages.index');
    }

    /*public function store(Request $request) {
        //dd($request->all());
        try {
            $this->validate($request, [
                'name' => 'required|array', // Expecting translations as an array
                'description' => 'required|array', // Expecting translations as an array
                'title' => 'required|array', // Expecting translations as an array
                'type' => 'required|in:webview,api', // Ensure 'type' is either 'webview' or 'api'
                'status' => 'required|in:Active,inactive',
                'link' => 'nullable|required_if:type,webview', // Ensure link is required if type is 'webview'
            ]);
            $data = $request->except('link');
            $page = new Page();
            $page->setTranslations('name', $request->input('name'));
            $page->setTranslations('description', $request->input('description'));
            $page->setTranslations('title', $request->input('title'));
            $page->type = $request->input('type');
            $page->status = $request->input('status');
            if ($request->input('type') === 'webview') 
                $page->link = $request->input('link');
            $page->save();
            return $this->addResponse('Page created successfully.');
        } catch (\Exception $e) {
            return $this->returnError('An error occurred while creating the Page. Please try again later.', 500);
        }
    }*/

    /*public function store(Request $request)
    {
        try {
            $this->validate($request, [
                'name' => 'required|array',
                'description' => 'required|array',
                'title' => 'required|array',
                'type' => 'required|in:webview,api',
                'status' => 'required|in:Active,inactive',
                'link' => 'nullable|required_if:type,webview',
            ]);
            $page = new Page();
            $page->setTranslations('name', $request->input('name'));
            $page->setTranslations('description', $request->input('description'));
            $page->setTranslations('title', $request->input('title'));
            $page->type = $request->input('type');
            $page->status = $request->input('status');
            if ($request->input('type') === 'webview') {
                $page->link = $request->input('link');
            }
            $page->save(); // احفظ الصفحة
            return $this->addResponse('Page created successfully.');
        } catch (\Exception $e) {
            return $this->returnError('An error occurred: ' . $e->getMessage(), 500);
        }
    }*/

    public function store(Request $request)
    {
        try {
            $data = $this->validate($request, [
                'name' => 'required',
                //'description' => 'required',
                //'title' => 'required',
                'type' => 'required|in:webview,api',
                'status' => 'required|in:Active,inactive',
                'link' => 'nullable|required_if:type,webview',
                'icon' => 'nullable|image|max:2048',
                'image' => 'nullable|image|max:2048',
            ]);
            $page = new Page();
            $page->name = $request->input('name');
            //$page->title = $request->input('title');
            $page->description = $request->input('description');
            $page->type = $request->input('type');
            $page->status = $request->input('status');
            if ($request->input('type') === 'webview') {
                $page->link = $request->input('link');
            }
            if ($request->hasFile('image'))
                $page->image = $this->uploadFiles('pages', $request->file('image'), null);
            if ($request->hasFile('icon'))
               $page->icon = $this->uploadFiles('pages', $request->file('icon'), null);
            //dd($page);
            $page->save();
            return $this->addResponse('Page created successfully.');
        } catch (\Exception $e) {
            \Log::error('Error creating page: ' . $e->getMessage());
            return $this->returnError('An error occurred: ' . $e->getMessage(), 500);
        }
    }





    public function update(Request $request, $id) {
        $row = Page::findOrFail($id);
        dd($row);
        //$row->update($data);
        return $this->updateResponse();
    }

    public function updateStatus(Request $request, $id) {
        $provider = Page::findOrFail($id);
        $status = $request->input('status') === 'Active';
        $provider->status = $status;
        $provider->save();
        return response()->json(['message' => 'Status updated successfully']);
    }

    public function destroy($id) {
        $row = Page::findOrFail($id);
        $row->delete();
        return $this->deleteResponse('Page deleted successfully.');
    }
}
