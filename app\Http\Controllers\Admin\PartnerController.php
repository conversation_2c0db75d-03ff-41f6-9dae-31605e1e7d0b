<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\ResponseTrait;
use App\Models\Partner;
use App\Models\Package;
use App\Models\Patient;
use App\Models\PromoCode;
use App\Imports\PatientsImport;
use App\Models\Relative;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Maatwebsite\Excel\Facades\Excel;

class PartnerController extends Controller
{
    use ResponseTrait;

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $partners = Partner::with(['package', 'promoCode'])->latest()->get();
            return DataTables::of($partners)
                ->addColumn('action', function ($partner) {
                    $html = '<div class="btn-group">
                        <a href="' . route('admin.partners.patients', $partner->id) . '" class="btn btn-sm btn-info">
                            <i class="ri-user-line align-middle"></i> ' . helperTrans('admin.Patients') . '
                        </a>

                    </div>';
                    return $html;
                    // <div class="dropdown d-inline-block">
                    //     <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                    //         <i class="ri-more-fill align-middle"></i>
                    //     </button>
                    //     <ul class="dropdown-menu dropdown-menu-end">
                    //         <li><a class="dropdown-item" href="' . route('admin.partners.edit', $partner->id) . '">
                    //             <i class="ri-pencil-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.edit') . '</a></li>
                    //         <li><a class="dropdown-item" href="' . route('admin.partners.show', $partner->id) . '">
                    //             <i class="ri-eye-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.view') . '</a></li>
                    //         <li>
                    //             <form action="' . route('admin.partners.destroy', $partner->id) . '" method="POST" class="d-inline">
                    //                 ' . csrf_field() . '
                    //                 ' . method_field('DELETE') . '
                    //                 <button type="submit" class="dropdown-item remove-item-btn" onclick="return confirm(\'' . helperTrans('admin.Are you sure?') . '\')">
                    //                     <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.delete') . '
                    //                 </button>
                    //             </form>
                    //         </li>
                    //     </ul>
                    // </div>
                })
                ->editColumn('status', function ($partner) {
                    $html = $partner->status === 'active'
                        ? '<span class="badge bg-success">active</span>'
                        : '<span class="badge bg-danger">inactive</span>';
                    return $html;
                })
                ->rawColumns(['action', 'status'])
                ->make(true);
        }
        return view('Admin.CRUDS.partners.index');
    }

    public function create()
    {
        $packages = Package::all();
        $promoCodes = PromoCode::all();
        return view('Admin.CRUDS.partners.create', compact('packages', 'promoCodes'));
    }

    public function store(Request $request)
    {

        $request->validate([
            'client_no' => 'required|string|max:255|unique:partners,client_no',
            'contact_person' => 'required|string|max:255',
            'email' => 'nullable|email|unique:partners,email',
            'phone' => 'nullable|string|max:20',
            'address' => 'nullable|string',
            'status' => 'nullable|in:active,inactive',
            'promo_code_id' => 'nullable|exists:promo_codes,id',
            'package_id' => 'nullable|exists:packages,id',
        ]);

        $data = $request->all();
        $data['phone'] = "+2" . $request->phone;

        Partner::create($data);

        return redirect()->route('admin.partners.index')
            ->with('success', helperTrans('admin.Partner created successfully.'));
    }

    public function show(Partner $partner)
    {
        return view('Admin.CRUDS.partners.show', compact('partner'));
    }

    public function edit(Partner $partner)
    {
        $packages = Package::all();
        $promoCodes = PromoCode::all();
        return view('Admin.CRUDS.partners.edit', compact('partner', 'packages', 'promoCodes'));
    }

    public function update(Request $request, Partner $partner)
    {
        $request->validate([
            'client_no' => 'required|string|max:255|unique:partners,client_no,' . $partner->id,
            'contact_person' => 'required|string|max:255',
            'email' => 'required|email|unique:partners,email,' . $partner->id,
            'phone' => 'required|string|max:20',
            'address' => 'required|string',
            'status' => 'required|in:active,inactive',
            'promo_code_id' => 'nullable|exists:promo_codes,id',
            'package_id' => 'required|exists:packages,id',
        ]);

        $data = $request->all();
        $data['phone'] = "+2" . $request->phone;

        $partner->update($data);

        return redirect()->route('admin.partners.index')
            ->with('success', helperTrans('admin.Partner updated successfully'));
    }

    public function destroy(Partner $partner)
    {
        $partner->delete();
        return redirect()->route('admin.partners.index')
            ->with('success', helperTrans('admin.Partner deleted successfully.'));
    }

    public function patients(Request $request, Partner $partner)
    {
        if ($request->ajax()) {
            // dd($partner);
            $patients = Patient::where('partner_id', $partner->id)->latest()->get();
            return DataTables::of($patients)
                ->addColumn('client_no', function ($patient) use ($partner) {
                    return $partner->client_no;
                })->editColumn('status', function ($patient) {
                    $relative = Relative::where('card_id', $patient->card_id)->exists();
                    if ($relative) return '<span class="badge bg-warning">linked</span>';

                    return $patient->status == 1
                        ? '<span class="badge bg-success">active</span>'
                        : '<span class="badge bg-danger">unverified</span>';
                })
                ->editColumn('updated_at', function ($patient) {
                    return $patient->updated_at->format('Y-m-d H:i');
                })
                ->rawColumns(['status', 'updated_at'])
                ->make(true);
        }

        return view('Admin.CRUDS.partners.patients', compact('partner'));
    }

    public function importPatients(Request $request, Partner $partner)
    {
        $request->validate([
            'excel_file' => 'required|file|mimes:xlsx,xls,csv'
        ]);

        try {
            Excel::import(new PatientsImport($partner->id, $partner->package_id), $request->file('excel_file'));
            return redirect()->back()->with('success', helperTrans('admin.Patients imported successfully'));
        } catch (\Exception $e) {
            dd($e->getMessage());
            return redirect()->back()->with('error', helperTrans('admin.Error importing patients') . ': ' . $e->getMessage());
        }
    }
}
