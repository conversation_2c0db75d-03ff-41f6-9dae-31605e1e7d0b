<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class StatusBooking extends Model
{
    use HasFactory;
    protected $table = 'status_bookings';
    protected $fillable = [
        'name',
        'booking_type_id',
        'notes',
        'color',
    ];
    public function bookingType()
    {
        return $this->belongsTo(BookingType::class);
    }
}
