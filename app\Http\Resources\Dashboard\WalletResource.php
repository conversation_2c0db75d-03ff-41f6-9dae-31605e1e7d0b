<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WalletResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'wallet_name' => $this->wallet_name,
            'wallet_mobile_number' => $this->wallet_mobile_number,
            'wallet_cheque_name' => $this->wallet_cheque_name,
        ];
    }
}
