<?php

namespace App\Http\Controllers;

use App\Http\Requests\Dashboard\AttachmentRequest;
use App\Http\Traits\Api_Trait;
use App\Models\Attachment;
use Illuminate\Http\Request;

class AttachmentController extends Controller
{
    use Api_Trait;
    public function upload(AttachmentRequest $request)
    {
        $attachmentIds = [];

        $folder = $request->get('folder', 'attachments'); // default to 'attachments' if no folder provided

        foreach ($request->file('files') as $file) {
            $path = $file->store($folder, 'public'); // store in the specific folder under 'public'

            $attachment = Attachment::create([
                'file_path' => $path,
                'original_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getClientMimeType(),
            ]);

            $attachmentIds[] = $attachment->id;
        }

        return $this->returnData($attachmentIds, __('messages.uploaded_successfully'));
    }
}
