<?php

namespace App\Http\Controllers;

use App\Http\Requests\Dashboard\AttachmentRequest;
use App\Http\Traits\Api_Trait;
use App\Models\Attachment;
use Illuminate\Http\Request;

class AttachmentController extends Controller
{
    use Api_Trait;
    public function upload(AttachmentRequest $request)
    {
        $attachmentIds = [];

        // throw new BalaghatException($request->file('files'));
        foreach ($request->file('files') as $file) {
            $path = $file->store('attachments', 'public');

            $attachment = Attachment::create([
                'file_path' => $path,
                'original_name' => $file->getClientOriginalName(),
                'mime_type' => $file->getClientMimeType(),
            ]);

            $attachmentIds[] = $attachment->id;
        }

        return $this->returnData($attachmentIds, __('messages.uploaded_successfully'));
    }
}
