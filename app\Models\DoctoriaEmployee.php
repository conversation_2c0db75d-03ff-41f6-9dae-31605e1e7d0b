<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class DoctoriaEmployee extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = 'doctoria_employees';

    /**
     * The attributes that are mass assignable.
     *
     * @var array<int, string>
     */
    protected $fillable = [
        'name',
        'phone',
        'email',
        'position',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array<string, string>
     */
    protected $casts = [
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    /**
     * Scope to filter by name
     */
    public function scopeWhenName($query, $name)
    {
        return $query->when($name, function ($q) use ($name) {
            return $q->where('name', 'like', '%' . $name . '%');
        });
    }

    /**
     * Scope to filter by email
     */
    public function scopeWhenEmail($query, $email)
    {
        return $query->when($email, function ($q) use ($email) {
            return $q->where('email', 'like', '%' . $email . '%');
        });
    }

    /**
     * Scope to filter by phone
     */
    public function scopeWhenPhone($query, $phone)
    {
        return $query->when($phone, function ($q) use ($phone) {
            return $q->where('phone', 'like', '%' . $phone . '%');
        });
    }

    /**
     * Scope to filter by position
     */
    public function scopeWhenPosition($query, $position)
    {
        return $query->when($position, function ($q) use ($position) {
            return $q->where('position', 'like', '%' . $position . '%');
        });
    }

    /**
     * Get the employee's full contact information
     */
    public function getFullContactAttribute()
    {
        $contact = [];
        
        if ($this->phone) {
            $contact[] = 'Phone: ' . $this->phone;
        }
        
        if ($this->email) {
            $contact[] = 'Email: ' . $this->email;
        }
        
        return implode(' | ', $contact);
    }

    /**
     * Get the employee's display name with position
     */
    public function getDisplayNameAttribute()
    {
        return $this->name . ($this->position ? ' (' . $this->position . ')' : '');
    }
}
