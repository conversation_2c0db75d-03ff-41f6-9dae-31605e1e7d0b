<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Contact;
use App\Models\Doctor;
use App\Models\Patient;
use App\Models\Provider;
use Illuminate\Http\Request;

class ContactController extends Controller
{
    public function index()
    {
        $contacts = Contact::latest()->paginate(20);
        return view('Admin.CRUDS.contact.index', compact('contacts'));
    }

    public function show($id)
    {
        $contact = Contact::findOrFail($id);
        return view('Admin.CRUDS.contact.show', compact('contact'));
    }

    public function edit($id)
    {
        $contact = Contact::findOrFail($id);
        return view('Admin.CRUDS.contact.edit', compact('contact'));
    }

    public function update(Request $request, $id)
    {
        $contact = Contact::findOrFail($id);
        $request->validate([
            'response' => 'required|string',
        ]);
        $contact->response = $request->response;
        $contact->is_read = true;
        $contact->save();

        //send notification to patient
        if($contact->user_type == Patient::class){
            run_push_notification($contact->user_id, 'patient', [
                'title' => 'Response from Admin',
                'body' => "Have a new response from Admin",
            ]);
        }else if($contact->user_type == Provider::class){
            run_push_notification($contact->user_id, 'provider', [
                'title' => 'Response from Admin',
                'body' => "Have a new response from Admin",
            ]);
        }else if($contact->user_type == Doctor::class){
            run_push_notification($contact->user_id, 'doctor', [
                'title' => 'Response from Admin',
                'body' => "Have a new response from Admin",
            ]);
        }

        return redirect()->route('admin.contact.index')->with('success', 'Response sent successfully.');
    }

    public function markAsRead($id)
    {
        $contact = Contact::findOrFail($id);
        $contact->is_read = true;
        $contact->save();
        return redirect()->back()->with('success', 'Marked as read.');
    }
}
