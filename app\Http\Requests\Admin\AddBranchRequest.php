<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class AddBranchRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' =>'required',
            'address' =>'required',
            'phone' =>'required|string|max:255',
            'governorate_id' =>'required|string|max:255',
            'city_id' =>'nullable|string|max:255',
            'longitude' =>'nullable|numeric',
            'latitude' =>'nullable|numeric',
            "about" => 'required',
            'whatsapp' => ["required","unique:doctor_branches,whatapp"],
            'tel' => ["required","unique:doctor_branches,tel"],
            'email' => ["required",'email',"unique:doctor_branches,email"],
        ];
    }
}
