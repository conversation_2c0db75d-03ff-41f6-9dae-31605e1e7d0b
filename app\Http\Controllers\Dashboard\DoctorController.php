<?php

namespace App\Http\Controllers\Dashboard;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\DoctorRequest;
use App\Http\Requests\FileRequest;
use App\Http\Resources\DoctorResource;
use App\Http\Traits\Api_Trait;
use App\Imports\Dashboard\DoctorImport;
use App\Models\Doctor;
use App\Models\ImportedFile;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;

class DoctorController extends Controller
{
    use Api_Trait;

    /**
     * Display a listing of doctors
     */
    public function index(Request $request)
    {
        try {
            $query = Doctor::with(['sub_specialization','specialization', 'governorate', 'city']);
            // Filter by specialization
            if ($request->has('specialization_id')) {
                $query->where('specialization_id', $request->specialization_id);
            }

            // Filter by specialization
            if ($request->has('sub_specialization_id')) {
                $query->where('sub_specialization_id', $request->sub_specialization_id);
            }

            // Filter by governorate
            if ($request->has('governorate_id')) {
                $query->where('governorate_id', $request->governorate_id);
            }

            // Filter by city
            if ($request->has('city_id')) {
                $query->where('city_id', $request->city_id);
            }

            // Filter by gender
            if ($request->has('gender')) {
                $query->where('gender', $request->gender);
            }

            // Filter by doctor type
            if ($request->has('doctor_type')) {
                $query->where('doctor_type', $request->doctor_type);
            }

            // Search by name or nickname
            if ($request->has('search')) {
                $search = $request->search;
                $query->where(function ($q) use ($search) {
                    $q->where('name', 'like', "%{$search}%")
                        ->orWhere('nickname->ar', 'like', "%{$search}%")
                        ->orWhere('nickname->en', 'like', "%{$search}%")
                        ->orWhere('code', 'like', "%{$search}%");
                });
            }

            $doctors = $query->paginate($request->get('per_page', 15));

            return $this->returnData(
                DoctorResource::collection($doctors)->response()->getData(true),
                [helperTrans('api.Doctors retrieved successfully')],
                200
            );
        } catch (\Exception $e) {
            return $this->returnError([
                'message' => 'Failed to retrieve doctors: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Store a newly created doctor
     */
    public function store(DoctorRequest $request)
    {
        $doctorData = [
            'code' => $request->code,
            'nickname' => json_encode([
                'ar' => $request->nickname_ar,
                'en' => $request->nickname_en
            ]),
            'name' => $request->name,
            'gender' => $request->gender,
            'specialization_id' => $request->specialization_id,
            'governorate_id' => $request->governorate_id,
            'city_id' => $request->city_id,
            'doctor_type' => 'provider',
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
            'experience_years' => $request->experience_years,
            'location' => $request->location_ar || $request->location_en ? json_encode([
                'ar' => $request->location_ar,
                'en' => $request->location_en
            ]) : null,
            'about' => $request->about_ar || $request->about_en ? json_encode([
                'ar' => $request->about_ar,
                'en' => $request->about_en
            ]) : null,
            'phone' => $request->phone,
            'email' => $request->email,
        ];
        $doctor = Doctor::create($doctorData);

        return $this->returnData(
            DoctorResource::make($doctor),
            [helperTrans('api.Doctor created successfully')],
            201
        );
    }

    /**
     * Display the specified doctor
     */
    public function show(Doctor $doctor)
    {
        try {
            $doctor->load(['sub_specialization','specialization', 'governorate', 'city']);

            return $this->returnData(
                DoctorResource::make($doctor),
                [helperTrans('api.Doctor retrieved successfully')],
                200
            );
        } catch (\Exception $e) {
            return $this->returnError([
                'message' => 'Failed to retrieve doctor: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Update the specified doctor
     */
    public function update(DoctorRequest $request, Doctor $doctor)
    {
        $doctorData = [
            'code' => $request->code,
            'nickname' => json_encode([
                'ar' => $request->nickname_ar,
                'en' => $request->nickname_en
            ]),
            'name' => json_encode([
                'ar' => $request->name_ar,
                'en' => $request->name_en
            ]),
            'gender' => $request->gender,
            'specialization_id' => $request->specialization_id,
            'sub_specialization_id' => $request->sub_specialization_id,
            'doctor_level_id' => $request->doctor_level_id,
            'governorate_id' => $request->governorate_id,
            'city_id' => $request->city_id,
            'doctor_type' => 'provider',
            'longitude' => $request->longitude,
            'latitude' => $request->latitude,
            'experience_years' => $request->experience_years,
            'location' => $request->location_ar || $request->location_en ? json_encode([
                'ar' => $request->location_ar,
                'en' => $request->location_en
            ]) : null,
            'about' => $request->about_ar || $request->about_en ? json_encode([
                'ar' => $request->about_ar,
                'en' => $request->about_en
            ]) : null,
            'phone' => $request->phone,
            'email' => $request->email,
        ];

        $doctor->update($doctorData);

        return $this->returnData(
            DoctorResource::make($doctor->fresh()),
            [helperTrans('api.Doctor updated successfully')],
            200
        );
    }

    /**
     * Remove the specified doctor
     */
    public function destroy(Doctor $doctor)
    {

        $doctor->delete();

        return $this->returnData(
            null,
            [helperTrans('api.Doctor deleted successfully')],
            200
        );
    }

    /**
     * Import doctors from Excel file
     */
    public function import(FileRequest $request)
    {
        try {
            // Save file to imported_files table before import
            $importedFile = ImportedFile::saveFile(
                $request->file('file'),
                auth('sanctum')->id()
            );

            $import = new DoctorImport($importedFile->id);
            Excel::import($import, $importedFile->path);

            $response = [
                'success_count' => $import->getSuccessCount(),
                'error_count' => $import->getErrorCount(),
                'errors' => $import->getErrors(),
                'imported_file_id' => $importedFile->id,
                'filename' => $importedFile->file_name
            ];

            if ($import->getErrorCount() > 0) {
                return $this->returnData([
                    $response,
                    helperTrans('api.Doctors Template Has Errors'),
                    "errors Count: {$import->getErrorCount()}"
                ], 422);
            }

            return $this->returnData($response, [
                helperTrans('api.Doctors imported successfully'),
                "Successfully imported {$import->getSuccessCount()} doctors"
            ], 200);
        } catch (\Exception $e) {
            return $this->returnError([
                'message' => 'Doctor import failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
