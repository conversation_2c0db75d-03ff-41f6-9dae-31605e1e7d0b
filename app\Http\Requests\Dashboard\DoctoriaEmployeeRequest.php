<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Validation\Rule;

class DoctoriaEmployeeRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => ['required', 'string', 'max:255', 'min:2'],
            'phone' => ['nullable', 'string', 'max:20', 'regex:/^[\+]?[0-9\s\-\(\)]+$/'],
            'email' => ['nullable', 'email', 'max:255'],
            'position' => ['nullable', 'string', 'max:255'],
        ];

        // Handle unique validation for create vs update
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            // For update requests
            $employeeId = $this->route('doctoria_employee');
            
            $rules['email'][] = Rule::unique('doctoria_employees', 'email')->ignore($employeeId);
            $rules['phone'][] = Rule::unique('doctoria_employees', 'phone')->ignore($employeeId);
        } else {
            // For create requests
            $rules['email'][] = 'unique:doctoria_employees,email';
            $rules['phone'][] = 'unique:doctoria_employees,phone';
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Employee name is required',
            'name.string' => 'Employee name must be a valid string',
            'name.max' => 'Employee name cannot exceed 255 characters',
            'name.min' => 'Employee name must be at least 2 characters',
            
            'phone.string' => 'Phone number must be a valid string',
            'phone.max' => 'Phone number cannot exceed 20 characters',
            'phone.regex' => 'Phone number format is invalid',
            'phone.unique' => 'This phone number is already registered',
            
            'email.email' => 'Email must be a valid email address',
            'email.max' => 'Email cannot exceed 255 characters',
            'email.unique' => 'This email address is already registered',
            
            'position.string' => 'Position must be a valid string',
            'position.max' => 'Position cannot exceed 255 characters',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => 'employee name',
            'phone' => 'phone number',
            'email' => 'email address',
            'position' => 'position',
        ];
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Clean and format phone number
        if ($this->has('phone') && $this->phone) {
            $this->merge([
                'phone' => preg_replace('/[^\+0-9]/', '', $this->phone)
            ]);
        }

        // Clean and format email
        if ($this->has('email') && $this->email) {
            $this->merge([
                'email' => strtolower(trim($this->email))
            ]);
        }

        // Clean and format name
        if ($this->has('name') && $this->name) {
            $this->merge([
                'name' => trim($this->name)
            ]);
        }

        // Clean and format position
        if ($this->has('position') && $this->position) {
            $this->merge([
                'position' => trim($this->position)
            ]);
        }
    }
}
