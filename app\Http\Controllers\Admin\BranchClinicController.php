<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BranchClinic;
use App\Models\DoctorClinic;
use App\Models\ProviderBranch;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class BranchClinicController extends Controller
{
    public function index(Request $request, $branchId)
    {
        $branch = ProviderBranch::findOrFail($branchId);
        if ($request->ajax()) {
            $branchClinics = BranchClinic::with('clinic')
                ->where('provider_branch_id', $branchId);

            return DataTables::of($branchClinics)
                ->addColumn('action', function($branchClinic) {
                    return '<div class="dropdown d-inline-block">
                    <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ri-more-fill align-middle"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                    <li><a class="dropdown-item" href="#"><i class="ri-pencil-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.doctors') . '</a></li>
                        <li>
                           <a class="remove-branch-clinic-btn" href="javascript:void(0)" data-id="' . $branchClinic->id . '">
                                <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.delete') . '
                            </a>
                        </li>
                    </ul>
                </div>';
                    // return  '<a class="remove-branch-clinic-btn" href="javascript:void(0)" data-id="' . $branchClinic->id . '">
                    //             <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.delete') . '
                    //         </a>';
                })
                ->addColumn('name_ar', function($branchClinic) {
                    return $branchClinic->clinic ? $branchClinic->clinic->getTranslation('name', 'ar') : '';
                })
                ->addColumn('name_en', function($branchClinic) {
                    return $branchClinic->clinic ? $branchClinic->clinic->getTranslation('name', 'en') : '';
                })
                ->addColumn('notes', function($branchClinic) {
                    return $branchClinic->clinic ? $branchClinic->clinic->notes : '';
                })
                ->addColumn('id', function($branchClinic) {
                    return $branchClinic->clinic ? $branchClinic->clinic->id : '';
                })
                ->rawColumns(['name_ar', 'name_en', 'notes', 'id','action'])
                ->make(true);
        }
        // For non-ajax requests, show the view as before
        $branchClinics = BranchClinic::with('clinic')
            ->where('provider_branch_id', $branchId)
            ->get();
        $clinics = $branchClinics->pluck('clinic');

        return view('Admin.CRUDS.branch_clinics.index', compact('clinics', 'branchId','branch'));
    }

    public function store(Request $request, $branchId)
    {
        // dd([$request->all(),$branchId]);
        $request->validate([
            'clinic_id' => 'required|exists:clinics,id',
        ]);


        BranchClinic::create([
            'provider_branch_id' => $branchId,
            'clinic_id' => $request->clinic_id,
        ]);
        return redirect()->route('admin.branch_clinics.index', $branchId)
            ->with('success', helperTrans('admin.Clinic added to branch successfully.'));
    }

    public function destroy($id)
    {
        $branchClinic = BranchClinic::findOrFail($id);
        $branchClinic->delete();
        return response()->json([
            'status' => true,
            'message' => helperTrans('admin.Clinic deleted successfully.')
        ]);
    }
}
