<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\VoucherRequest;
use App\Http\Traits\ResponseTrait;
use App\Models\Voucher;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\DataTables;

class VoucherController extends Controller
{
    //

    use  ResponseTrait;

    public function index(Request $request)
    {

        if ($request->ajax()) {
            $admins = Voucher::query()->latest();
            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {

                    $edit = '';
                    $delete = '';


                    return '
                            <button ' . $edit . '  class="editBtn btn rounded-pill btn-primary waves-effect waves-light"
                                    data-id="' . $admin->id . '"
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-edit"></i>
                                </span>
                            </span>
                            </button>
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $admin->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';


                })

                ->editColumn('phone', function ($row) {
                    return $row->phone != null ? $row->phone : 'Not Used';
                })

                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);


        }
        return view('Admin.CRUDS.voucher.index');
    }


    public function create()
    {
        return view('Admin.CRUDS.voucher.parts.create');
    }

    public function store(VoucherRequest $request)
    {
        $data = $request->validationData();
        Voucher::create($data);
        return $this->addResponse();

    }


    public function show($id)
    {


        //
    }


    public function edit($id )
    {
        $row=Voucher::findOrFail($id);
        return view('Admin.CRUDS.voucher.parts.edit', compact('row'));

    }

    public function update(VoucherRequest $request, $id )
    {

        $row=Voucher::findOrFail($id);
        $data = $request->validationData();
        $row->update($data);
        return $this->updateResponse();

    }


    public function destroy($id)
    {
        $row = Voucher::findOrFail($id);
        $row->delete();
        return $this->deleteResponse();
    }//end fun

}
