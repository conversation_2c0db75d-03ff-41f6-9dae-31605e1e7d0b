<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ServiceRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'sub_category_id' => ['required', 'exists:sub_categories,id'],
            'name_ar' => ['required', 'string', 'max:255'],
            'name_en' => ['required', 'string', 'max:255'],
            'description_ar' => ['nullable', 'string'],
            'description_en' => ['nullable', 'string'],
            'code' => ['required', 'string', 'max:50', 'unique:services,code'],
            'price' => ['required', 'numeric', 'min:0'],
            'is_active' => ['boolean'],
        ];

        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            $rules['code'] = [
                'required',
                'string',
                'max:50',
                Rule::unique('services', 'code')->ignore($this->route('service'))
            ];
        }

        return $rules;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'sub_category_id' => trans('dashboard.services.sub_category'),
            'name_ar' => trans('dashboard.services.name_ar'),
            'name_en' => trans('dashboard.services.name_en'),
            'description_ar' => trans('dashboard.services.description_ar'),
            'description_en' => trans('dashboard.services.description_en'),
            'code' => trans('dashboard.services.code'),
            'price' => trans('dashboard.services.price'),
        ];
    }
}
