<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Resources\Json\JsonResource;

class RelativeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'card_id' => $this->card_id,
            'name' => $this->name,
            'phone' => $this->phone,
            'status' => $this->status,
            'external_id' => $this->external_id,
            'national_id' => $this->national_id,
            'gender' => $this->gender,
            'is_smoking' => $this->is_smoking,
            'is_alcoholic' => $this->is_alcoholic,
            'join_subscribe' => $this->join_subscribe,
            'birth_date' => $this->birth_date,
            'address' => $this->address,
            'type' => $this->type,

            // Relationships
            'relationship' => $this->relationship ? RelationshipResource::make($this->relationship) : null,
            'patient' => $this->patient ? PatientResource::make($this->patient) : null,
            'nationality' => $this->nationality ? NationalityResource::make($this->nationality) : null,
            'country' => $this->country ? CountryResource::make($this->country) : null,
            'city' => $this->city ? CityResource::make($this->city) : null,
            'area' => $this->area ? AreaResource::make($this->area) : null,
            'governorate' => $this->governorate ? GovernorateResource::make($this->governorate) : null,

            // Additional computed fields
            'age' => $this->birth_date ? now()->diffInYears($this->birth_date) : null,
            'is_active' => $this->status === 'active',
            'has_phone' => !empty($this->phone),
            'has_national_id' => !empty($this->national_id),
            'has_external_id' => !empty($this->external_id),
            'location_display' => $this->getLocationDisplay(),
            'full_info_display' => $this->getFullInfoDisplay(),

            // Timestamps
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }

    /**
     * Get location display string
     */
    private function getLocationDisplay()
    {
        $location = [];
        
        if ($this->area && $this->area->name) {
            $location[] = $this->area->name;
        }
        
        if ($this->city && $this->city->name) {
            $location[] = $this->city->name;
        }
        
        if ($this->governorate && $this->governorate->name) {
            $location[] = $this->governorate->name;
        }
        
        return implode(', ', $location) ?: null;
    }

    /**
     * Get full info display string
     */
    private function getFullInfoDisplay()
    {
        $info = [];
        
        if ($this->name) {
            $info[] = $this->name;
        }
        
        if ($this->relationship && $this->relationship->name) {
            $info[] = "({$this->relationship->name})";
        }
        
        if ($this->patient && $this->patient->name) {
            $info[] = "- Patient: {$this->patient->name}";
        }
        
        return implode(' ', $info) ?: null;
    }
}
