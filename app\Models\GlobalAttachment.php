<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class GlobalAttachment extends Model
{
    use HasFactory;
    protected $table = 'global_attachments';
    protected $fillable = [
        'attachment_id',
        'attachable_id',
        'attachable_type',
    ];
    protected $guarded = [];
    public function attachment()
    {
        return $this->belongsTo(Attachment::class);
    }
    public function attachable()
    {
        return $this->morphTo();
    }
}
