<?php
namespace App\Enums\Branch;
enum BranchType: string {
    case Master = 'master';
    case SUBBRANCH = 'sub_branch';
    public static function status($value): string {
        if ($value == self::Master) {
            return '<span class="badge badge-success">' . trans('branch.master') . '</span>';
        } elseif ($value == self::SUBBRANCH) {
            return '<span class="badge badge-warning">' . trans('branch.sub_branch') . '</span>';
        } else {
            return '<span class="badge badge-default">' . trans('general.not_status_assigned') . '</span>';
        }
    }
}
