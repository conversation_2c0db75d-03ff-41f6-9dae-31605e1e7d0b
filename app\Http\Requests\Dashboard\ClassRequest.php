<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Validation\Rule;

class ClassRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'notes' => ['nullable', 'string', 'max:1000'],
        ];

        // Handle unique name validation for create vs update
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            // For update requests
            $rules['name'] = [
                'required',
                'string',
                'max:255',
                Rule::unique('classes', 'name')->ignore($this->route('class'))
            ];
        } else {
            // For create requests
            $rules['name'] = ['required', 'string', 'max:255', 'unique:classes,name'];
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Class name is required',
            'name.unique' => 'This class name already exists',
            'name.max' => 'Class name cannot exceed 255 characters',
            'notes.max' => 'Notes cannot exceed 1000 characters',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => 'class name',
            'notes' => 'notes',
        ];
    }
}
