<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;

class PatientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {

        $patientId = auth('patient')->id();

        return [
            'name' => 'required|string|max:255',
            'nickname' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255|unique:patients,email,' . $patientId,
            'phone' => 'nullable|string|max:20|unique:patients,phone,' . $patientId . '|unique:doctors,phone',
            'gender' => 'nullable|in:male,female',
            'postcode' => 'nullable|string|max:20',
            'refer_code' => 'nullable|string|max:255|unique:patients,refer_code,' . $patientId,
            'address' => 'nullable|string',
            'location' => 'nullable|string|max:300',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'nationality_id' => 'nullable|exists:nationalities,id',
            'city_id' => 'nullable|exists:cities,id',
            'marital_status' => 'nullable|string|max:255',
            'occupation' => 'nullable|string|max:255',
            'n_children' => 'nullable|integer|min:0',
            'residence' => 'nullable|string|max:255',
            'is_smoking' => 'nullable|boolean',
            'is_alcoholic' => 'nullable|boolean',
            'athlete' => 'nullable|string|max:255',
            'date_of_birth' => 'nullable|date',
            'id_number' => 'nullable|string|min:14|max:255|unique:patients,id_number,' . $patientId,
            'country_id' => 'nullable|exists:countries,id',
            'governorate_id' => 'nullable|exists:governorates,id',
        ];
    }
}
