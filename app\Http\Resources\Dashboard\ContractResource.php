<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class ContractResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'admin_id' => $this->admin_id,
            'provider_id' => $this->provider_id,
            'official_full_name' => $this->official_full_name,
            'commercial_registration_number' => $this->commercial_registration_number,
            'national_id' => $this->national_id,
            'tax_card_number' => $this->tax_card_number,
            'tax_card_due_date' => $this->tax_card_due_date,
            'private_phone_number' => $this->private_phone_number,
            'private_email' => $this->private_email,
            'representative_full_name' => $this->representative_full_name,
            'position' => $this->position,
            'phone' => $this->phone,
            'email' => $this->email,
            'facebook_link' => $this->facebook_link,
            'instagram_link' => $this->instagram_link,
            'contract_start_date' => Carbon::parse($this->contract_start_date)->toDateString(),
            'contract_end_date' => Carbon::parse($this->contract_end_date)->toDateString(),
            'payment_method_id' => $this->payment_method_id,
            'claims_due_date' => $this->claims_due_date,
            'admin_fees' => $this->admin_fees,
            'notes' => $this->notes,
            'provider' => $this->provider->only('id', 'name'),
            'admin' => $this->admin->only('id', 'name'),
            'need_approve' => $this->need_approve,
            'attachments' => ContractAttachmentResource::collection($this->attachments),
            'payment_method' => $this->paymentMethod?->only('id', 'name'),
            'insurance_companies' => InsuranceCompanyResource::collection($this->insuranceCompanies),
            'other_places' => OtherPlacesResource::collection($this->otherPlaces),
            'delegates' => DelegateManagerResource::collection($this->delegateManagers),
            'banks' => BankResource::collection($this->banks),
            'wallets' => WalletResource::collection($this->wallets),
            'instapays' => InstapayResource::collection($this->instapays),
            'created_at' => Carbon::parse($this->created_at)->toDateString(),
            'updated_at' => Carbon::parse($this->updated_at)->toDateString(),
        ];
    }
}
