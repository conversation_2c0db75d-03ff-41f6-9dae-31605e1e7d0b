<?php
namespace App\Http\Controllers\Admin\GeneralMainSetting;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Traits\ResponseTrait;
use App\Models\City;
class LocationController extends Controller {
    use  ResponseTrait;
    public function getCities($governorateId) {
        $cities = City::where('governorate_id', $governorateId)->get();
        return response()->json($cities);
    }
}
