<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PriceListService;
use App\Http\Resources\Dashboard\PriceListServiceResource;
use App\Http\Traits\Api_Trait;
use App\Http\Requests\Dashboard\PriceListServiceRequest;
use App\Models\Service;
use App\Models\PriceList;
use App\Http\Resources\Dashboard\PriceListResource;
use App\Models\PriceListServiceCategory;

class PriceListServiceController extends Controller
{
    use Api_Trait;

    public function index(Request $request)
    {
        $price_list_services = PriceListService::with('service')->where('price_list_id', $request->price_list_id)
            ->when($request->service_category_id, function ($query) use ($request) {
                $query->whereHas('service', function ($query) use ($request) {
                    $query->where('service_category_id', $request->service_category_id);
                });
            })
            ->when($request->service_sub_category_id, function ($query) use ($request) {
                $query->whereHas('service', function ($query) use ($request) {
                    $query->where('service_sub_category_id', $request->service_sub_category_id);
                });
            })->get()->map(function ($price_list_service) use ($request) {
                return [
                    'service_id' => $price_list_service->service->id,
                    'service_name' => $price_list_service->service->name,
                    'service_price' => $price_list_service->price + ($request->percentage / 100.0 * $price_list_service->price),
                    'service_category_id' => $price_list_service->service->service_category->id,
                    'service_category_name' => $price_list_service->service->service_category->name,
                    'service_sub_category_id' => $price_list_service->service->service_sub_category->id,
                    'service_sub_category_name' => $price_list_service->service->service_sub_category->name,
                ];
            });
        return $this->returnData($price_list_services, [helperTrans('api.Price List Service Data')], 200);
    }



    public function getServiceCategories(Request $request)
    {
        $price_list_service_categories = PriceListServiceCategory::with('service_category')->where('price_list_id', $request->price_list_id)->get()->map(function ($price_list_service_category) {
            return [
                'service_category_id' => $price_list_service_category->service_category->id,
                'service_category_name' => $price_list_service_category->service_category->name,
            ];
        })->unique('service_category_id')->values();
        return $this->returnData($price_list_service_categories, [helperTrans('api.Price List Service Data')], 200);
    }

    public function store(PriceListServiceRequest $request)
    {

        $price_list = PriceList::find($request->price_list_id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }

        foreach ($request->service_sub_category_ids as $service_sub_category_id) {
            $price_list_service_category = PriceListServiceCategory::firstOrcreate([
                'price_list_id' => $request->price_list_id,
                'service_category_id' => $request->service_category_id,
                'service_sub_category_id' => $service_sub_category_id,
            ]);
        }
        $service_ids = Service::whereIn('service_sub_category_id', $request->service_sub_category_ids)->pluck('id');

        $price_list->services()->sync($service_ids);


        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }

    public function update(PriceListServiceRequest $request, $id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }

        $updated_services = [];

        foreach ($request->validated()['services'] as $service_data) {
            $price_list_service = PriceListService::updateOrCreate(
                [
                    'price_list_id' => $id,
                    'service_id' => $service_data['id']
                ],
                [
                    'price' => $service_data['price']
                ]
            );

            $updated_services[] = PriceListServiceResource::make($price_list_service);
        }

        return $this->returnData($updated_services, [helperTrans('api.Price List Services Updated')], 200);
    }

    public function destroy(Request $request, $id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }
        foreach ($request->services as $service_data) {
            $price_list->services()->detach($service_data['id']);
        }
        return $this->returnData(null, [helperTrans('api.Price List Service Data')], 200);
    }
}
