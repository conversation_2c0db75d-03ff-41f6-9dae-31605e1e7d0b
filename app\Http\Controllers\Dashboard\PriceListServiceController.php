<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\PriceListService;
use App\Http\Resources\Dashboard\PriceListServiceResource;
use App\Http\Traits\Api_Trait;
use App\Http\Requests\Dashboard\PriceListServiceRequest;
use App\Models\Service;
use App\Models\PriceList;
use App\Http\Resources\Dashboard\PriceListResource;
use App\Http\Resources\Dashboard\ServiceResource;
use App\Http\Resources\Dashboard\PriceListServiceCategoryResource;
use App\Models\PriceListServiceCategory;
use App\Models\Provider;
use App\Models\ProviderCategoryServiceCategory;
use App\Models\ProviderService;
use Illuminate\Support\Facades\Validator;

class PriceListServiceController extends Controller
{
    use Api_Trait;

    public function index(Request $request)
    {
        $price_list_services = PriceListService::with(['service.service_category', 'service.service_sub_category'])
            ->where('price_list_id', $request->price_list_id)
            ->when($request->service_category_id, function ($query) use ($request) {
                $query->whereHas('service', function ($query) use ($request) {
                    $query->where('service_category_id', $request->service_category_id);
                });
            })
            ->orderBy('id', 'desc')->get();

        $grouped_services = $price_list_services->groupBy('service.service_sub_category_id')
            ->map(function ($services, $sub_category_id) use ($request) {
                $first_service = $services->first();
                return [
                    'service_sub_category_id' => $sub_category_id,
                    'service_sub_category_name' => $first_service->service->service_sub_category->name,
                    'service_category_id' => $first_service->service->service_category->id,
                    'service_category_name' => $first_service->service->service_category->name,
                    'services' => $services->map(function ($price_list_service) use ($request) {
                        return [
                            'service_id' => $price_list_service->service->id,
                            'service_name' => $price_list_service->service->name,
                            'service_price' => $price_list_service->price ?? 0,
                            //($request->percentage / 100.0 * $price_list_service->price),
                            "service_type" => $price_list_service->service->type,
                        ];
                    })->values()
                ];
            })->values();

        return $this->returnData($grouped_services, [helperTrans('api.Price List Service Data')], 200);
    }


    public function getServiceCategories(Request $request)
    {
        $special_service_category = null;

        if ($request->provider_id) {
            $provider_service = ProviderService::where('provider_id', $request->provider_id)->first();
            if (!$provider_service) {
                $provider = Provider::find($request->provider_id);
                $provider->provider_category_id;
                $provider_service_category = ProviderCategoryServiceCategory::where('provider_category_id', $provider->provider_category_id)->first();
                $special_service_category = $provider_service_category->service_category_id;
            }
        }

        $price_list_service_categories = PriceListServiceCategory::with('service_category', 'service_sub_category')
            ->where('price_list_id', $request->price_list_id)->when($special_service_category, function ($query) use ($special_service_category) {
                $query->where('service_category_id', $special_service_category);
            })->orderBy('id', 'desc')->get()->unique('service_category_id')->values();
        return $this->returnData(PriceListServiceCategoryResource::collection($price_list_service_categories), [helperTrans('api.Price List Service Data')], 200);
    }

    public function store(PriceListServiceRequest $request)
    {


        $price_list = PriceList::find($request->price_list_id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }

        foreach ($request->service_sub_category_ids as $service_sub_category_id) {
            $price_list_service_category = PriceListServiceCategory::firstOrcreate([
                'price_list_id' => $request->price_list_id,
                'service_category_id' => $request->service_category_id,
                'service_sub_category_id' => $service_sub_category_id,
            ]);
        }

        if ($request->service_ids && count($request->service_ids) > 0) {
            $services = Service::whereIn('id', $request->service_ids)->select('id', 'service_category_id', 'service_sub_category_id')->get();
        } else {
            $services = Service::whereIn('service_sub_category_id', $request->service_sub_category_ids)->select('id', 'service_category_id', 'service_sub_category_id')->get();
        }

        foreach ($services as $service) {
            $price_list_service = PriceListService::firstOrCreate([
                'price_list_id' => $request->price_list_id,
                'service_id' => $service->id,
                'service_category_id' => $service->service_category_id,
                'service_sub_category_id' => $service->service_sub_category_id,
            ]);
        }

        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }

    public function update(PriceListServiceRequest $request, $id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }

        $updated_services = [];

        foreach ($request->validated()['services'] as $service_data) {
            $price_list_service = PriceListService::updateOrCreate(
                [
                    'price_list_id' => $id,
                    'service_id' => $service_data['id']
                ],
                [
                    'price' => $service_data['price']
                ]
            );

            $updated_services[] = PriceListServiceResource::make($price_list_service);
        }

        return $this->returnData($updated_services, [helperTrans('api.Price List Services Updated')], 200);
    }

    public function destroy(Request $request, $id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }
        foreach ($request->services as $service_data) {
            $price_list->services()->detach($service_data['id']);
        }
        return $this->returnData(null, [helperTrans('services deleted successfully')], 200);
    }

    public function get_custom_services(Request $request)
    {
        // $validator = Validator::make($request->all(), [
        //     'service_sub_category_ids' => 'required|array',
        // ]);
        // if ($validator->fails()) {
        //     return $this->returnError($validator->errors()->first(), 422);
        // }
        if (count($request->service_sub_category_ids) > 1) {
            $services = collect([]);
        } else {
            $services = Service::with('service_category', 'service_sub_category')
                ->whereIn('service_sub_category_id', $request->service_sub_category_ids)
                ->get();
        }
        return $this->returnData(
            ServiceResource::collection($services),
            helperTrans('api.Service Data'),
            200
        );
    }

    public function dublicate_price_list_services(Request $request, $id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }
        $new_price_list = PriceList::create([
            'name' => [
                "ar" => $price_list->getTranslation('name', 'ar') . ' ' . helperTrans('api.Copy'),
                "en" => $price_list->getTranslation('name', 'en') . ' ' . helperTrans('api.Copy'),
            ],
            'year' => $price_list->year,
        ]);

        $price_list_services = PriceListService::where('price_list_id', $price_list->id)->get();

        foreach ($price_list_services as $row) {
            PriceListService::firstOrCreate([
                'price_list_id' => $new_price_list->id,
                'service_id' => $row->service_id,
                'price' => $row->price,
            ]);
        }


        $price_list_service_categories = PriceListServiceCategory::where('price_list_id', $price_list->id)->get();

        foreach ($price_list_service_categories as $row) {
            PriceListServiceCategory::firstOrCreate([
                'price_list_id' => $new_price_list->id,
                'service_category_id' => $row->service_category_id,
                'service_sub_category_id' => $row->service_sub_category_id,
            ]);
        }
        return $this->returnData(PriceListResource::make($new_price_list), [helperTrans('api.Price List Data')], 200);
    }
}
