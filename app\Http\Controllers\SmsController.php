<?php

namespace App\Http\Controllers;

use App\Services\SmsService;
use Illuminate\Http\Request;

class SmsController extends Controller
{
    protected $smsService;

    public function __construct(SmsService $smsService)
    {
        $this->smsService = $smsService;
    }

    public function sendSms(Request $request)
    {
        $validated = $request->validate([
            'phone_number' => 'required|string',
            'message' => 'required|string',
        ]);

        $phone_number = $validated['phone_number'];
        $message = $validated['message'];

        $result = $this->smsService->sendSms($phone_number, $message);

        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => 'SMS sent successfully!',
                'data' => $result['data'],
            ]);
        } else {
            return response()->json([
                'success' => false,
                'message' => $result['message'],
                'error' => $result['error'],
            ], 400);
        }
    }
}
