<?php

namespace App\Imports\Dashboard;

use App\Models\City;
use App\Models\Contract;
use App\Models\ContractBank;
use App\Models\ContractDelegate;
use App\Models\ContractInstapay;
use App\Models\ContractWallet;
use App\Models\Doctor;
use App\Models\DoctorLevel;
use App\Models\Governorate;
use App\Models\Provider;
use App\Models\ProviderBranchDoctor;
use App\Models\ProviderTime;
use App\Models\Specialization;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;

class ContractImport implements ToCollection, WithHeadingRow, WithValidation
{
    protected $errors = [];
    protected $successCount = 0;
    protected $errorCount = 0;
    protected $importedFileId;

    public function __construct(?int $importedFileId = null)
    {
        $this->importedFileId = $importedFileId;
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $rows)
    {
        foreach ($rows as $index => $row) {
            try {
                DB::beginTransaction();

                $this->processContractRow($row, $index + 2); // +2 because of header row and 0-based index

                DB::commit();
                $this->successCount++;
            } catch (\Exception $e) {
                DB::rollBack();
                $this->errorCount++;
                $this->errors[] = "Row " . ($index + 2) . ": " . $e->getMessage();
                Log::error("Doctor import error on row " . ($index + 2) . ": " . $e->getMessage());
            }
        }

        return [
            'success_count' => $this->successCount,
            'error_count' => $this->errorCount,
            'errors' => $this->errors
        ];
    }

    protected function processContractRow($row, $rowNumber)
    {
        // Validate required fields
        $this->validateRequiredFields($row, $rowNumber);

        // Check for duplicate code
        // $this->checkDuplicateCode($row['code'], $rowNumber);

        $provider = $this->getProviderById($row['provider_id']);

        $contract = $this->createContract($row, $provider);
        $this->delegateManagerContract($contract, $row);
        $this->contractBanks($contract, $row);
        $this->contractWallets($contract, $row);
        $this->contractInstapays($contract, $row);
    }
    protected function createContract($row, $provider)
    {
        return Contract::firstOrCreate([
            'provider_id' => $provider->id,
            'official_full_name' => $row['official_full_name'],
        ], [
            'commercial_registration_number' => $row['commercial_registration_number'],
            'national_id' => $row['national_id'],
            'tax_card_number' => $row['tax_card_number'],
            'tax_card_due_date' => $row['tax_card_due_date'],
            'private_phone_number' => $row['private_phone_number'],
            'private_email' => $row['private_email'],
            'representative_full_name' => $row['representative_full_name'],
            'position' => $row['position'],
            'phone' => $row['phone'],
            'email' => $row['email'],
            'facebook_link' => $row['facebook_link'],
            'instagram_link' => $row['instagram_link'],
            'contract_start_date' => $row['contract_start_date'],
            'contract_end_date' => $row['contract_end_date'],
            'payment_method_id' => $row['payment_method_id'],
            'claims_due_date' => $row['claims_due_date'],
            'admin_fees' => $row['admin_fees'],
            'notes' => $row['notes'],
            'imported_file_id' => $this->importedFileId,
        ]);
    }
    protected function delegateManagerContract($contarct, $row)
    {
        return ContractDelegate::firstOrCreate([
            'contract_id' => $contarct->id,
            'delegate_manager_id' => $row['delegate_manager_id'],
        ], [
            'imported_file_id' => $this->importedFileId,
        ]);
    }
   
    protected function contractBanks($contarct, $row)
    {
        return ContractBank::firstOrCreate([
            'contract_id' => $contarct->id,
            'bank_name' => $row['bank_name'],
        ], [
            'bank_account_number' => $row['bank_account_number'],
            'bank_iban' => $row['bank_iban'],
            'bank_swift_code' => $row['bank_swift_code'],
            'bank_branch_bank' => $row['bank_branch_bank'],
            'bank_mobile_number' => $row['bank_mobile_number'],
            'imported_file_id' => $this->importedFileId,
        ]);
    }
    protected function contractWallets($contarct, $row)
    {
        return ContractWallet::firstOrCreate([
            'contract_id' => $contarct->id,
            'wallet_name' => $row['wallet_name'],
        ], [
            'wallet_mobile_number' => $row['wallet_mobile_number'],
            'wallet_cheque_name' => $row['wallet_cheque_name'],
            'imported_file_id' => $this->importedFileId,
        ]);
    }
    protected function contractInstapays($contarct, $row)
    {
        return ContractInstapay::firstOrCreate([
            'contract_id' => $contarct->id,
            'instapay_mobile_number' => $row['instapay_mobile_number'],
        ], [
            'instapay_username' => $row['instapay_username'],
            'cheque_name' => $row['cheque_name'],
            'imported_file_id' => $this->importedFileId,
        ]);
    }
    protected function validateRequiredFields($row, $rowNumber)
    {
        $requiredFields = [
            'provider_id' => 'Provider ID',
            'official_full_name' => 'Official Full Name',
            'contract_start_date' => 'Contract Start Date',
            'contract_end_date' => 'Contract End Date',
            'payment_method_id' => 'Payment Method ID',
        ];

        foreach ($requiredFields as $field => $label) {
            if (empty($row[$field])) {
                throw new \Exception("$label is required");
            }
        }
    }

    protected function getProviderById($provider_id)
    {
        $provider = Provider::find($provider_id);

        if (!$provider) {
            throw new \Exception("Provider with ID '$provider_id' not found");
        }

        return $provider;
    }



    public function rules(): array
    {
        return [
            '*.provider_id' => 'required|integer|exists:providers,id',
            '*.official_full_name' => 'required|string|max:255',
            '*.contract_start_date' => 'required|date',
            '*.contract_end_date' => 'required|date',
            '*.payment_method_id' => 'required|integer|exists:payment_methods,id',
            '*.delegate_manager_id' => 'required|integer|exists:delegate_managers,id',
            '*.bank_name' => 'nullable|string|max:255',
            '*.bank_account_number' => 'nullable|string|max:255',
            '*.bank_iban' => 'nullable|string|max:255',
            '*.bank_swift_code' => 'nullable|string|max:255',
            '*.bank_branch_bank' => 'nullable|string|max:255',
            '*.bank_mobile_number' => 'nullable|string|max:255',
            '*.wallet_name' => 'nullable|string|max:255',
            '*.wallet_mobile_number' => 'nullable|string|max:255',
            '*.wallet_cheque_name' => 'nullable|string|max:255',
            '*.instapay_mobile_number' => 'nullable|string|max:255',
            '*.instapay_username' => 'nullable|string|max:255',
            '*.cheque_name' => 'nullable|string|max:255',
            '*.commercial_registration_number' => 'nullable|string|max:255',
            '*.national_id' => 'nullable|string|max:255',
            '*.tax_card_number' => 'nullable|string|max:255',
            '*.tax_card_due_date' => 'nullable|date',
        ];
    }

    public function getErrors()
    {
        return $this->errors;
    }

    public function getSuccessCount()
    {
        return $this->successCount;
    }

    public function getErrorCount()
    {
        return $this->errorCount;
    }
}
