<?php

namespace App\Http\Controllers\Api\V1\Setting;

use App\Http\Resources\DoctorClinicResource;
use App\Http\Resources\ProviderTimeResource;
use App\Models\Doctor;
use App\Models\Booking;
use App\Models\MainService;
use Illuminate\Http\Request;
use App\Http\Traits\Api_Trait;
use App\Models\ProviderCategory;
use App\Http\Controllers\Controller;
use App\Http\Resources\DoctorBranchResource;
use App\Http\Resources\DoctorDetailsResource;
use App\Http\Resources\DoctorResource;

use App\Http\Resources\BookingResource;
use App\Http\Resources\DoctorLessResource;
use Illuminate\Support\Facades\Validator;

use App\Http\Resources\DoctorReviewsResource;
use App\Http\Resources\DoctorStatisticsResource;
use App\Models\Category;
use App\Models\City;
use App\Models\DoctorBranch;
use App\Models\Governorate;
use App\Models\ProviderTime;
use Google\Service\Analytics\Goal;

class DoctorController extends Controller
{
    use Api_Trait;

    public function doctors(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'doctor_field' => 'required|in:online,offline,home',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        if ($request->doctor_field == 'online') {
            // dd($request->all());

            //get specialized doctors which have online service

            $doctors = Doctor::where('status', 1)->where('doctor_type', 'specialized')
                ->whereNotNull('service_price_online')->where('service_price_online', '!=', 0);

            $other_doctors = Doctor::where('status', 1)->where('doctor_type', 'specialized')
                ->whereNotNull('service_price_online')->where('service_price_online', '!=', 0);

            if ($request->specialization_id) {
                $specialization_id   = $request->input('specialization_id');
                $doctors->where('specialization_id', $specialization_id);
                $other_doctors->where('specialization_id', $specialization_id);
            }

            if ($request->gender) {
                $gender  = $request->input('gender');
                $doctors->where('gender', $gender);
                $other_doctors->where('gender', $gender);
            }

            if ($request->governorate_id && $request->city_id) {

                $governorate_id  = $request->input('governorate_id');
                $city_id  = $request->input('city_id');

                $doctors->where('governorate_id', $governorate_id)->orderByRaw("CASE WHEN city_id = ? THEN 0 ELSE 1 END", [$city_id]);

                $other_doctors->where('governorate_id', '!=', $governorate_id);
            } else if ($request->governorate_id) {

                $governorate_id  = $request->input('governorate_id');

                $doctors->where('governorate_id', $governorate_id);

                $other_doctors->where('governorate_id', '!=', $governorate_id);
            } else if ($request->city_id) {

                $city_id  = $request->input('city_id');
                $city = City::where('id', $city_id)->first();

                $governorate_id  = $city->governorate_id;

                $doctors->where('governorate_id', $governorate_id)->orderByRaw("CASE WHEN city_id = ? THEN 0 ELSE 1 END", [$city_id]);

                $other_doctors->where('governorate_id', '!=', $governorate_id);
            }


            // if ($request->has('sortBy')) {
            //     $sortBy = $request->input('sortBy');
            //     switch ($sortBy) {
            //         case 'price_low':
            //             $doctors->orderBy('service_price_online');
            //             break;
            //         case 'price_high':
            //             $doctors->orderBy('service_price_online', 'desc');
            //             break;
            //         case 'best_rating':
            //             $doctors->orderBy('total_rate', 'desc');
            //             break;
            //     }
            // }
            return response()->json(
                [
                    'data' => DoctorResource::collection($doctors->get()),
                    'other_doctors' => $doctors != $other_doctors ? DoctorResource::collection($other_doctors->get()) : [],
                    "message" => "Doctors data",
                    "status" => 200
                ],
                200
            );
        }

        if ($request->doctor_field == 'offline') {

            //get specialized doctors which have offline service

            $doctors = Doctor::where('status', 1)->where('doctor_type', '!=','general');


            if ($request->specialization_id) {
                $specialization_id   = $request->input('specialization_id');
                $doctors->where('specialization_id', $specialization_id);
            }

            if ($request->gender) {
                $gender  = $request->input('gender');
                $doctors->where('gender', $gender);
            }

            if ($request->governorate_id && $request->city_id) {

                $governorate_id  = $request->input('governorate_id');
                $city_id  = $request->input('city_id');

                $doctors->where('governorate_id', $governorate_id)->orderByRaw("CASE WHEN city_id = ? THEN 0 ELSE 1 END", [$city_id]);
            } else if ($request->governorate_id) {

                $governorate_id  = $request->input('governorate_id');

                $doctors->where('governorate_id', $governorate_id);
            } else if ($request->city_id) {

                $city_id  = $request->input('city_id');
                $city = City::where('id', $city_id)->first();

                $governorate_id  = $city->governorate_id;

                $doctors->where('governorate_id', $governorate_id)->orderByRaw("CASE WHEN city_id = ? THEN 0 ELSE 1 END", [$city_id]);
            }

            return response()->json(
                [
                    'data' => DoctorResource::collection($doctors->get()),
                    'other_doctors' => collect([]),
                    "message" => "Doctors data",
                    "status" => 200
                ],
                200
            );
        }
        if ($request->doctor_field == 'home') {

            //get specialized doctors which have offline service

            $doctors = Doctor::where('status', 1)->where('doctor_type', 'specialized')->whereNotNull('service_price_home')->where('service_price_home', '!=', 0);


            if ($request->specialization_id) {
                $specialization_id   = $request->input('specialization_id');
                $doctors->where('specialization_id', $specialization_id);
            }

            if ($request->gender) {
                $gender  = $request->input('gender');
                $doctors->where('gender', $gender);
            }

            if ($request->governorate_id && $request->city_id) {

                $governorate_id  = $request->input('governorate_id');
                $city_id  = $request->input('city_id');

                $doctors->where('governorate_id', $governorate_id)->orderByRaw("CASE WHEN city_id = ? THEN 0 ELSE 1 END", [$city_id]);
            } else if ($request->governorate_id) {

                $governorate_id  = $request->input('governorate_id');

                $doctors->where('governorate_id', $governorate_id);
            } else if ($request->city_id) {

                $city_id  = $request->input('city_id');
                $city = City::where('id', $city_id)->first();

                $governorate_id  = $city->governorate_id;

                $doctors->where('governorate_id', $governorate_id)->orderByRaw("CASE WHEN city_id = ? THEN 0 ELSE 1 END", [$city_id]);
            }

            return response()->json(
                [
                    'data' => DoctorResource::collection($doctors->get()),
                    'other_doctors' => collect([]),
                    "message" => "Doctors data",
                    "status" => 200
                ],
                200
            );
        }

        // $hasFilters = $request->hasAny(['city_id', 'government_id', 'gender', 'doctor_field', 'specialization_id', 'sortBy']);

        // // Start building the query
        // $doctors = Doctor::where('status', 1)->where('doctor_type', 'specialized');
        // $other_doctors = Doctor::where('status', 1)->where('doctor_type', 'specialized');

        // // if ($request->doctor_field == 'online') {
        // //     $doctors = Doctor::where('status', 1)->where('doctor_type', 'specialized')->where('is_online', 1);
        // //     ProviderTime::where([
        // //         'provider_type' => 'doctor',
        // //         'provider_id'   => $doctor_id,
        // //         'type' => 'online'
        // //     ])->count();
        // // } else if ($request->doctor_field == 'offline') {
        // //     $doctors = Doctor::where('status', 1)->where('doctor_type', 'specialized')->where('is_online', 0);
        // // } else if ($request->doctor_field == 'home') {
        // //     $doctors = Doctor::where('status', 1)->where('doctor_type', 'specialized');
        // // } else {
        // //     $doctors = Doctor::where('status', 1)->where('doctor_type', 'specialized');
        // // }


        // if ($hasFilters) {
        //     // Apply filters based on the request parameters
        //     if ($request->has('city_id')) {
        //         $city_id  = $request->input('city_id');
        //         $city = City::where('id', $city_id)->first();

        //         $doctors->where('city_id', $city_id);
        //         $other_doctors->where('governorate_id', $city->governorate_id)->where('city_id', '!=', $city_id);
        //     }
        //     if ($request->has('government_id')) {
        //         $government_id  = $request->input('government_id');
        //         $doctors->where('governorate_id', $government_id);
        //         $other_doctors->where('governorate_id', '!=', $government_id);
        //     }
        //     if ($request->has('gender')) {
        //         $gender  = $request->input('gender');
        //         $doctors->where('gender', $gender);
        //         $other_doctors->where('gender', '!=', $gender);
        //     }
        //     // if ($request->has('doctor_field')) {
        //     //     $doctor_field = $request->input('doctor_field');
        //     //     if ($doctor_field == 'online') {
        //     //         $doctors->where('service_price_online', '!=', 0)->whereNotNull('service_price_online');
        //     //         $other_doctors = collect([]);
        //     //     }

        //     //     // if ($doctor_field == 'home') {
        //     //     //     $doctors->where('service_price_home', '!=', 0)->whereNotNull('service_price_home');
        //     //     //     $other_doctors->where('service_price_home', '!=', 0)->whereNotNull('service_price_home');
        //     //     // } elseif ($doctor_field == 'online') {
        //     //     //     $doctors->where('service_price_online', '!=', 0)->whereNotNull('service_price_online');
        //     //     //     $other_doctors->where('service_price_online', '!=', 0)->whereNotNull('service_price_online');
        //     //     // } else {
        //     //     //     $doctors->whereNotNull('service_price_online')->where('service_price_online', '!=', 0)
        //     //     //         ->whereNotNull('service_price_home')->where('service_price_home', '!=', 0);
        //     //     // }
        //     // }

        //     if ($request->has('specialization_id')) {
        //         $specialization_id   = $request->input('specialization_id');
        //         $doctors->where('specialization_id', $specialization_id);
        //     }


        //     if ($request->has('sortBy')) {
        //         $sortBy = $request->input('sortBy');
        //         switch ($sortBy) {
        //             case 'price_low':
        //                 $doctors->where('service_price_home', '!=', 0)->whereNotNull('service_price_home')->orderBy('service_price_home');
        //                 break;
        //             case 'price_high':
        //                 $doctors->where('service_price_home', '!=', 0)->whereNotNull('service_price_home')->orderBy('service_price_home', 'desc');
        //                 break;
        //             case 'best_rating':
        //                 $doctors->orderBy('total_rate', 'desc');
        //                 break;
        //         }
        //     }
        // } else {
        //     // If no filters are provided, order by `updated_at` and fetch all
        //     $doctors = $doctors->orderBy('updated_at', 'desc');
        // }

        // // $res = [
        // //     'doctors' => DoctorResource::collection($doctors->get()),
        // //     'all_doctors' => DoctorResource::collection($other_doctors->get())
        // // ];
        // // Execute the query and return the results
        return $this->returnData(collect([]), [helperTrans('api.doctors data')], 200);
    }


    public function general_doctors(Request $request)
    {
        $user = auth('doctor')->user();
        if ($user) {
            $doctors = Doctor::where('status', 1)->where('doctor_type', 'general')->where('id', '!=', $user->id)->get();
        } else {
            $doctors = Doctor::where('status', 1)->where('doctor_type', 'general')->get();
        }

        return $this->returnData(DoctorResource::collection($doctors), [helperTrans('api.doctors data')], 200);
    }

    public function search(Request $request)
    {


        $query = $request->input('query');



        $productsQuery = Doctor::where('status', 1)->where('name->ar', 'LIKE', "%{$query}%")
            ->orWhere('name->en', 'LIKE', "%{$query}%")
            ->orWhere('nickname', 'LIKE', "%{$query}%");

        return $this->returnData(DoctorResource::collection($productsQuery->get()), [helperTrans('api.doctors data')], 200);
    }

    public function doctorDetails($id)
    {
        $doctor = Doctor::with(['specialization', 'times', 'doctor_branch', 'reviews'])->where('id', $id)->where('status', true)->first();
        if (!$doctor) {
            return $this->returnError([helperTrans('api.doctor not found')], 404);
        }
        $data = [
            "basic_information" => new  DoctorDetailsResource($doctor),
            "statistics" => new DoctorStatisticsResource($doctor),
            'doctor_branches' => $doctor->doctor_type != 'specialized' ? $doctor->provider_branches() :DoctorBranchResource::collection($doctor->doctor_branch),
            'reviews' => DoctorReviewsResource::collection($doctor->reviews),

        ];
        // dd('gg');
        return $this->returnData($data, [helperTrans('api.doctor data')], 200);
    }

    public function doctors_by_category(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'category_id' => 'required|exists:categories,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $category = Category::find($request->category_id);

        return $this->returnData(DoctorResource::collection($category->doctors), [helperTrans('api.doctors data')], 200);
    }

    public function get_popular()
    {
        $doctors = Doctor::with(['specialization', 'times', 'doctor_branch'])->where('status', true)->where('is_popular', 1)->get();
        return $this->returnData(DoctorLessResource::collection($doctors), [helperTrans('api.doctors popular data')], 200);
    }

    public function fliter_doctor(Request $request)
    {
        $doctors = Doctor::whenGender($request->gender)
            ->whenSpecialization($request->specialization)
            ->whenCity($request->city)
            ->whenGovernorate($request->governorate)
            ->whenPriceHome($request->priceFromHome, $request->priceToHome)
            ->whenPriceOnline($request->priceFromOnline, $request->priceToOnline)
            ->with(['specialization', 'times', 'doctor_branch'])->where('status', true)->get();
        return $this->returnData(DoctorResource::collection($doctors), [helperTrans('api.doctors popular data')], 200);
    }

    public function branches($doctor_id)
    {
        $doctor = Doctor::find($doctor_id);

        if (!$doctor) {
            return $this->returnError([helperTrans('api.doctor not found')], 404);
        }

        $doctorBranches = DoctorBranch::join('provider_times as pt', 'doctor_branches.id', '=', 'pt.provider_id')
            ->join('cities as c', 'c.id', '=', 'doctor_branches.city_id')
            ->join('days as d', 'd.id', '=', 'pt.day_id')
            ->whereIn('doctor_branches.id', function ($query) use ($doctor_id) {
                $query->select('id')
                    ->from('doctor_branches')
                    ->where('doctor_id', $doctor_id);
            })
            ->where('pt.type', 'offline')
            ->get(['doctor_branches.*', 'pt.*', 'd.*', 'c.*', 'doctor_branches.location as address', 'c.name as city_name', 'doctor_branches.id as branche_id']);

        $filteredBranches = $doctorBranches->map(function ($branch) {
            return [
                'branche_id' => $branch['branche_id'],
                'from_time' => $branch['from_time'],
                'to_time' => $branch['to_time'],
                'city_name' => $branch['city_name'],
                'city' => json_decode($branch['city_name'], true),
                'address' => $branch['address'] ?? '',
                'price' => $branch['price'] ?? '',
                'latitude' => $branch['latitude'],
                'longitude' => $branch['longitude'],
                'day_id' => $branch['day_id'],
                'days' => json_decode($branch['day'], true),
            ];
        });

        (object)$groupedBranches = $filteredBranches->groupBy('city_name')->map(function ($branches) {

            $days = $branches->map(function ($branch) {
                return  collect($branch['day_id'])->map(function ($dayId) use ($branch) {
                    return  (object) [
                        'id' => $dayId,
                        'ar' => $branch['days']['ar'],
                        'en' => $branch['days']['en'],
                    ];
                });
            })->flatten(1)->unique('id')->values();

            return (object)[
                'city' => $branches->first()['city'],
                'branche_id' => $branches->first()['branche_id'],
                'from_time' => $branches->first()['from_time'],
                'to_time' => $branches->first()['to_time'],
                'days' => $days,
                'price' => $branches->first()['price'],
                'address' => $branches->first()['address'],
                'latitude' => $branches->first()['latitude'],
                'longitude' => $branches->first()['longitude'],
            ];
        });

        $indexedBranches = $groupedBranches->values();

        // return $indexedBranches;

        return $this->returnData(DoctorClinicResource::collection($indexedBranches), [helperTrans('api.doctor branches')], 200);
    }

    public function online_availability($doctor_id)
    {
        $online_times = ProviderTime::with('day')
            ->where('provider_id', $doctor_id)
            ->where('provider_type', "doctor")
            ->where('type', "online")->get();

        if (!$online_times->count()) {
            return $this->returnError([helperTrans('api.doctor has no online appointments')], 404);
        }

        $online_times = ProviderTimeResource::collection($online_times);

        return $online_times;
    }
}
