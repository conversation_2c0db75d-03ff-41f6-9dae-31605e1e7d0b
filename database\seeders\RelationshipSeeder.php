<?php

namespace Database\Seeders;

use App\Models\Relationship;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class RelationshipSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $relationships = [
            ['name' => 'Son', 'code' => '30'],
            ['name' => 'Daughter', 'code' => '40'],
            ['name' => 'Father', 'code' => '60'],
            ['name' => 'Mother', 'code' => '50'],
            ['name' => 'Wife', 'code' => '20'],
            ['name' => 'Husband', 'code' => '70'],
            // ['name' => 'Brother', 'code' => 'BROTHER'],
            // ['name' => 'Sister', 'code' => 'SISTER'],
            ['name' => 'Other', 'code' => '80'],
        ];

        foreach ($relationships as $relationship) {
            Relationship::updateOrCreate(
                ['name' => $relationship['name']],
                $relationship
            );
        }
    }
}
