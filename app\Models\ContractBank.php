<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractBank extends Model
{
    use HasFactory;
    protected $table = 'contract_banks';
    protected $fillable = [
        'contract_id',
        'bank_name',
        'bank_account_number',
        'bank_iban',
        'bank_swift_code',
        'bank_branch_bank',
        'bank_mobile_number',
    ];
    protected $guarded = [];
    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }
}
