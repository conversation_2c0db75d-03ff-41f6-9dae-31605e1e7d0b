<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\CityRequest;
use App\Http\Requests\Dashboard\GovernorateRequest;
use App\Http\Resources\CityResource;
use App\Http\Resources\GovernorateResource;
use App\Http\Traits\Api_Trait;
use App\Models\City;
use App\Models\Governorate;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class GovernorateController extends Controller
{

    use Api_Trait;

    public function index(Request $request)
    {
        $governorates = Governorate::with(['country', 'nationality']);

        if ($request->country_id) {
            $governorates->where('country_id', $request->country_id);
        }

        $governorates = $governorates->get();
        return $this->returnData(GovernorateResource::collection($governorates), [helperTrans('api.governorates data')]);
    }

    public function show(Governorate $governorate)
    {
        return $this->returnData(new GovernorateResource($governorate->load('nationality','country')), [helperTrans('api.governorate show')]);
    }

    public function store(GovernorateRequest $request)
    {
        $governorate = Governorate::create([
            'name' => $request->name,
            'nationality_id' => $request->nationality_id
        ]);

        return $this->returnData(new GovernorateResource($governorate), [helperTrans('api.governorate created')], 201);
    }

    public function update(GovernorateRequest $request, Governorate $governorate)
    {
        $governorate->update($request->only('nationality_id'));
        if ($request->has('name')) {
            $governorate->setTranslations('name', $request->name);
            $governorate->save();
        }

        return $this->returnData(new GovernorateResource($governorate), [helperTrans('api.governorate updated')]);
    }

    public function destroy(Governorate $governorate)
    {
        $governorate->delete();
    }
}
