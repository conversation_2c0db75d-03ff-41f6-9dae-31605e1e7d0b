<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('status_bookings', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->foreignId('booking_type_id')->nullable()->constrained('booking_types')->onDelete('restrict');;
            $table->string('notes')->nullable();
            $table->string('color')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('status_bookings');
    }
};
