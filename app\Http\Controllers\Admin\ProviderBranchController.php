<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Provider;
use App\Models\ProviderBranch;
use App\Models\Governorate;
use App\Models\City;
use App\Models\ProviderTime;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class ProviderBranchController extends Controller
{
    public function index(Provider $provider, Request $request)
    {
        if (request()->ajax()) {
            $branches = ProviderBranch::where('provider_id', $provider->id);
            return DataTables::of($branches)
                ->addColumn('action', function ($branch) use ($provider) {
                    $html = '<div class="dropdown d-inline-block">
                    <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ri-more-fill align-middle"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">';
                    if($branch->provider->provider_category_id == 4){
                        $html .= '<li><a class="dropdown-item" href="' . route('admin.branch_clinics.index', ['branchId' => $branch->id]) . '"><i class="ri-stethoscope-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.clinics') . '</a></li>';
                    }

                    $html .= '<li><a class="dropdown-item" href="' . route('admin.provider_branches.edit', ['provider' => $provider->id, 'branch' => $branch->id]) . '"><i class="ri-pencil-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.edit') . '</a></li>
                        <li>
                            <a class="dropdown-item remove-item-btn" href="javascript:void(0)" data-id="' . $branch->id . '">
                                <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.delete') . '
                            </a>
                        </li>
                    </ul>
                </div>';
                    return $html;
                })
                ->addColumn('name_en', function ($branch) {
                    return $branch->getTranslation('name', 'en');
                })
                ->addColumn('name_ar', function ($branch) {
                    return $branch->getTranslation('name', 'ar');
                })
                ->addColumn('degree_text', function ($branch) {
                    return $branch->degree === 'main' ? helperTrans('admin.main') : helperTrans('admin.branch');
                })
                ->rawColumns(['action'])
                ->make(true);
        }

        return view('Admin.CRUDS.new_providers.branches.index', compact('provider'));
    }

    public function getData(Provider $provider) {}

    public function create(Provider $provider)
    {
        $governorates = Governorate::all();
        return view('Admin.CRUDS.new_providers.branches.create', compact('provider', 'governorates'));
    }

    public function store(Request $request, Provider $provider)
    {
        $request->validate([
            'name.ar' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'degree' => 'required|in:main,branch',
            'governorate_id' => 'required|exists:governorates,id',
            'city_id' => 'required|exists:cities,id',
            'location' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'latitude' => 'nullable|string',
            'longitude' => 'nullable|string',
        ]);

        $data = $request->except(['name']);
        $data['name'] = $request->name;
        $branch = $provider->branches()->create($data);

        $times=[];
        for ($i=1; $i <= 7; $i++) {
            $times[] = [
                'provider_type' => 'provider_branch',
                'provider_id' => $branch->id,
                'day_id' => $i,
                'from_time' => '09:00',
                'to_time' => '24:00',
                'type' => 'offline'
            ];
        }
        ProviderTime::insert($times);
        return redirect()->route('admin.provider_branches.index', ['provider' => $provider->id])
            ->with('success', helperTrans('admin.Branch created successfully'));
    }

    public function edit(Provider $provider, ProviderBranch $branch)
    {
        $governorates = Governorate::all();
        $cities = City::where('governorate_id', $branch->governorate_id)->get();
        return view('Admin.CRUDS.new_providers.branches.edit', compact('provider', 'branch', 'governorates', 'cities'));
    }

    public function update(Request $request, Provider $provider, ProviderBranch $branch)
    {
        $request->validate([
            'name.ar' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'degree' => 'required|in:main,branch',
            'governorate_id' => 'required|exists:governorates,id',
            'city_id' => 'required|exists:cities,id',
            'location' => 'required|string|max:255',
            'phone' => 'required|string|max:20',
            'latitude' => 'nullable|string',
            'longitude' => 'nullable|string',
        ]);

        $data = $request->except(['name']);
        $data['name'] = $request->name;

        $branch->update($data);

        ProviderTime::where('provider_id', $branch->id)->where('provider_type', 'provider_branch')->delete();

        $times=[];
        for ($i=1; $i <= 7; $i++) {
            $times[] = [
                'provider_type' => 'provider_branch',
                'provider_id' => $branch->id,
                'day_id' => $i,
                'from_time' => '09:00',
                'to_time' => '24:00',
                'type' => 'offline'
            ];
        }
        ProviderTime::insert($times);

        return redirect()->route('admin.provider_branches.index', ['provider' => $provider->id])
            ->with('success', helperTrans('admin.Branch updated successfully'));
    }

    public function destroy(Provider $provider, ProviderBranch $branch)
    {
        $branch->delete();

        return response()->json(['success' => true, 'message' => helperTrans('admin.Branch deleted successfully')]);
    }
}
