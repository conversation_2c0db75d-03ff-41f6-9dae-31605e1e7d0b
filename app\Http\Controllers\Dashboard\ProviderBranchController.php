<?php

namespace App\Http\Controllers\Dashboard;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\ProviderBranchRequest;
use App\Http\Requests\FileRequest;
use App\Http\Resources\ProviderBranchResource;
use App\Http\Traits\Api_Trait;
use App\Imports\Dashboard\ProviderBranchImport;
use App\Models\Assistant;
use App\Models\AssistantBranch;
use App\Models\ImportedFile;
use App\Models\Provider;
use App\Models\ProviderBranch;
use App\Models\ProviderTime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ProviderBranchController extends Controller
{
    use Api_Trait;

    public function index(Request $request)
    {
        throw new DoctoriaException(auth()->user());
        $validator = Validator::make(
            $request->all(),
            [
                'provider_id'   => 'required|exists:providers,id',
            ],
            []
        );

        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $provider = Provider::find($request->provider_id);

        $branches = $provider->branches;

        return $this->returnData(ProviderBranchResource::collection($branches), [helperTrans('api.Branches Data')], 200);
    }

    public function show($id)
    {
        $provider_branch = ProviderBranch::find($id);
        if (!$provider_branch) {
            return $this->returnError(helperTrans('api.Provider Branch Not Found'));
        }
        return $this->returnData(ProviderBranchResource::make($provider_branch), [helperTrans('api.Provider Branch Data')], 200);
    }

    public function store(ProviderBranchRequest $request)
    {
        try {
            DB::beginTransaction();
            $branch_data = collect($request->validated())->except([
                'times',
                'assistant_name',
                'assistant_phone',
                'assistant_email',
                'assistant_password',
            ])->toArray();

            $branch_data['location'] = json_encode($request->location);

            $provider_branch = ProviderBranch::create($branch_data);
            if ($request->assistant_name) {
                $assistant = Assistant::firstOrCreate([
                    'name' => $request->assistant_name,
                    'phone' => $request->assistant_phone,
                    'email' => $request->assistant_email,
                    'password' => $request->assistant_password,
                ]);

                AssistantBranch::firstOrCreate([
                    'assistant_id' => $assistant->id,
                    'provider_branch_id' => $provider_branch->id,
                ]);
            }
           foreach ($request->times as $time) {
                ProviderTime::create([
                    'provider_id' => $provider_branch->id,
                    'provider_type'=>'provider_branch',
                    'day_id' => $time['day_id'],
                    'from_time' => $time['from_time'],
                    'to_time' => $time['to_time'],
                ]);
            }


            DB::commit();

            return $this->returnData(ProviderBranchResource::make($provider_branch), [helperTrans('api.Provider Branch Data')], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            throw new DoctoriaException($e->getMessage());
        }
    }
    public function update(ProviderBranchRequest $request, $id)
    {
        try {
            DB::beginTransaction();
            $branch_data = collect($request->validated())->except([
                'times',
                'assistant_name',
                'assistant_phone',
                'assistant_email',
                'assistant_password',
            ])->toArray();

            $branch_data['location'] = json_encode($request->location);

            $provider_branch = ProviderBranch::find($id);

            if (!$provider_branch) {
                return $this->returnError(helperTrans('api.Provider Branch Not Found'));
            }

            $provider_branch->update($branch_data);

            if ($request->assistant_name) {
                $provider_branch->assistant()->first()?->update([
                    'name' => $request->assistant_name,
                    'phone' => $request->assistant_phone,
                    'email' => $request->assistant_email,
                    'password' => $request->assistant_password,
                ]);
            }

            $provider_branch->times()->delete();

            foreach ($request->times as $time) {
                ProviderTime::create([
                    'provider_id' => $provider_branch->id,
                    'provider_type'=>'provider_branch',
                    'day_id' => $time['day_id'],
                    'from_time' => $time['from_time'],
                    'to_time' => $time['to_time'],
                ]);
            }

            DB::commit();

            return $this->returnData(ProviderBranchResource::make($provider_branch), [helperTrans('api.Provider Branch Data')], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            throw new DoctoriaException($e->getMessage());
        }
    }


    public function destroy($id)
    {
        try {
            DB::beginTransaction();

            $provider_branch = ProviderBranch::find($id);
            if (!$provider_branch) {
                return $this->returnError(helperTrans('api.Provider Branch Not Found'));
            }

            $provider_branch->times()->delete();
            $provider_branch->delete();

            DB::commit();

            return $this->returnData(null, [helperTrans('api.Provider Branch Data')], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            throw new DoctoriaException($e->getMessage());
        }
    }

    public function importBranches(FileRequest $request)
    {
        try {
            $importedFile = ImportedFile::saveFile(
                $request->file('file'),
                auth('sanctum')->id()
            );

            $import = new ProviderBranchImport($importedFile->id);
            Excel::import($import, $importedFile->path);

            $response = [
                'success_count' => $import->getSuccessCount(),
                'error_count' => $import->getErrorCount(),
                'errors' => $import->getErrors(),
                'imported_file_id' => $importedFile->id,
                'filename' => $importedFile->file_name
            ];

            if ($import->getErrorCount() > 0) {
                return $this->returnData([
                    $response,
                    helperTrans('api.Provider Branches Template Has Errors'),
                    "errors Count: {$import->getErrorCount()}"
                ], 422);
            }

            return $this->returnData($response, [
                helperTrans('api.Provider Branches imported successfully'),
                "Successfully imported {$import->getSuccessCount()} provider branches"
            ], 200);
        }
         catch (\Exception $e) {
            return $this->returnError([
                'message' => 'Provider Branch import failed: ' . $e->getMessage()
            ], 500);
        }
    }
}
