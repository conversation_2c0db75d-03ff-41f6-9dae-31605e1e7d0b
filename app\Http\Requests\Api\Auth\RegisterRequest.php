<?php

namespace App\Http\Requests\Api\Auth;

use Illuminate\Foundation\Http\FormRequest;

class RegisterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name' => 'nullable',
            'refer_code' => "nullable|unique:patients,refer_code",
            'nickname' => 'nullable|unique:patients,nickname',
            'phone' => 'required|unique:patients,phone|unique:doctors,phone',
            'gender' => 'nullable|in:male,female',
            'postcode' => 'nullable',
            'city_id' => 'nullable|exists:cities,id',
            'nationality_id' => 'nullable|exists:nationalities,id',
            'address' => 'nullable',
            'email' => 'nullable|unique:patients,email',
            'password' => 'required|min:8|max:12|confirmed',
            'driver' => 'required|in:sms,whatsapp',
            'otp_type' => "nullable|in:forgot-password,register",
        ];
    }
}
