<?php

namespace App\Http\Controllers\Api\V1\Doctor;

use App\Models\Pharmacy;
use App\Models\Diagnosis;
use App\Models\SolutionType;
use Illuminate\Http\Request;
use App\Models\MedicationWay;
use App\Http\Traits\Api_Trait;
use App\Models\MedicationUnit;
use App\Models\SolutionPriority;
use App\Http\Controllers\Controller;
use App\Http\Resources\PharmacyResource;
use App\Http\Resources\DiagnosisResource;
use App\Http\Resources\PharmaciesResource;
use App\Http\Resources\SolutionTypeResource;
use App\Http\Resources\MedicationWayResource;
use App\Http\Resources\MedicationUnitResource;
use App\Http\Resources\SolutionPriorityResource;

class DiagnosesController extends Controller
{
    //
    use Api_Trait;

    // public function diagnoses()
    // {
    //     $diagnoses = Diagnosis::get();
    //     return $this->returnData(DiagnosisResource::collection($diagnoses), [helperTrans('api.Diagnoses data')], 200);
    // }

    public function diagnoses(Request $request)
    {
        $query = $request->input('query');

        // Fetch medication units based on the query
        $diagnoses = Diagnosis::where('diagnosis->ar', 'LIKE', "%{$query}%")
        ->orWhere('diagnosis->en', 'LIKE', "%{$query}%")
        ->get(); // Execute the query to get the results

        // Return the data with a resource collection
             return $this->returnData(DiagnosisResource::collection($diagnoses), [helperTrans('api.Diagnoses data')], 200);

    }

    // public function medication_units()
    // {
    //     $medication_units = MedicationUnit::get();
    //     return $this->returnData(MedicationUnitResource::collection($medication_units), [helperTrans('api.Medication Unit Data')], 200);
    // }

    public function medication_units(Request $request)
    {
        $query = $request->input('query');

        // Fetch medication units based on the query
        $medication_units = MedicationUnit::where('unit->ar', 'LIKE', "%{$query}%")
        ->orWhere('unit->en', 'LIKE', "%{$query}%")
        ->get(); // Execute the query to get the results

        // Return the data with a resource collection
        return $this->returnData(
            MedicationUnitResource::collection($medication_units),
            [helperTrans('api.Medication Unit Data')],
            200
        );
    }

    public function pharmacies(Request $request)
    {
        $query = $request->input('query');

        // Fetch medication units based on the query
        $pharmacies = Pharmacy::where('name->ar', 'LIKE', "%{$query}%")
        ->orWhere('name->en', 'LIKE', "%{$query}%")
        ->get(); // Execute the query to get the results

        // Return the data with a resource collection
        return $this->returnData(
            PharmaciesResource::collection($pharmacies),
            [helperTrans('api.Pharmacies Data')],
            200
        );
    }


    public function solution_types()
    {
        $solution_types = SolutionType::get();
        return $this->returnData(SolutionTypeResource::collection($solution_types), [helperTrans('api.Solution Type Data')], 200);
    }

    public function medication_ways()
    {
        $medication_ways = MedicationWay::get();
        return $this->returnData(MedicationWayResource::collection($medication_ways), [helperTrans('api.Medication Ways Data')], 200);
    }

    public function solution_priorities()
    {
        $solution_priorities = SolutionPriority::get();
        return $this->returnData(SolutionPriorityResource::collection($solution_priorities), [helperTrans('api.Solution Priorities Data')], 200);
    }
}
