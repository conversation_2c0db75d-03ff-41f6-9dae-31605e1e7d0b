<?php

namespace App\Http\Controllers\Admin;

use App\Exports\RadiologyCenterExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\RadiologyCenterRequest;
use App\Http\Traits\ResponseTrait;
use App\Http\Traits\Upload_Files;
use App\Imports\RadiologyCenterImport;
use App\Models\Category;
use App\Models\Delegate;
use App\Models\DoctorContract;
use App\Models\ProviderCategory;
use App\Models\RadiologyCenter;
use App\Models\RadiologyCenterContracts;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;
use Maatwebsite\Excel\Facades\Excel;

class RadiologyCenterController extends Controller
{
    //
    use  ResponseTrait,Upload_Files;

    public function index(Request $request)
    {

        if ($request->ajax()) {
            $admins = RadiologyCenter::query()->latest();
            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {

                    $edit = '';
                    $delete = '';


                    return '
                            <button ' . $edit . '  class="editBtn btn rounded-pill btn-primary waves-effect waves-light"
                                    data-id="' . $admin->id . '"
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-edit"></i>
                                </span>
                            </span>
                            </button>
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $admin->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';


                })
                ->editColumn('name', function ($row) {

                    return $row->name ?? '';

                })


                ->addColumn('radiology_center_branches', function ($row) {
                    $route=route('radiology_center_branches.index').'?radiology_center_id='.$row->id;
                    return "<a href='$route' class='form-control'>".helperTrans('api.Show Branches')."</a>";
                })

                ->editColumn('desc', function ($row) {

                    return $row->desc ?? '';

                })
                ->editColumn('image', function ($admin) {
                    return '
                              <a data-fancybox="" href="' . get_file($admin->image) . '">
                                <img height="60px" src="' . get_file($admin->image) . '">
                            </a>
                             ';
                })


                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);


        }
        return view('Admin.CRUDS.radiologyCenter.index');
    }


    public function create()
    {

        $categories=Category::get();


        return view('Admin.CRUDS.radiologyCenter.parts.create',compact('categories'));
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try{
            $data = $request->validationData();
            $workingHours = [];
            if ($request->has('working_days')) {
                foreach ($request->working_days as $day) {
                    $workingHours[$day] = [
                        'start_time' => $request->input('start_time')[$day] ?? null,
                        'end_time' => $request->input('end_time')[$day] ?? null,
                    ];
                }
            }
            $exceptedColumn = [
                'work_time',
                'start_time',
                'end_time',
                'working_days',
                'contract_start_date',
                'contract_end_date',
                'attach_contract',
                'attach_documents',
                'attach_price_list',
                'price_list_discount',
                'contract_note',
                'bank_name',
                'bank_account_number',
                'bank_iban',
                'bank_swift_code',
                'bank_branch_bank',
                'bank_mobile_number',
                'e_wallet_name',
                'e_wallet_mobile_number',
                'instapay_mobile_number',
                'instapay_email',
                'cheque_name',
                'working_hours',
                "price_list_xray_radiologyCenter",
                "price_list_mri",
                "price_list_ct",
                "price_list_other_radiologyCenter",
                'clamis_due_date',
                'admin_fees',
                'name_1',
                'email_1',
                'phone_1',
                'name_2',
                'email_2',
                'phone_2',
            ];
            unset(
                $data['category_id'],
                $data['price_list_hematology'],
                $data['price_list_chemistry'],
                $data['price_list_hormones'],
                $data['price_list_serology'],
                $data['home_care_gross_price'],
                $data['home_care_discount'],
                $data['home_care_net_price'],
                $data['price_list_immunology'],
                $data['price_list_drug'],
                $data['price_list_microbiology'],
                $data['price_list_pathology'],
                $data['price_list_other'],
                $data['service_clinic_gross_price'],
                $data['service_clinic_discount'],
                $data['service_clinic_net_price'],
                $data['service_online_gross_price'],
                $data['service_online_discount'],
                $data['service_online_net_price'],
                $data['price_list_outpatient'],
                $data['price_list_intpatient'],
                $data['price_list_consultation'],
                $data['price_list_laboratory'],
                $data['price_list_radiologe'],
                $data['price_list_exception'],
                $data['gender'],
                $data['specialization_id'],
                $data['experience_id'],
                $data["price_list_hospital_consulation"],
                $data["price_list_hospital_outpatient"],
                $data["price_list_hospital_inpatient"],
                $data["price_list_hospital_laboratory" ],
                $data["price_list_hospital_radiology" ],
                $data["price_list_hospital_discount_other" ],
                $data["price_list_doctor_visit"],
                $data["price_list_nurse_visit"],
                $data["price_list_hospital_other"],
                $data["nickname_hospital_doctor" ],
                $data["specialization_hospital_id" ],
                $data["sub_specialization_id"],
                $data["price_list_acutemedicine_local"],
                $data["price_list_chronicmedication_local"],
                $data["price_list_cosmetics_local"],
                $data["price_list_otherservice_local"],
                $data["price_list_acutemedicine_imported"],
                $data["price_list_chronicmedication_imported"],
                $data["price_list_cosmetics_imported"],
                $data["price_list_otherservice_imported"],
                $data["price_list_dental"],
                $data["price_list_glasses"],
                $data["price_list_frame"],
                $data["price_list_sessions"]
            );
            if($request->image){
                $image = json_decode($request->image, true);
                $imagedata = $image['image'] ?? null;
                $data['image'] =  $imagedata;
            }
            $data['password'] = bcrypt($request->password);
            if (is_array($data['name'])) {
                $data['name'] = $data['name'];
            }
            if (is_array($data['address'])) {
                $data['address'] = $data['address'];
            }
            if (is_array($data['about'])) {
                $data['about'] = $data['about'];
            }
            if (is_array($data['website'])) {
                $data['website_link'] = $data['website'];
            }
            $data['delegate'] = $request->has('delegate') ? 1 : 0;
            $datafinal = Arr::except($data, $exceptedColumn);
            $row=RadiologyCenter::create($datafinal);
            $code = "radiologycenter_$row->id";
            $row->update(['code' => $code]);
            $contractData = [
                'working_hours' => json_encode($workingHours),
                'radiology_center_id' => $row->id ?? 1,
                'contract_start_date' => $request->contract_start_date,
                'contract_end_date' => $request->contract_end_date,
                'attach_contract' => $request->attach_contract,
                'attach_documents' => $request->attach_documents,
                'attach_price_list' => $request->attach_price_list,
                'contract_note' => $request->contract_note,
                'clamis_due_date' => $request->clamis_due_date,
                'admin_fees' => $request->admin_fees,
                'bank_name' => $request->bank_name,
                'bank_account_number' => $request->bank_account_number,
                'bank_iban' => $request->bank_iban,
                'bank_swift_code' => $request->bank_swift_code,
                'bank_branch_bank' => $request->bank_branch_bank,
                'bank_mobile_number' => $request->bank_mobile_number,
                'e_wallet_name' => $request->e_wallet_name,
                'e_wallet_mobile_number' => $request->e_wallet_mobile_number,
                'instapay_mobile_number' => $request->instapay_mobile_number,
                'instapay_email' => $request->instapay_email,
                'cheque_name' => $request->cheque_name,
            ];

            $contract = DoctorContract::create($contractData);
            $contractDatadetails = [
                'contract_id' => $contract->id ?? 1,
                "x_ray"=>$request->price_list_xray_radiologyCenter,
                "ct_service"=>$request->price_list_mri,
                "mri_service"=>$request->price_list_ct,
                "other_service"=>$request->price_list_other_radiologyCenter,
            ];
            RadiologyCenterContracts::create($contractDatadetails);
            if ($request->category_id)
                foreach ($request->category_id as $category_id)
                {
                    ProviderCategory::create([
                        'provider_id'=>$row->id,
                        'category_id'=>$category_id,
                        'provider_type'=>'radiology_center',
                    ]);
                }
            if ($request->has('delegate')) {
                Delegate::create([
                    'radiology_center_id ' => $row->id,
                    'name_1' => $request->name_1,
                    'email_1' => $request->email_1,
                    'phone_1' => $request->phone_1,
                    'name_2' => $request->name_2,
                    'email_2' => $request->email_2,
                    'phone_2' => $request->phone_2,
                ]);
            }
            DB::commit();
            return redirect()->route('radiology_centers.index')->with('success', 'created success');
        }catch(\Exception $e){
            DB::rollBack();
            return redirect()->back()->with('fail', 'happen error when add data');
        }
    }


    public function show($id)
    {
        //
    }


    public function edit($id)
    {
        $row = RadiologyCenter::findOrFail($id);

        $categories=Category::get();
        $categoriesIdes=ProviderCategory::where('provider_id',$row->id)->where('provider_type','radiology_center')->pluck('category_id')->toArray();



        return view('Admin.CRUDS.radiologyCenter.parts.edit', compact('row','categories','categoriesIdes'));

    }

    public function update(RadiologyCenterRequest $request, $id)
    {

        $row = RadiologyCenter::findOrFail($id);
        $data = $request->validationData();
        if ($request->image)
            $data["image"] = $this->uploadFiles('radiology_centers', $request->file('image'), null);
        unset($data['category_id']);

        $row->update($data);
        if ($request->category_id) {
            ProviderCategory::where('provider_id',$id)->where('provider_type','radiology_center')->whereNotIn('category_id',$request->category_id)->delete();

            foreach ($request->category_id as $category_id) {
                ProviderCategory::updateOrCreate([
                    'category_id' => $category_id,
                    'provider_id' => $id,
                    'provider_type'=>'radiology_center',
                ]);
            }
        }
        else{
            ProviderCategory::where('provider_id',$id)->where('provider_type','radiology_center')->delete();
        }
        return $this->updateResponse();

    }


    public function destroy($id)
    {
        $row = RadiologyCenter::findOrFail($id);
        $row->delete();
        return $this->deleteResponse();
    }//end

    public function export()
    {
        return Excel::download(new RadiologyCenterExport, 'radiology_centers.xlsx');
    }

    public function import(){
        return view('Admin.CRUDS.radiologyCenter.parts.import');
    }

    public function update_import(Request $request){

        $file = $request->file('file');

        Excel::import(new RadiologyCenterImport, $request->file('file'));

        return $this->addResponse();
    }
}
