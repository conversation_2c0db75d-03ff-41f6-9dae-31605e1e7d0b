<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\DoctoriaEmployeeResource;
use App\Http\Requests\Dashboard\DoctoriaEmployeeRequest;
use App\Http\Traits\Api_Trait;
use App\Models\DoctoriaEmployee;
use Illuminate\Http\Request;

class DoctoriaEmployeesController extends Controller
{
    use Api_Trait;

    /**
     * Display a listing of doctoria employees with filtering and pagination
     */
    public function index(Request $request)
    {
        $employees = DoctoriaEmployee::query()
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                      ->orWhere('email', 'like', '%' . $request->search . '%')
                      ->orWhere('phone', 'like', '%' . $request->search . '%')
                      ->orWhere('position', 'like', '%' . $request->search . '%');
                });
            })
            ->whenName($request->name)
            ->whenEmail($request->email)
            ->whenPhone($request->phone)
            ->whenPosition($request->position)
            ->when($request->has_email !== null, function ($query) use ($request) {
                if ($request->has_email) {
                    $query->whereNotNull('email')->where('email', '!=', '');
                } else {
                    $query->where(function ($q) {
                        $q->whereNull('email')->orWhere('email', '');
                    });
                }
            })
            ->when($request->has_phone !== null, function ($query) use ($request) {
                if ($request->has_phone) {
                    $query->whereNotNull('phone')->where('phone', '!=', '');
                } else {
                    $query->where(function ($q) {
                        $q->whereNull('phone')->orWhere('phone', '');
                    });
                }
            })
            ->when($request->has_position !== null, function ($query) use ($request) {
                if ($request->has_position) {
                    $query->whereNotNull('position')->where('position', '!=', '');
                } else {
                    $query->where(function ($q) {
                        $q->whereNull('position')->orWhere('position', '');
                    });
                }
            })
            ->orderBy($request->sort_by ?? 'name', $request->sort_direction ?? 'asc')
            ->paginate($request->per_page ?? 25);

        return $this->paginatedResponse(
            DoctoriaEmployeeResource::collection($employees),
            helperTrans('api.Doctoria Employees Data'),
            200
        );
    }

    /**
     * Store a newly created doctoria employee
     */
    public function store(DoctoriaEmployeeRequest $request)
    {
        $data = $request->validated();
        
        $employee = DoctoriaEmployee::create($data);

        return $this->returnData(
            new DoctoriaEmployeeResource($employee),
            [helperTrans('api.Doctoria Employee created successfully')], 201
        );
    }

    /**
     * Display the specified doctoria employee
     */
    public function show($id)
    {
        $employee = DoctoriaEmployee::find($id);
        if (!$employee) {
            return $this->returnError(helperTrans('api.Doctoria Employee Not Found'), 404);
        }
        
        return $this->returnData(
            new DoctoriaEmployeeResource($employee),
            [helperTrans('api.Doctoria Employee Data')], 200
        );
    }

    /**
     * Update the specified doctoria employee
     */
    public function update(DoctoriaEmployeeRequest $request, $id)
    {
        $employee = DoctoriaEmployee::find($id);
        if (!$employee) {
            return $this->returnError(helperTrans('api.Doctoria Employee Not Found'), 404);
        }
        
        $data = $request->validated();
        
        $employee->update($data);

        return $this->returnData(
            new DoctoriaEmployeeResource($employee),
            [helperTrans('api.Doctoria Employee updated successfully')], 200
        );
    }

    /**
     * Remove the specified doctoria employee
     */
    public function destroy($id)
    {
        $employee = DoctoriaEmployee::find($id);
        if (!$employee) {
            return $this->returnError(helperTrans('api.Doctoria Employee Not Found'), 404);
        }

        $employee->delete();

        return $this->returnData(
            null,
            [helperTrans('api.Doctoria Employee deleted successfully')],
            200
        );
    }

    /**
     * Get employees statistics
     */
    public function statistics()
    {
        $stats = [
            'total_employees' => DoctoriaEmployee::count(),
            'employees_with_email' => DoctoriaEmployee::whereNotNull('email')->where('email', '!=', '')->count(),
            'employees_with_phone' => DoctoriaEmployee::whereNotNull('phone')->where('phone', '!=', '')->count(),
            'employees_with_position' => DoctoriaEmployee::whereNotNull('position')->where('position', '!=', '')->count(),
            'employees_with_complete_info' => DoctoriaEmployee::whereNotNull('email')
                ->where('email', '!=', '')
                ->whereNotNull('phone')
                ->where('phone', '!=', '')
                ->whereNotNull('position')
                ->where('position', '!=', '')
                ->count(),
        ];

        return $this->returnData(
            $stats,
            [helperTrans('api.Doctoria Employees Statistics')],
            200
        );
    }
}
