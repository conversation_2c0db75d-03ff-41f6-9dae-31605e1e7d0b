<?php

namespace App\Http\Controllers\Api\V1\Patient;

use Illuminate\Http\Request;
use App\Http\Traits\Api_Trait;
use App\Models\InsuranceBooking;
use App\Models\InsuranceCompany;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Validator;
use App\Http\Requests\InsuranceBookingRequest;
use App\Http\Resources\InsuranceBookingResource;
use App\Http\Resources\InsuranceCompanyResource;
use App\Models\InsurancePatient;

class InsuranceCompanyController extends Controller
{
    //

    use Api_Trait;

    public function index(Request $request)
    {
        $campanies = InsuranceCompany::get();
        return $this->returnData(InsuranceCompanyResource::collection($campanies), [helperTrans('api.Insurace campanies data')], 200);
    }

    // public function insurance_booking(InsuranceBookingRequest $request){

    //     $InsuranceBookingfailed = InsuranceCompany::where('id',$request->insurance_id)->where('code',$request->code)->first();



    //     if(!$InsuranceBookingfailed){
    //         return $this->returnError([helperTrans('api.code Insurance company failed')]);
    //     }

    //     $patient = auth('patient')->user();

    //     $InsuranceBooking =  InsuranceBooking::created([
    //         'patient_id' => $patient->id,
    //         'insurance_company_id' => $request->insurance_id,
    //     ]);

    //     return $this->returnData(InsuranceBookingResource::collection($InsuranceBooking), [helperTrans('api.Insurace booking created')], 200);


    // }

    public function insurance_booking(InsuranceBookingRequest $request)
    {
        // Verify insurance company details
        $insuranceCompany = InsuranceCompany::where('id', $request->insurance_id)
            ->where('code', $request->code)
            ->first();

        if (!$insuranceCompany) {
            return $this->returnError([helperTrans('api.code Insurance company failed')]);
        }

        // Get the authenticated patient
        $patient = auth('patient')->user();

        if (!$patient) {
            return $this->returnError([helperTrans('api.Unauthenticated patient')], 401);
        }

        // Create the insurance booking
        $insurancePatient = InsurancePatient::create([
            'patient_id' => $patient->id,
            'insurance_company_id' => $request->insurance_id,
        ]);

        // Return the created booking data with a success message
        return $this->returnData(
            new InsuranceBookingResource($insurancePatient),
            [helperTrans('api.Insurance booking created')],
            200
        );
    }
}
