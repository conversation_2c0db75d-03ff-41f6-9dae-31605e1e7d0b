<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Resources\Json\JsonResource;

class PartnerResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'client_no' => $this->client_no,
            'name' => $this->name,
            'contact_person' => $this->contact_person,
            'contact_person_position' => $this->contact_person_position,
            'email' => $this->email,
            'phone' => $this->phone,
            'address' => $this->address,
            'status' => $this->status,
            'contract_start_date' => $this->contract_start_date,
            'contract_end_date' => $this->contract_end_date,
            'doctoria_employee' => $this->doctoria_employee_id ? $this->doctoria_employee : null,

            // Relationships
            // 'promo_code' => $this->when($this->relationLoaded('promoCode'), function () {
            //     return [
            //         'id' => $this->promoCode->id,
            //         'code' => $this->promoCode->code,
            //         'description' => $this->promoCode->description,
            //         'discount_type' => $this->promoCode->discount_type,
            //         'discount_value' => $this->promoCode->discount_value,
            //         'start_date' => $this->promoCode->start_date,
            //         'end_date' => $this->promoCode->end_date,
            //         'is_active' => $this->promoCode->is_active,
            //     ];
            // }),

            'package' => $this->when($this->relationLoaded('package'), function () {
                return [
                    'id' => $this->package->id,
                    'name' => $this->package->getTranslation('name', app()->getLocale()),
                    'description' => $this->package->description,
                    'price' => $this->package->price,
                    'max_relative' => $this->package->max_relative,
                ];
            }),

            // Additional computed fields
            'patients_count' => $this->when($this->relationLoaded('patients'), function () {
                return $this->patients->count();
            }),
            'country' => $this->country_id ? CountryResource::make($this->country) : null,

        ];
    }
}
