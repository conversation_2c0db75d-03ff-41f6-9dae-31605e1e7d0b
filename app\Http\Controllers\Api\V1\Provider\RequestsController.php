<?php

namespace App\Http\Controllers\Api\V1\Provider;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProviderRequestResource;
use App\Http\Traits\Api_Trait;
use App\Models\ProviderRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RequestsController extends Controller
{
    use Api_Trait;
    public function index(Request $request)
    {
        $provider = auth('provider')->user();
        $requests = ProviderRequest::where('provider_id', $provider->id)->where('show_for_provider', true)
            ->when($request->has('status'), function ($query) use ($request) {
                return $query->where('status', $request->status);
            })
            ->latest()
            ->get();

        return $this->returnData(ProviderRequestResource::collection($requests), [helperTrans('api.Booking Data')], 200);
    }

    public function change_status(Request $request)
    {
        // dd($request->all());
        $validator = Validator::make(
            $request->all(),
            [
                'request_id' => 'required|exists:provider_requests,id',
                'status' => 'required|in:active,complete,ask_to_cancel,cancel',
                'cancel_reason' => 'required_if:status,ask_to_cancel',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }


        $provider_request = ProviderRequest::find($request->request_id);
        $provider_request->status = $request->status;
        if ($request->status == 'ask_to_cancel') {
            $provider_request->cancel_reason = $request->cancel_reason;
        }
        $provider_request->save();

        if($request->status != 'ask_to_cancel'){
            run_push_notification($provider_request->patient->id, 'patient', [
                'title' => 'Booking ' . $request->status,
                'body' => 'booking ' . ($provider_request->provider ? (' at ' . $provider_request->provider->name) : ' ' ). ' has been updated to ' . ($request->status == 'active' ? 'accepted' : $request->status),
                'type' => 'booking_status',
            ]);
        }
        if($provider_request->provider){
            run_push_notification($provider_request->provider->id, 'provider', [
                'title' => 'Booking ' . $request->status,
                'body' => 'Booking has been updated to ' . ($request->status == 'active' ? 'accepted' : $request->status),
                'type' => 'booking_status',
            ]);
        }

        return $this->returnData(ProviderRequestResource::make($provider_request), [helperTrans('api.Updated Successfully')], 200);
    }
}
