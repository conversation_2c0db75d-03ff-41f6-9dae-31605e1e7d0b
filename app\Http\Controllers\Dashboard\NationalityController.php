<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\Gc\NationaltyRequest;
use App\Http\Requests\Dashboard\NationalityRequest;
use App\Http\Resources\NationalityResource;
use App\Http\Traits\Api_Trait;
use App\Models\Nationality;

class NationalityController extends Controller
{

    use Api_Trait;
    public function index()
    {
        $nationalities = Nationality::get();
        return $this->returnData(
            NationalityResource::collection($nationalities),
            [helperTrans('api.nationalities data')],
            200
        );
    }

    public function store(NationalityRequest $request)
    {
        $nationality = Nationality::create($request->only([
            'name',
            'nickname',
            'country_name',
            'phone_code',
            'country_code',
        ]));

        return $this->returnData(
            new NationalityResource($nationality),
            [helperTrans('api.nationality created')],
            201
        );
    }

    public function show(Nationality $nationality)
    {
        return $this->returnData(
            new NationalityResource($nationality),
            [helperTrans('api.nationality show')],
            200
        );
    }

    public function update(NationalityRequest $request, Nationality $nationality)
    {
        $nationality->update($request->only([
            'name',
            'nickname',
            'country_name',
            'phone_code',
            'country_code',
        ]));

        return $this->returnData(
            new NationalityResource($nationality),
            [helperTrans('api.nationality updated')],
            200
        );
    }

    public function destroy(Nationality $nationality)
    {
        $nationality->delete();

        return response()->json([
            'status' => true,
            'message' => [helperTrans('api.nationality deleted')]
        ]);
    }

}
