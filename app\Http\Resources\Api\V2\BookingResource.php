<?php

namespace App\Http\Resources\Api\V2;

use App\Http\Resources\AdressDetailsResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookingResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'id' => $this->id,
            'booking_type' => $this->type->only(['id', 'name']),
            'patient' => $this->patient->only('id', 'name', 'image', 'nickname', 'email', 'phone', 'gender'),
            'date' => $this->date,
            'time' => $this->time,
            'description' => $this->description,
            'status' => $this->status?->only('id', 'name', 'booking_type_id'),
            'payment_method' => $this->paymentMethod?->only('id', 'name'),
            'payment' => $this->payment,
            'price' => $this->price,
            'promo_code' => $this->promoCode?->only('id', 'code', 'description', 'discount_type', 'discount_value'),
            'provider' => $this->provider?->only('id', 'name', 'image', 'phone', 'email'),
            'provider_branch' => $this->providerBranch?->only('id', 'name', 'phone', 'email'),
            'doctor' => new DoctorResource($this->doctor),
            'referal_booking' => $this->referalBooking?->only('id', 'description', 'status', 'payment', 'price', 'provider', 'provider_branch', 'doctor'),
            'relative' => $this->relative?->only('id', 'name', 'gender', 'birth_date', 'is_smoking', 'is_alcoholic'),
            'services' => $this->services,
            'attachments' => $this->attachments,
            'address_detail' => new AdressDetailsResource($this->addressDetail),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
