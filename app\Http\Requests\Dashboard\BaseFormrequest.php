<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

class BaseFormRequest extends FormRequest
{
    protected function failedValidation(Validator $validator)
    {
        throw new HttpResponseException($this->returnErrorValidation($validator));
    }

    protected function returnErrorValidation(Validator $validator)
    {
        return response()->json([
            'code' => 403,
            'message' => __('messages.validation_failed'),
            'errors' => $validator->errors(),
        ], 200);
    }
}
