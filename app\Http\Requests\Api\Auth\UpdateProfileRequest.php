<?php

namespace App\Http\Requests\Api\Auth;

use Illuminate\Foundation\Http\FormRequest;

class UpdateProfileRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $user = auth('patient')->user();
        return [
            'name' => 'nullable',
            'email' => "nullable|unique:doctors,email|unique:patients,email,".$user->id,
            'nickname' => 'nullable|unique:doctors,nickname|unique:patients,nickname,'.$user->id,
            'phone' => 'nullable|unique:doctors,phone|unique:patients,phone,' . $user->id,
            'password' => 'nullable|min:8|max:12',
            'gender'=>'nullable|in:male,female',
            'location'=>'nullable',
            'image' => 'nullable|file',
        ];
    }
}
