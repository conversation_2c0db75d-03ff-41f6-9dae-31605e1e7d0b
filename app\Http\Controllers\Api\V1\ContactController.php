<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Contact;
use App\Http\Resources\ContactResource;
use App\Http\Requests\Api\ContactRequest;
use App\Http\Traits\Api_Trait;

class ContactController extends Controller
{
    use Api_Trait;
    public function index()
    {
        if (auth('patient')->check()) {
            $user = auth('patient')->user();
            $contacts = $user->contacts ?? [];
        } else if (auth('provider')->check()) {
            $user = auth('provider')->user();
            $contacts = $user->contacts ?? [];
        } else if (auth('doctor')->check()) {
            $user = auth('doctor')->user();
            $contacts = $user->contacts ?? [];
        }
        return $this->returnData(ContactResource::collection($contacts), 'Contacts retrieved successfully');
    }

    public function store(ContactRequest $request)
    {
        $validated = $request->validated();
        if (auth('patient')->check()) {
            $user = auth('patient')->user();
        } else if (auth('provider')->check()) {
            $user = auth('provider')->user();
        } else if (auth('doctor')->check()) {
            $user = auth('doctor')->user();
        }

        $contact = $user->contacts()->create([
            'name' => $validated['name'],
            'email' => $validated['email'],
            'message' => $validated['message'],
        ]);
        return $this->returnData(ContactResource::make($contact), 'Contact created successfully');
    }
    public function mark_contact(Request $request, $contact_id)
    {

        $contact = Contact::find($contact_id);
        $contact->update(['is_user_read' => true]);
        return $this->returnData(ContactResource::make($contact), 'Contact marked successfully');
    }
}
