<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\Route;

class RouteController
{
  function all()
  {
    $routes = collect(Route::getRoutes())->map(function ($route) {
      return [
        'uri' => $route->uri(),
        'name' => $route->getName(),
        'methods' => $route->methods(),
        'action' => $route->getActionName(),
      ];
    });

    return view('routes.all', ['routes' => $routes]);
  }

  function api()
  {
    $routes = collect(Route::getRoutes())->filter(function ($route) {
      return in_array('api', $route->middleware());
    })->map(function ($route) {
      return [
        'uri' => $route->uri(),
        'methods' => $route->methods(),
      ];
    });

    return view('routes.api', ['routes' => $routes]);
  }
}
