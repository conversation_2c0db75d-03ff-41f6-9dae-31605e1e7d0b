<?php

namespace App\Http\Controllers\Api\V1\Setting;

use App\Http\Controllers\Controller;
use App\Http\Resources\CategoryResource;
use App\Http\Resources\NewSpecializationResource;
use App\Http\Traits\Api_Trait;
use App\Models\Category;
use App\Models\Specialization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class CategoryController extends Controller
{
    //
    use Api_Trait;
    // public function index()
    // {
    //     $categories = Category::get();
    //     return $this->returnData(CategoryResource::collection($categories), [helperTrans('api.categories data')], 200);
    // }

    public function index()
    {
        $categories = Category::get();
        return $this->returnData(CategoryResource::collection($categories), [helperTrans('api.categories data')], 200);
    }
}
