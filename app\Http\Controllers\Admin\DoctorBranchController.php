<?php

namespace App\Http\Controllers\Admin;

use App\Models\Day;
use App\Models\Branch;
use App\Models\Doctor;
use App\Models\Category;
use App\Models\DoctorBranch;

use App\Models\ProviderTime;

use Illuminate\Http\Request;
use App\Models\ProviderCategory;
use Yajra\DataTables\DataTables;
use App\Http\Traits\Upload_Files;
use App\Http\Traits\ResponseTrait;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\DoctorRequest;
use App\Http\Requests\Admin\DoctorBranchRequest;
use App\Http\Requests\Admin\ProviderTimeRequest;

class DoctorBranchController extends Controller
{
    //
    //
    use  ResponseTrait, Upload_Files;

    public function index(Request $request, $id)
    {
        $doctor = Doctor::find($id);

        if (!$doctor) {
            return redirect()->back()->with('error', 'الطبيب غير موجود');
        }

        if ($request->ajax()) {
            $admins = DoctorBranch::where('doctor_id', $id)
                ->latest();

            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {
                    $delete = '';
                    return '
                        <a href="' . route('doctors_branches.edit', $admin->id) . '" class="btn btn-primary btn-sm me-2">
                            <i class="fas fa-edit"></i>
                        </a>
                        <button ' . $delete . ' class="btn rounded-pill btn-danger waves-effect waves-light delete" data-id="' . $admin->id . '">
                            <span class="svg-icon svg-icon-3">
                                <i class="las la-trash-alt"></i>
                            </span>
                        </button>
                    ';
                })
                ->editColumn('status', function ($admin) {
                    return $admin->status;
                })
                ->editColumn('name', function ($row) {
                    if (is_json($row->name)) {
                        $nameData = json_decode($row->name, true);
                        $nameAr = $nameData['ar'] ?? 'N/A';
                        $nameEn = $nameData['en'] ?? 'N/A';
                        return '<span>' . htmlspecialchars($nameAr) . '</span> / <span>' . htmlspecialchars($nameEn) . '</span>';
                    }
                    if (is_string($row->name)) {
                        return htmlspecialchars($row->name);
                    }
                })
                ->editColumn('location', function ($row) {
                    if (is_json($row->location)) {
                        $locationData = json_decode($row->location, true);
                        $locationAr = $locationData['ar'] ?? 'N/A';
                        $locationEn = $locationData['en'] ?? 'N/A';
                        return htmlspecialchars($locationAr);
                    }
                    if (is_string($row->location)) {
                        return htmlspecialchars($row->location);
                    }
                })
                ->editColumn('about', function ($row) {
                    if (is_json($row->about)) {
                        $aboutData = json_decode($row->about, true);
                        $aboutAr = $aboutData['ar'] ?? 'N/A';
                        $aboutEn = $aboutData['en'] ?? 'N/A';
                        return htmlspecialchars($aboutAr);
                    }
                    if (is_string($row->about)) {
                        return htmlspecialchars($row->about);
                    }
                })
                ->editColumn('price', function ($admin) {
                    return number_format($admin->price, 2);
                })
                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);
        }

        return view('Admin.CRUDS.doctorBranch.index', compact('doctor'));
    }




    public function create($id)
    {
        $doctor = Doctor::find($id);
        return view('Admin.CRUDS.doctorBranch.parts.create', compact('doctor'));
    }

    public function store(DoctorBranchRequest $request, $id)
    {
        $doctor_id = Doctor::find($id);
        $data = $request->validated();

        if ($request->hasFile('image')) {
            $data["image"] = $this->uploadFiles('doctors/doctor_branch/' . $doctor_id->id, $request->file('image'), null);
        }

        $data['doctor_id'] = $doctor_id->id;
        $doctor = DoctorBranch::create($data);

        $category_id = Category::where('slug', 'doctor_branch')->first();
        if ($category_id == null) {
            $category_id = Category::create([
                'slug'  => 'doctor_branch',
                'name'  => 'doctor_branch',
                'main_service_id'  => 2,
            ]);
        }

        if ($category_id) {
            ProviderCategory::create([
                'provider_id' => $doctor->id,
                'category_id' => $category_id->id,
                'provider_type' => 'doctor_branch',
            ]);
        }

        return redirect()->back()->with('success', ' doctor added successfully');
    }



    public function edit($id)
    {
        $row = DoctorBranch::with('times')->findOrFail($id);
        $days = Day::get();
        return view('Admin.CRUDS.doctorBranch.parts.edit', compact('row', 'days'));
    }

    public function update(DoctorBranchRequest $request, $id)
    {
        $row = DoctorBranch::findOrFail($id);
        $data = $request->validated();

        if ($request->hasFile('image')) {
            $data["image"] = $this->uploadFiles('doctors/doctor_branch/' . $row->doctor->id, $request->file('image'), $row->image);
        }

        $row->update($data);

        return redirect()->route('admin.doctors_branches.index', $row->doctor->id)->withInput()->with('success', ' doctor updated successfully');
    }


    public function destroy($id)
    {
        $row = DoctorBranch::findOrFail($id);
        $row->delete();
        return redirect()->route('admin.doctors_branches.index', $row->doctor->id)->with('success', ' doctor deleted successfully');
    } //end fun

    public function doctor_times($id)
    {

        $doctor = DoctorBranch::findOrFail($id);
        $doctorTimes = ProviderTime::where('provider_type', 'doctor_branch')->where('provider_id', $id)->get();
        $days = Day::get();
        $categoriesIdes = ProviderCategory::where('provider_id', $id)->where('provider_type', 'doctor_branch')->pluck('category_id')->toArray();
        $categories = Category::whereIn('id', $categoriesIdes)->get();
        return view('Admin.CRUDS.doctorBranch.times', compact('doctor', 'doctorTimes', 'days'));
    }


    public function update_doctor_times($id, ProviderTimeRequest $request)
    {
        $doctor = DoctorBranch::findOrFail($id);
        ProviderTime::where('provider_id', $id)->where('provider_type', 'doctor_branch')->delete();

        $validated = $request->validated();

        if (isset($validated['day_id']) && is_array($validated['day_id'])) {
            foreach ($validated['day_id'] as $index => $dayId) {
                ProviderTime::create([
                    'provider_id' => $id,
                    'day_id' => $dayId,
                    'from_time' => $validated['from_time'][$index],
                    'to_time' => $validated['to_time'][$index],
                    'type' => 'offline',
                    'provider_type' => 'doctor_branch',
                ]);
            }
        }

        return redirect()->route('admin.doctors_branches.index', $doctor->doctor->id)->with('success', ' doctor deleted successfully');
    }
}
