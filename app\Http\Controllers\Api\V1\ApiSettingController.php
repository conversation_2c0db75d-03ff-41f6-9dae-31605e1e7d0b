<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Traits\Api_Trait;
use App\Models\ApiSetting;
use Illuminate\Http\Request;

class ApiSettingController extends Controller
{
    use Api_Trait;

    public function index()
    {
        $setting_keys = ApiSetting::pluck('key');
        $setting = [];
        foreach ($setting_keys as $key) {
            $setting[$key] = ApiSetting::get($key);
        }

        return $this->returnData($setting, 'Api settings retrieved successfully');
    }
}
