<?php

namespace App\Http\Requests\Api\Patient;

use App\Rules\ExistsInBothBookingTables;
use Illuminate\Foundation\Http\FormRequest;

class UpdateBookingRequest extends FormRequest
{


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {



        return [
            'booking_id' => ['required', new ExistsInBothBookingTables()],
            'payment_method' => 'required|in:individual,cash',
            'invoice' => 'required_if:payment_method,individual',
            'invoice.invoice_id'    => 'unique:invoices,invoice_id',
            "phone" => 'nullable',
            "latitude" => 'nullable',
            "longitude" => 'nullable',
            "location" => 'nullable',
            "governorate_id" => 'nullable|exists:governorates,id',
            "city_id" => 'nullable|exists:cities,id',
            "street" => 'nullable'
        ];
    }
}
