<?php

namespace App\Http\Controllers\Api\V1\Patient;

use App\Http\Controllers\Controller;
use App\Http\Resources\SpecializationBookingResource;
use App\Http\Resources\ProviderSpecializationBookingResource;
use App\Http\Traits\Api_Trait;
use App\Http\Traits\Upload_Files;
use App\Models\Doctor;
use App\Models\DoctorBranch;
use App\Models\PromoCode;
use App\Models\SpecializationBookingDocs;
use App\Models\SpecializationBooking;
use App\Models\ProviderSpecializationBooking;
use App\Models\ProviderSpecializationBookingDocs;
use App\Models\ProviderTime;
use App\Models\Relative;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class SpecializationBookingController extends Controller
{
    use Api_Trait, Upload_Files;
    //
    public function make_booking(Request $request)
    {

        $patient = auth('patient')->user();

        if ($request->visit == 'home') {
            $validator = Validator::make(
                $request->all(),
                [
                    'booking_type'  => 'required|in:individual',
                    'date'          => 'required|date',
                    'time'          => 'required|date_format:H:i',
                    'relative_id'   => 'nullable|exists:relatives,id',
                    'description'   => 'nullable',
                    'files_ids'     =>  'nullable|array',
                    'phone'         =>  'required',
                    'doctor_id'     =>  'required|exists:doctors,id',
                    'latitude'      =>  'nullable',
                    'longitude'     =>  'nullable',
                    'location'      => 'nullable',
                    'governorate_id' =>  'nullable|exists:governorates,id',
                    'city_id'       =>  'nullable|exists:cities,id',
                    'street'        =>  'nullable',
                    'promo_code_id' => 'nullable|numeric|exists:promo_codes,id',
                    'invoice'    => 'nullable',
                    'invoice.invoice_id'    => 'unique:invoices,invoice_id',
                ],
                []
            );
            if ($validator->fails()) {
                return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
            }


            $booking = SpecializationBooking::create([
                'id'   => getNextSharedId(),
                'patient_id'    => $patient->id,
                'date'          => $request->date,
                'time'          => $request->time,
                'description'   => $request->description,
                'location'      => $request->location,
                'booking_type'  => $request->booking_type,
                'visit'         => $request->visit,
                'doctor_id'     => $request->doctor_id,
                'referral_code' => $request->referral_code,
                'phone'         => $request->phone,
                'relative_id'   => $request->relative_id,
                'latitude'      =>  $request->latitude,
                'longitude'     =>  $request->longitude,
                'governorate_id' => $request->governorate_id,
                'city_id'       =>  $request->city_id,
                'street'        =>  $request->street,
                'promo_code_id' =>  $request->promo_code_id ?? null,
                'price'         => Doctor::find($request->doctor_id)->service_price_home,
                // 'invoice_id'    =>  $request->invoice_id ?? null,
            ]);

            if ($request->invoice) {
                $booking->invoices()->create([
                    'invoice_id' => $request->invoice['invoice_id'],
                    'invoice_key' => $request->invoice['invoice_key'],
                    'payment_data' => $request->invoice['payment_data'],
                ]);
            }

            if ($request['files_ids'] && count($request['files_ids']) > 0) {
                foreach ($request['files_ids'] as $file_id) {
                    $media = Media::find($file_id);
                    if ($media) {
                        $media->model_type = SpecializationBooking::class;
                        $media->model_id = $booking->id;
                        $media->save();
                    }
                }
            }

            // return $this->returnData(SpecializationBookingResource::make($booking), [helperTrans('api.Specialization booking successfully')]);
        } elseif ($request->visit == 'online') {

            $validator = Validator::make(
                $request->all(),
                [
                    'date'          => 'required|date',
                    'time'          => 'required|date_format:H:i',
                    'description'   => 'nullable',
                    'booking_type'  => 'required|in:individual',
                    'relative_id' => 'nullable|exists:relatives,id',
                    'files_ids'     =>  'nullable|array',
                    'doctor_id'     =>  'required|exists:doctors,id',
                    'promo_code_id' => 'nullable|numeric|exists:promo_codes,id',
                    'invoice'    => 'nullable',
                    'invoice.invoice_id'    => 'unique:invoices,invoice_id',
                ],
                []
            );
            if ($validator->fails()) {
                return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
            }

            $booking = SpecializationBooking::create([
                'id'   => getNextSharedId(),
                'patient_id'    => $patient->id,
                'date'          => $request->date,
                'time'          => $request->time,
                'description'   => $request->description,
                'location'      => $request->location,
                'booking_type'  => $request->booking_type,
                'visit'         => $request->visit,
                'doctor_id'     => $request->doctor_id,
                'branch_id'     => $request->branch_id,
                'referral_code' => $request->referral_code,
                'relative_id'   => $request->relative_id,
                'promo_code_id' =>  $request->promo_code_id ?? null,
                'price'        => Doctor::find($request->doctor_id)->service_price_online,
                // 'invoice_id'    =>  $request->invoice_id ?? null,
            ]);

            if ($request->invoice) {
                $booking->invoices()->create([
                    'invoice_id' => $request->invoice['invoice_id'],
                    'invoice_key' => $request->invoice['invoice_key'],
                    'payment_data' => $request->invoice['payment_data'],
                ]);
            }


            if ($request['files_ids'] && count($request['files_ids']) > 0) {
                foreach ($request['files_ids'] as $file_id) {
                    $media = Media::find($file_id);
                    if ($media) {
                        $media->model_type = SpecializationBooking::class;
                        $media->model_id = $booking->id;
                        $media->save();
                    }
                }
            }

            // return $this->returnData(SpecializationBookingResource::make($booking), [helperTrans('api.Specialization booking successfully')]);
        } elseif ($request->visit == 'offline') {

            $validator = Validator::make(
                $request->all(),
                [
                    'date'          => 'required',
                    'time'          => 'required',
                    'branch_id'     => 'required|exists:doctor_branches,id',
                    'doctor_id'     =>  'nullable|exists:doctors,id',
                    'booking_type'  => 'required|in:individual,cash',
                    'description'   => 'nullable|string',
                    'relative_id' => 'nullable|exists:relatives,id',
                    'files_ids'     =>  'nullable|array',
                    'phone'         =>  'nullable',
                    'promo_code_id' => 'nullable|numeric|exists:promo_codes,id',
                    'invoice'    => 'nullable',
                    'invoice.invoice_id'    => 'unique:invoices,invoice_id',
                ],
                []
            );
            if ($validator->fails()) {
                return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
            }


            $booking = SpecializationBooking::create([
                'id'   => getNextSharedId(), //very important
                'patient_id'    => $patient->id,
                'date'          => $request->date,
                'time'          => $request->time,
                'booking_type'  => $request->booking_type,
                'visit'         => $request->visit,
                'doctor_id'     => $request->doctor_id,
                'referral_code' => $request->referral_code,
                'branch_id'     => $request->branch_id,
                'description'   => $request->description,
                'relative_id'   => $request->relative_id,
                'phone'         => $request->phone,
                'promo_code_id' =>  $request->promo_code_id ?? null,
                // 'invoice_id'    =>  $request->invoice_id ?? null,
                'status'        => $request->booking_type == 'cash' ? 'pending' : 'not_paid',
                'price'         => DoctorBranch::find($request->branch_id)->price,
            ]);

            if ($request->booking_type == 'cash') {
                // if ($request->relative_id) {
                //     $patient_name = Relative::find($request->relative_id)->name;
                // } else {
                $patient_name = $patient->name;
                // }

                $info = ['title' => 'New Booking', 'body' => "$patient_name has booked clinc booking (pay cash)"];
                run_push_notification($request->doctor_id, 'doctor', $info);
            }

            if ($request->invoice) {
                $booking->invoices()->create([
                    'invoice_id' => $request->invoice['invoice_id'],
                    'invoice_key' => $request->invoice['invoice_key'],
                    'payment_data' => $request->invoice['payment_data'],
                ]);
            }


            if ($request['files_ids'] && count($request['files_ids']) > 0) {
                foreach ($request['files_ids'] as $file_id) {
                    $media = Media::find($file_id);
                    if ($media) {
                        $media->model_type = SpecializationBooking::class;
                        $media->model_id = $booking->id;
                        $media->save();
                    }
                }
            }



            // return $this->returnData(SpecializationBookingResource::make($booking), [helperTrans('api.Specialization booking successfully')]);
        }

        if ($booking) {
            $promoCode = PromoCode::find($request->promo_code_id);
            if ($promoCode && $promoCode->is_active) {
                $promoCode->current_redemptions++;
                $promoCode->save();

                // If the usage limit per patient is reached, set the is_active to false
                if ($promoCode->current_redemptions == $promoCode->max_redemptions) {
                    $promoCode->is_active = false;
                    $promoCode->save();
                }
            }
        }

        $token = 'eHKHiIERRSqiSQBH_q-qVz:APA91bELuJpU218uH02kixSOme_95JQ1WwH77J3NOmBvxwviIrRTp6tKU17KZt3yvFboKJMJdgSgU6uIAu9qa9An7KKxRQmdhnF2Yrqai6BKeIcHyPNnWfc';

        $data =
            [
                'user_id' => $booking->patient_id,
                'priority' => 'high',
                'type' => 'test',
                'android_channel_id' => 'pushnotificationapp'
            ];
        $firebase = (new \Kreait\Firebase\Factory())->withServiceAccount(public_path('firebase.json'))
            ->createMessaging();


        $message = \Kreait\Firebase\Messaging\CloudMessage::withTarget('token', $token)
            ->withNotification(['title' => 'accept_booking', 'body' => 'accept_booking', 'data' => $data, 'sound' => true])
            ->withData([]);
        $firebase->send($message);

        return $this->returnData(SpecializationBookingResource::make($booking), [helperTrans('api.Specialization booking successfully')]);
    }

    public function make_provider_specialization_booking(Request $request)
    {
        $patient = auth('patient')->user();
        //date from_time to_time descriptions location laboratory_id pharmacy_id radiology_center_id hospital_id
        if ($request->laboratory_id == null && $request->pharmacy_id == null && $request->radiology_center_id == null && $request->hospital_id == null) {
            $validator = Validator::make(
                $request->all(),
                [
                    'laboratory_id' => 'required',
                    'pharmacy_id' => 'required',
                    'radiology_center_id' => 'required',
                    'hospital_id' => 'required',
                ],
                []
            );
            if ($validator->fails()) {
                return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
            }
        }
        $laboratory_id = $request->laboratory_id ?? null;
        $pharmacy_id = $request->pharmacy_id ?? null;
        $radiology_center_id = $request->radiology_center_id ?? null;
        $hospital_id = $request->hospital_id ?? null;

        $validator = Validator::make(
            $request->all(),
            [
                'date'                      => 'required',
                'from_time'                 => 'required',
                'to_time'                   => 'required',
                'description'               => 'nullable',
                'insurance_company_id'      => 'nullable',
                'other_phone_num'           => 'required',
                'location'                  => 'required',
                'booking_type'              => 'required|in:insurance,individual,cash_on_delivery,cash',
                'doc'                       => 'nullable|array',
                'doc.*'                     => 'required|file',
            ],
            []
        );

        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $booking = ProviderSpecializationBooking::create([
            'id'   => getNextSharedId(),
            'patient_id'            =>  $patient->id,
            'date'                  =>  $request->date,
            'from_time'             =>  $request->from_time,
            'to_time'               =>  $request->to_time,
            'description'           =>  $request->description,
            'location'              =>  $request->location,
            'other_phone_num'       =>  $request->other_phone_num,
            'insurance_company_id'  =>  $request->insurance_company_id ?? null,
            'laboratory_id'         =>  $laboratory_id,
            'pharmacy_id'           =>  $pharmacy_id,
            'radiology_center_id'   =>  $radiology_center_id,
            'hospital_id'           =>  $hospital_id,
        ]);

        if ($request->doc)
            foreach ($request->doc as $doc) {
                $doc_file = $this->uploadFiles('provider_specialization_bookings', $doc, null);

                ProviderSpecializationBookingDocs::create([
                    'booking_id' => $booking->id,
                    'doc' => $doc_file,
                ]);
            }

        return $this->returnData(ProviderSpecializationBookingResource::make($booking), [helperTrans('api.Provider Specialization booking successfully')]);
    }

    public function available_offline_time($branch_id, $date)
    {
        $validator = Validator::make(
            [
                'branch_id' => $branch_id,
                'date' => $date
            ],
            [
                'branch_id' => 'required|exists:doctor_branches,id',
                'date' => 'required|date',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $day_en_order = Carbon::parse($date)->dayOfWeek;
        $day_orders = [
            0 => 2,
            1 => 3,
            2 => 4,
            3 => 5,
            4 => 6,
            5 => 7,
            6 => 1
        ];

        $date_day = $day_orders[$day_en_order];

        $provider_times = ProviderTime::select(['from_time', 'to_time'])->where([
            'provider_type' => 'doctor_branch',
            'provider_id'   => $branch_id,
            'day_id'        =>  $date_day
        ])->first();

        if (!$provider_times) {
            return $this->returnErrorNotFound(helperTrans('not found branch dates'));
        }

        $booking_times = SpecializationBooking::whereNotIn('status', ['complete', 'cancel'])->select(['time'])->where([
            'branch_id' => $branch_id,
            'date'      => $date
        ])->pluck('time')->map(function ($item) {
            return Carbon::parse($item)->format('H:i');
        })->toArray();

        $available_time = [];

        $start_time = Carbon::parse($provider_times['from_time']);
        $end_time =  Carbon::parse($provider_times['to_time']);
        $time = $start_time;

        while (true) {
            if ($time->addMinutes(30)->lessThanOrEqualTo($end_time)) {
                $available_time[] = $time->subMinutes(30)->format('H:i');
                $time->addMinutes(30);
            } else {
                break;
            }
        }
        return $this->returnData(array_values(array_diff($available_time, $booking_times)), '');
    }
    public function available_online_time($doctor_id, $date)
    {
        $validator = Validator::make(
            [
                'doctor_id' => $doctor_id,
                'date' => $date
            ],
            [
                'doctor_id' => 'required|exists:doctors,id',
                'date' => 'required|date',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $day_en_order = Carbon::parse($date)->dayOfWeek;
        $day_orders = [
            0 => 2,
            1 => 3,
            2 => 4,
            3 => 5,
            4 => 6,
            5 => 7,
            6 => 1
        ];

        $date_day = $day_orders[$day_en_order];

        $provider_times = ProviderTime::select(['from_time', 'to_time'])->where([
            'provider_type' => 'doctor',
            'provider_id'   => $doctor_id,
            'type' => 'online',
            'day_id'        =>  $date_day
        ])->first();

        if (!$provider_times) {
            return $this->returnErrorNotFound(helperTrans('not found doctor dates'));
        }

        $booking_times = SpecializationBooking::whereNotIn('status', ['complete', 'cancel'])->select(['time'])->where([
            'doctor_id' => $doctor_id,
            'date'      => $date
        ])->pluck('time')->map(function ($item) {
            return Carbon::parse($item)->format('H:i');
        })->toArray();

        $available_time = [];


        $start_time = Carbon::parse($provider_times['from_time']);
        $end_time =  Carbon::parse($provider_times['to_time']);
        $time = $start_time;

        while (true) {
            if ($time->addMinutes(30)->lessThanOrEqualTo($end_time)) {
                $available_time[] = $time->subMinutes(30)->format('H:i');
                $time->addMinutes(30);
            } else {
                break;
            }
        }
        return $this->returnData(array_values(array_diff($available_time, $booking_times)), '');
    }
    public function available_home_time($doctor_id, $date)
    {
        $validator = Validator::make(
            [
                'doctor_id' => $doctor_id,
                'date' => $date
            ],
            [
                'doctor_id' => 'required|exists:doctors,id',
                'date' => 'required|date',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $day_en_order = Carbon::parse($date)->dayOfWeek;
        $day_orders = [
            0 => 2,
            1 => 3,
            2 => 4,
            3 => 5,
            4 => 6,
            5 => 7,
            6 => 1
        ];

        $date_day = $day_orders[$day_en_order];

        $provider_times = ProviderTime::select(['from_time', 'to_time'])->where([
            'provider_type' => 'doctor',
            'provider_id'   => $doctor_id,
            'type' => 'home',
            'day_id'        =>  $date_day
        ])->first();

        if (!$provider_times) {
            return $this->returnErrorNotFound(helperTrans('not found doctor dates'));
        }

        $booking_times = SpecializationBooking::whereNotIn('status', ['complete', 'cancel'])->select(['time'])->where([
            'doctor_id' => $doctor_id,
            'date'      => $date
        ])->pluck('time')->map(function ($item) {
            return Carbon::parse($item)->format('H:i');
        })->toArray();

        $available_time = [];

        $start_time = Carbon::parse($provider_times['from_time']);
        $end_time =  Carbon::parse($provider_times['to_time']);
        $time = $start_time;

        while (true) {
            if ($time->addMinutes(30)->lessThanOrEqualTo($end_time)) {
                $available_time[] = $time->subMinutes(30)->format('H:i');
                $time->addMinutes(30);
            } else {
                break;
            }
        }
        return $this->returnData(array_values(array_diff($available_time, $booking_times)), '');
    }
}
