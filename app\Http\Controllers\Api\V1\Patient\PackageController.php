<?php

namespace App\Http\Controllers\Api\V1\Patient;

use App\Http\Controllers\Controller;
use App\Http\Resources\PackageResource;
use App\Http\Traits\Api_Trait;
use App\Models\PackaceRequest;
use App\Models\Package;
use App\Models\PackageRequest;
use App\Models\PackageRequestRelative;
use App\Models\PackageService;
use App\Models\Patient;
use App\Models\PatientSubscribe;
use App\Models\SubscribeRelative;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class PackageController extends Controller
{
    use Api_Trait;
    //
    public function index(Request $request)
    {


        $packages = Package::with(['mainServicesPackage.mainService'])->where('show_in_app', true)->get();

        return $this->returnData(PackageResource::collection($packages), [helperTrans('api.packages data')], 200);
    }

    public function subscribe(Request $request)
    {
        //add transactions

        $validator = Validator::make(
            $request->all(),
            [
                'package_id' => 'required|exists:packages,id',
                'invoice' => 'required',
                'invoice.invoice_id'    => 'unique:invoices,invoice_id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();
        $package = PatientSubscribe::where([
            // 'package_id' => $request->package_id,
            'patient_id' => $patient->id,
            'status' => 'active',
            // 'status' => 'not_paid',
            // 'invoice_id' => $request->invoice_id,
        ])->first();
        if ($package) {
            return $this->returnErrorValidation("already have package", 403);
        }

        $patien_subscribe = PatientSubscribe::create([
            'package_id' => $request->package_id,
            'patient_id' => $patient->id,
            // 'status' => 'not_paid',
            // 'invoice_id' => $request->invoice_id,
        ]);
        $patien_subscribe->invoices()->create([
            'invoice_id' => $request->invoice['invoice_id'] ?? null,
            'invoice_key' => $request->invoice['invoice_key'] ?? null,
            'payment_data' => $request->invoice['payment_data'] ?? null,
        ]);




        return $this->returnSuccessMessage([helperTrans('api.Complete Payment Now')], 200);
    }



    public function get_patient_subscribe($patient_id)
    {
        $patient_subscribe = PatientSubscribe::where('status', 'active')->where('patient_id', $patient_id)->get();

        if ($patient_subscribe->isNotEmpty()) {
            $packages = PackageResource::collection($patient_subscribe->map->package);

            return $this->returnData(
                $packages,
                [helperTrans('api.patient packages data')],
                200
            );
        }

        return $this->returnErrorNotFound([helperTrans('not found')]);
    }

    public function subscribes()
    {
        $patient = auth('patient')->user();
        $patient_subscribe = PatientSubscribe::where('status', 'active')->where('patient_id', $patient->id)->get();

        if ($patient_subscribe->isNotEmpty()) {
            $packages = PackageResource::collection([$patient_subscribe[0]->package]);

            return $this->returnData(
                $packages,
                [helperTrans('api.patient packages data')],
                200
            );
        }

        return $this->returnErrorNotFound([helperTrans('not found')]);
    }

    public function add_package_relative(Request $request)
    {
        $patient = auth('patient')->user();

        //validate relatives
        $validator = Validator::make(
            $request->all(),
            [
                'package_id' => 'required|exists:packages,id',
                'relatives'    => 'array',
                'relatives.*' => 'exists:relatives,id',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient_subscribe = PatientSubscribe::where('status', 'active')->where('patient_id', $patient->id)->where('package_id', $request->package_id)->first();
        if (!$patient_subscribe) {
            return $this->returnErrorValidation([helperTrans('api.not subscribed')], 403);
        }
        if ($patient_subscribe->relatives()->count() >= $patient_subscribe->package->max_relative) {
            return $this->returnErrorValidation([helperTrans('api.max relative')], 403);
        }
        foreach ($request->relatives as $relative_id) {

            SubscribeRelative::firstOrCreate([
                'patient_subscribe_id' => $patient_subscribe->id,
                'patient_id' => $patient->id,
                'relative_id' => $relative_id
            ]);
        }
        return $this->returnSuccessMessage([helperTrans('api.subscribed successfully')], 200);
    }
    public function remove_package_relative(Request $request)
    {
        $patient = auth('patient')->user();

        //validate relatives
        $validator = Validator::make(
            $request->all(),
            [
                'package_id' => 'required|exists:packages,id',
                'relative_id'    => 'required|exists:relatives,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient_subscribe = PatientSubscribe::where('status', 'active')->where('patient_id', $patient->id)->where('package_id', $request->package_id)->first();

        if (!$patient_subscribe) {
            return $this->returnErrorValidation([helperTrans('api.not subscribed')], 403);
        }

        SubscribeRelative::where([
            'patient_subscribe_id' => $patient_subscribe->id,
            'patient_id' => $patient->id,
            'relative_id' => $request->relative_id
        ])?->delete();


        return $this->returnSuccessMessage([helperTrans('api.deleted successfully')], 200);
    }

    public function request_package(Request $request)
    {
        $patient = auth('patient')->user();

        //validate relatives
        $validator = Validator::make(
            $request->all(),
            [
                'package_id' => 'required|exists:packages,id',
                'relatives'    => 'array',
                'relatives.*' => 'exists:relatives,id',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }
        $package_request = PackageRequest::create([
            'patient_id' => $patient->id,
            'package_id' => $request->package_id
        ]);

        foreach ($request->relatives as $relative_id) {
            PackageRequestRelative::create([
                "package_request_id" => $package_request->id,
                "relative_id" => $relative_id
            ]);
        }

        return $this->returnSuccessMessage([helperTrans('api.subscribed successfully')], 200);
    }
}
