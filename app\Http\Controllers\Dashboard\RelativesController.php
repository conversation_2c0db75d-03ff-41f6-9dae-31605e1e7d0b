<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\RelativeRequest;
use App\Http\Resources\Dashboard\RelativeResource;
use App\Models\Relative;
use App\Http\Traits\Api_Trait;
use Illuminate\Http\Request;

class RelativesController extends Controller
{
    use Api_Trait;

    /**
     * Display a listing of relatives with filtering and pagination
     */
    public function index(Request $request)
    {
        $relatives = Relative::with(['relationship', 'patient', 'nationality', 'country', 'city', 'area'])
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                        ->orWhere('phone', 'like', '%' . $request->search . '%')
                        ->orWhere('national_id', 'like', '%' . $request->search . '%')
                        ->orWhere('external_id', 'like', '%' . $request->search . '%')
                        ->orWhereHas('patient', function ($patientQuery) use ($request) {
                            $patientQuery->where('name', 'like', '%' . $request->search . '%');
                        })
                        ->orWhereHas('relationship', function ($relationshipQuery) use ($request) {
                            $relationshipQuery->where('name', 'like', '%' . $request->search . '%');
                        });
                });
            })
            // ->when($request->name, function ($query) use ($request) {
            //     $query->where('name', 'like', '%' . $request->name . '%');
            // })
            // ->when($request->phone, function ($query) use ($request) {
            //     $query->where('phone', 'like', '%' . $request->phone . '%');
            // })
            // ->when($request->status, function ($query) use ($request) {
            //     $query->where('status', $request->status);
            // })
            // ->when($request->gender, function ($query) use ($request) {
            //     $query->where('gender', $request->gender);
            // })
            // ->when($request->relationship_id, function ($query) use ($request) {
            //     $query->where('relationship_id', $request->relationship_id);
            // })
            ->when($request->patient_id, function ($query) use ($request) {
                $query->where('patient_id', $request->patient_id);
            })
            ->when($request->city_id, function ($query) use ($request) {
                $query->where('city_id', $request->city_id);
            })
            // ->when($request->area_id, function ($query) use ($request) {
            //     $query->where('area_id', $request->area_id);
            // })
            // ->when($request->nationality_id, function ($query) use ($request) {
            //     $query->where('nationality_id', $request->nationality_id);
            // })
            // ->when($request->is_smoking !== null, function ($query) use ($request) {
            //     $query->where('is_smoking', $request->is_smoking);
            // })
            // ->when($request->is_alcoholic !== null, function ($query) use ($request) {
            //     $query->where('is_alcoholic', $request->is_alcoholic);
            // })
            // ->when($request->join_subscribe !== null, function ($query) use ($request) {
            //     $query->where('join_subscribe', $request->join_subscribe);
            // })
            // ->when($request->has_phone !== null, function ($query) use ($request) {
            //     if ($request->has_phone) {
            //         $query->whereNotNull('phone')->where('phone', '!=', '');
            //     } else {
            //         $query->where(function ($q) {
            //             $q->whereNull('phone')->orWhere('phone', '');
            //         });
            //     }
            // })
            // ->when($request->has_national_id !== null, function ($query) use ($request) {
            //     if ($request->has_national_id) {
            //         $query->whereNotNull('national_id')->where('national_id', '!=', '');
            //     } else {
            //         $query->where(function ($q) {
            //             $q->whereNull('national_id')->orWhere('national_id', '');
            //         });
            //     }
            // })
            ->orderBy($request->sort_by ?? 'name', $request->sort_direction ?? 'asc')
            ->paginate($request->per_page ?? 25);

        return $this->paginatedResponse(
            RelativeResource::collection($relatives),
            helperTrans('api.Relatives Data'),
            200
        );
    }

    /**
     * Store a newly created relative
     */
    public function store(RelativeRequest $request)
    {
        $relative = Relative::create($request->validated());

        return $this->returnData(
            new RelativeResource($relative->load(['relationship', 'patient', 'nationality', 'country', 'city', 'area'])),
            [helperTrans('api.Relative Created Successfully')],
            201
        );
    }

    /**
     * Display the specified relative
     */
    public function show($id)
    {
        $relative = Relative::with(['relationship', 'patient', 'nationality', 'country', 'city', 'area'])->find($id);
        if (!$relative) {
            return $this->returnError(helperTrans('api.Relative Not Found'), 404);
        }

        return $this->returnData(
            new RelativeResource($relative),
            [helperTrans('api.Relative Data')],
            200
        );
    }

    /**
     * Update the specified relative
     */
    public function update(RelativeRequest $request, $id)
    {
        $relative = Relative::find($id);
        if (!$relative) {
            return $this->returnError(helperTrans('api.Relative Not Found'), 404);
        }

        $relative->update($request->validated());

        return $this->returnData(
            new RelativeResource($relative->load(['relationship', 'patient', 'nationality', 'country', 'city', 'area'])),
            [helperTrans('api.Relative Updated Successfully')],
            200
        );
    }

    /**
     * Remove the specified relative
     */
    public function destroy($id)
    {
        $relative = Relative::find($id);
        if (!$relative) {
            return $this->returnError(helperTrans('api.Relative Not Found'), 404);
        }

        $relative->delete();

        return $this->returnSuccessMessage(
            helperTrans('api.Relative Deleted Successfully'),
            200
        );
    }

    /**
     * Get relatives statistics
     */
    public function statistics()
    {
        $stats = [
            'total_relatives' => Relative::count(),
            'active_relatives' => Relative::where('status', 'active')->count(),
            'inactive_relatives' => Relative::where('status', 'inactive')->count(),
            'male_relatives' => Relative::where('gender', 'male')->count(),
            'female_relatives' => Relative::where('gender', 'female')->count(),
            'smoking_relatives' => Relative::where('is_smoking', true)->count(),
            'alcoholic_relatives' => Relative::where('is_alcoholic', true)->count(),
            'subscribed_relatives' => Relative::where('join_subscribe', true)->count(),
            'relatives_with_phone' => Relative::whereNotNull('phone')->where('phone', '!=', '')->count(),
            'relatives_with_national_id' => Relative::whereNotNull('national_id')->where('national_id', '!=', '')->count(),
            'relatives_by_relationship' => Relative::with('relationship')
                ->get()
                ->groupBy(function ($relative) {
                    return $relative->relationship ? $relative->relationship->name : 'No Relationship';
                })
                ->map(function ($relatives) {
                    return $relatives->count();
                }),
            'relatives_by_status' => Relative::selectRaw('status, COUNT(*) as count')
                ->groupBy('status')
                ->pluck('count', 'status'),
            'relatives_by_gender' => Relative::selectRaw('gender, COUNT(*) as count')
                ->groupBy('gender')
                ->pluck('count', 'gender'),
        ];

        return $this->returnData(
            $stats,
            [helperTrans('api.Relatives Statistics')],
            200
        );
    }
}
