<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\AreaResource;
use App\Http\Requests\Dashboard\AreaRequest;
use App\Http\Traits\Api_Trait;
use App\Models\Area;
use Illuminate\Http\Request;

class AreasController extends Controller
{
    use Api_Trait;

    /**
     * Display a listing of areas with filtering and pagination
     */
    public function index(Request $request)
    {
        $areas = Area::with(['city.governorate'])
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    foreach (languages() as $language) {
                        $q->orWhere("name->{$language->abbreviation}", 'like', '%' . $request->search . '%');
                    }
                    // Also search in city and governorate names
                    $q->orWhereHas('city', function ($cityQuery) use ($request) {
                        foreach (languages() as $language) {
                            $cityQuery->orWhere("name->{$language->abbreviation}", 'like', '%' . $request->search . '%');
                        }
                    });
                    $q->orWhereHas('city.governorate', function ($govQuery) use ($request) {
                        foreach (languages() as $language) {
                            $govQuery->orWhere("name->{$language->abbreviation}", 'like', '%' . $request->search . '%');
                        }
                    });
                });
            })
            ->when($request->name, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    foreach (languages() as $language) {
                        $q->orWhere("name->{$language->abbreviation}", 'like', '%' . $request->name . '%');
                    }
                });
            })
            ->when($request->name_ar, function ($query) use ($request) {
                $query->where('name->ar', 'like', '%' . $request->name_ar . '%');
            })
            ->when($request->name_en, function ($query) use ($request) {
                $query->where('name->en', 'like', '%' . $request->name_en . '%');
            })
            ->when($request->city_id, function ($query) use ($request) {
                $query->where('city_id', $request->city_id);
            })
            ->when($request->governorate_id, function ($query) use ($request) {
                $query->whereHas('city', function ($cityQuery) use ($request) {
                    $cityQuery->where('governorate_id', $request->governorate_id);
                });
            })
            ->when($request->has_city !== null, function ($query) use ($request) {
                if ($request->has_city) {
                    $query->whereNotNull('city_id');
                } else {
                    $query->whereNull('city_id');
                }
            })
            ->when($request->has_arabic !== null, function ($query) use ($request) {
                if ($request->has_arabic) {
                    $query->whereNotNull('name->ar')->where('name->ar', '!=', '');
                } else {
                    $query->where(function ($q) {
                        $q->whereNull('name->ar')->orWhere('name->ar', '');
                    });
                }
            })
            ->when($request->has_english !== null, function ($query) use ($request) {
                if ($request->has_english) {
                    $query->whereNotNull('name->en')->where('name->en', '!=', '');
                } else {
                    $query->where(function ($q) {
                        $q->whereNull('name->en')->orWhere('name->en', '');
                    });
                }
            })
            ->orderBy($request->sort_by ?? 'name', $request->sort_direction ?? 'asc')
            ->paginate($request->per_page ?? 25);

        return $this->paginatedResponse(
            AreaResource::collection($areas),
            helperTrans('api.Areas Data'),
            200
        );
    }

    /**
     * Store a newly created area
     */
    public function store(AreaRequest $request)
    {
        $data = $request->validated();

        $area = Area::create($data);

        return $this->returnData(
            new AreaResource($area),
            [helperTrans('api.Area created successfully')],
            201
        );
    }

    /**
     * Display the specified area
     */
    public function show($id)
    {
        $area = Area::with(['city.governorate'])->find($id);
        if (!$area) {
            return $this->returnError(helperTrans('api.Area Not Found'), 404);
        }

        return $this->returnData(
            new AreaResource($area),
            [helperTrans('api.Area Data')],
            200
        );
    }

    /**
     * Update the specified area
     */
    public function update(AreaRequest $request, $id)
    {
        $area = Area::find($id);
        if (!$area) {
            return $this->returnError(helperTrans('api.Area Not Found'), 404);
        }

        $data = $request->validated();

        $area->update($data);

        return $this->returnData(
            new AreaResource($area),
            [helperTrans('api.Area updated successfully')],
            200
        );
    }

    /**
     * Remove the specified area
     */
    public function destroy($id)
    {
        $area = Area::find($id);
        if (!$area) {
            return $this->returnError(helperTrans('api.Area Not Found'), 404);
        }

        $area->delete();

        return $this->returnData(
            null,
            [helperTrans('api.Area deleted successfully')],
            200
        );
    }

    /**
     * Get areas statistics
     */
    public function statistics()
    {
        $stats = [
            'total_areas' => Area::count(),
            'areas_with_arabic' => Area::whereNotNull('name->ar')->where('name->ar', '!=', '')->count(),
            'areas_with_english' => Area::whereNotNull('name->en')->where('name->en', '!=', '')->count(),
            'areas_with_both_languages' => Area::whereNotNull('name->ar')
                ->where('name->ar', '!=', '')
                ->whereNotNull('name->en')
                ->where('name->en', '!=', '')
                ->count(),
            'areas_missing_translations' => Area::where(function ($query) {
                $query->where(function ($q) {
                    $q->whereNull('name->ar')->orWhere('name->ar', '');
                })->orWhere(function ($q) {
                    $q->whereNull('name->en')->orWhere('name->en', '');
                });
            })->count(),
            'areas_with_city' => Area::whereNotNull('city_id')->count(),
            'areas_without_city' => Area::whereNull('city_id')->count(),
            'areas_by_governorate' => Area::with(['city.governorate'])
                ->whereNotNull('city_id')
                ->get()
                ->groupBy(function ($area) {
                    return $area->city && $area->city->governorate ? $area->city->governorate->name : 'Unknown';
                })
                ->map(function ($areas) {
                    return $areas->count();
                }),
        ];

        return $this->returnData(
            $stats,
            [helperTrans('api.Areas Statistics')],
            200
        );
    }

    /**
     * Get areas for dropdown/select options
     */
    public function options(Request $request)
    {
        $areas = Area::select('id', 'name')
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    foreach (languages() as $language) {
                        $q->orWhere("name->{$language->abbreviation}", 'like', '%' . $request->search . '%');
                    }
                });
            })
            ->orderBy('name')
            ->limit($request->limit ?? 50)
            ->get()
            ->map(function ($area) {
                return [
                    'id' => $area->id,
                    'name' => $area->name,
                    'value' => $area->id,
                    'label' => $area->name,
                ];
            });

        return $this->returnData(
            $areas,
            [helperTrans('api.Areas Options')],
            200
        );
    }
}
