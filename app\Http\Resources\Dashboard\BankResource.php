<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BankResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'bank_name' => $this->bank_name,
            'bank_account_number' => $this->bank_account_number,
            'bank_iban' => $this->bank_iban,
            'bank_swift_code' => $this->bank_swift_code,
            'bank_branch_bank' => $this->bank_branch_bank,
            'bank_mobile_number' => $this->bank_mobile_number,
        ];
    }
}
