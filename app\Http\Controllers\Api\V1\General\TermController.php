<?php

namespace App\Http\Controllers\Api\V1\General;

use App\Http\Controllers\Controller;
use App\Models\Term;
use App\Http\Traits\Api_Trait;
use App\Http\Resources\TermResource;

class TermController extends Controller
{
    use Api_Trait;
    public function index()
    {
        $parents = Term::with('children')->parent()->get();
        return $this->returnData(TermResource::collection($parents), [helperTrans('api.term data')], 200);
    }
}
