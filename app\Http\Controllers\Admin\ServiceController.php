<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Service;
use App\Models\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Yajra\DataTables\Facades\DataTables;

class ServiceController extends Controller
{
    public function index(Request $request)
    {
        if (request()->ajax()) {
            $services = Service::with('serviceCategory');

            return DataTables::of($services)
                ->addColumn('action', function ($service) {
                    $html = '<div class="dropdown d-inline-block">
                    <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ri-more-fill align-middle"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item edit-item-btn" href="' . route('admin.services.edit', $service->id) . '"><i class="ri-pencil-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.edit') . '</a></li>
                        <li>
                            <a class="dropdown-item remove-item-btn" href="javascript:void(0)" data-id="' . $service->id . '">
                                <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.delete') . '
                            </a>
                        </li>
                    </ul>
                </div>';
                    return $html;
                })
                ->addColumn('name_ar', function ($service) {
                    return $service->getTranslation('name', 'ar');
                })
                ->addColumn('name_en', function ($service) {
                    return $service->getTranslation('name', 'en');
                })
                ->addColumn('category', function ($service) {
                    return $service->serviceCategory ? $service->serviceCategory->getTranslation('name', app()->getLocale()) : '';
                })


                ->rawColumns(['action'])
                ->make(true);
        }

        return view('Admin.CRUDS.services.index');
    }

    public function create()
    {
        $serviceCategories = ServiceCategory::all();
        return view('Admin.CRUDS.services.create', compact('serviceCategories'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'name.ar' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'service_category_id' => 'required|exists:service_categories,id',
            'duration' => 'required|integer|min:1',
            'status' => 'required|in:active,inactive',
            'description.ar' => 'nullable|string',
            'description.en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $service = new Service();
        $service->service_category_id = $validated['service_category_id'];
        $service->name = $validated['name'];
        $service->description = $validated['description'] ?? ['ar' => '', 'en' => ''];
        $service->duration = $validated['duration'];
        $service->status = $validated['status'] === 'active';

        if ($request->hasFile('image')) {
            $service->image = $request->file('image')->store('services', 'public');
        }

        $service->save();

        return redirect()
            ->route('admin.services.index')
            ->with('success', helperTrans('admin.service_created_successfully'));
    }

    public function edit(Service $service)
    {
        $serviceCategories = ServiceCategory::all();
        return view('Admin.CRUDS.services.edit', compact('service', 'serviceCategories'));
    }

    public function update(Request $request, Service $service)
    {
        $validated = $request->validate([
            'name.ar' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'service_category_id' => 'required|exists:service_categories,id',
            'duration' => 'required|integer|min:1',
            'status' => 'required|in:active,inactive',
            'description.ar' => 'nullable|string',
            'description.en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $service->service_category_id = $validated['service_category_id'];
        $service->name = $validated['name'];
        $service->description = $validated['description'] ?? ['ar' => '', 'en' => ''];
        $service->duration = $validated['duration'];
        $service->status = $validated['status'] === 'active';

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($service->image) {
                Storage::disk('public')->delete($service->image);
            }
            $service->image = $request->file('image')->store('services', 'public');
        }

        $service->save();

        return redirect()
            ->route('admin.services.index')
            ->with('success', helperTrans('admin.service_updated_successfully'));
    }

    public function destroy(Service $service)
    {
        if ($service->image) {
            Storage::disk('public')->delete($service->image);
        }

        $service->delete();

        return response()->json([
            'status' => true,
            'message' => helperTrans('admin.service_deleted_successfully')
        ]);
    }
}
