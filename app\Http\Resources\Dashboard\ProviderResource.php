<?php

namespace App\Http\Resources\Dashboard;

use App\Http\Resources\CityResource;
use App\Http\Resources\CountryResource;
use App\Http\Resources\GovernorateResource;
use App\Http\Resources\ProviderCategoryResource;
use App\Http\Resources\SpecializationResource;
use App\Http\Resources\Dashboard\ClassResource;
use App\Http\Resources\ProviderTimeResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class ProviderResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        if ($this->provider_category_id == 1) {
            $image = new_get_file($this->image, 'lab');
        } elseif ($this->provider_category_id == 2) {
            $image = new_get_file($this->image, 'center');
        } elseif ($this->provider_category_id == 3) {
            $image = new_get_file($this->image, 'pharmacy');
        } elseif ($this->provider_category_id == 4) {
            $image = new_get_file($this->image, 'hospital');
        } elseif ($this->provider_category_id == 5) {
            $image = new_get_file($this->image, 'polycenter');
        } elseif ($this->provider_category_id == 6) {
            $image = new_get_file($this->image, 'specialized_center');
        } elseif ($this->provider_category_id == 7) {
            $image = new_get_file($this->image, 'optical_center');
        } elseif ($this->provider_category_id == 8) {
            $image = new_get_file($this->image, 'specialized_doctor');
        }
        $doctors = $this->branches()->first()?->doctors()->get()->map(function ($doctor) {
            return [
                'id' => $doctor->id,
                'name_ar' => $doctor->getTranslation('name', 'ar'),
                'name_en' => $doctor->getTranslation('name', 'en'),
                'nickname_ar' => $doctor->getTranslation('nickname', 'ar'),
                'nickname_en' => $doctor->getTranslation('nickname', 'en'),
                'gender' => $doctor->gender,
                'experience_years' => $doctor->experience_years,
                'experience' => $doctor->doctor_level?->only('id', 'name'),
                'about_ar' => $doctor->getTranslation('about', 'ar'),
                'about_en' => $doctor->getTranslation('about', 'en'),
                'location_ar' => $doctor->getTranslation('location', 'ar'),
                'location_en' => $doctor->getTranslation('location', 'en'),
                'phone' => $doctor->phone,
                'email' => $doctor->email,
                'specialization' => $doctor->specialization?->only('id', 'name'),
                'sub_specialization' => $doctor->sub_specialization?->only('id', 'name'),
                'image' => new_get_file($doctor->image, 'person'),
                'created_at' => Carbon::parse($doctor->created_at)->toDateString(),
            ];
        });
        $branches = $this->branches()->select('id', 'name', 'location', 'phone', 'tel1', 'status', 'degree', 'created_at')->get()->map(function ($branch) use ($request) {
            return [
                'id' => $branch->id,
                'name_ar' => $branch->getTranslation('name', 'ar'),
                'name_en' => $branch->getTranslation('name', 'en'),
                'location_ar' => $branch->getTranslation('location', 'ar'),
                'location_en' => $branch->getTranslation('location', 'en'),
                'phone' => $branch->phone,
                'tel' => $branch->tel1,
                'status' => $branch->status,
                'degree' => $branch->degree,
                'created_at' => $branch->created_at->toDateString(),
                'times' => ProviderTimeResource::collection($branch->times()->get()),
            ];
        });
        return [
            'id' => (int)$this->id,
            'name' => $this->name,
            'imported_file_id' => $this->imported_file_id,
            'image' => $image ?? null,
            'phone' => $this->phone,
            'email' => $this->email,
            'title' => $this->title,
            'class' => $this->class,
            'website_link' => $this->website_link,
            'discount' => $this->discount,
            'surgery_discount' => $this->surgery_discount,
            'special_offer' => $this->special_offer,
            'about' => (is_json($this->about) && is_array($decoded = json_decode($this->about, true)) && isset($decoded[$request->header('lang')]))
                ? $decoded[$request->header('lang')]
                : $this->about,
            'latitude' => $this->branches->where('degree', 'main')->first()?->latitude,
            'longitude' => $this->branches->where('degree', 'main')->first()?->longitude,
            'location' => (is_json($this->location) && is_array($decoded = json_decode($this->location, true)) && isset($decoded[$request->header('lang')]))
                ? $decoded[$request->header('lang')]
                : $this->location,
            'working_days' => $this->working_days,
            'area' => $this->area,
            'city' => new CityResource($this->city),
            'governorate' => new GovernorateResource($this->governorate),
            'country' => new CountryResource($this->country),
            'language' => is_string($this->language)
                ? json_decode($this->language, true)
                : $this->language,
            'work_number' => $this->work_number,
            'whatsapp_number' => $this->whatsapp_number,
            'tel1' => $this->tel1,
            'tel2' => $this->tel2,
            'email' => $this->email,
            'show_in_app' => $this->show_in_app,
            'class' => ClassResource::make($this->class),
            'provider_category' => new ProviderCategoryResource($this->provider_category),
            'provider_categor_id' => $this->provider_category_id,
            'created_at' => Carbon::parse($this->created_at)->toDateString(),
            'status' => $branches->first()['status'] ?? null,
            'total_branches' => $branches->count(),
            'branches' => $branches,
            'doctors' => $doctors,
            // 'specialization' => $firstDoctor?->specialization?->only('id', 'name'),
            'specializations' => SpecializationResource::collection($this->specializations),
            'attachments' => $this->attachments,
        ];
    }
}
