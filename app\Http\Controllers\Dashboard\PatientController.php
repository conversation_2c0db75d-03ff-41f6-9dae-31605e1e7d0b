<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Traits\Api_Trait;
use App\Models\Patient;
use App\Http\Resources\Dashboard\PatientResource;
use App\Http\Requests\Dashboard\PatientRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class PatientController extends Controller
{
    use Api_Trait;

    public function index(Request $request)
    {
        $patients = Patient::with(['nationality', 'governorate', 'city', 'country', 'partner'])
            ->when($request->search, function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%')
                    ->orWhere('phone', 'like', '%' . $request->search . '%')
                    ->orWhere('email', 'like', '%' . $request->search . '%');
            })
            ->when($request->status !== null, function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            ->when($request->gender, function ($query) use ($request) {
                $query->where('gender', $request->gender);
            })
            ->when($request->partner_id, function ($query) use ($request) {
                $query->where('partner_id', $request->partner_id);
            })
            ->paginate($request->per_page ?? 25);

        return $this->paginatedResponse(
            PatientResource::collection($patients),
            helperTrans('api.Patient Data'),
            200
        );
    }

    public function store(PatientRequest $request)
    {
        $data = $request->validated();

        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $patient = Patient::create($data);

        return $this->returnData(
            new PatientResource($patient->load(['nationality', 'governorate', 'city', 'country', 'partner'])),
            [helperTrans('api.Patient created successfully')],
            201
        );
    }

    public function show($id)
    {
        $patient = Patient::find($id);
        if (!$patient) {
            return $this->returnError(helperTrans('api.Patient Not Found'), 404);
        }
        $patient->load(['nationality', 'governorate', 'city', 'country', 'partner']);

        return $this->returnData(
            new PatientResource($patient),
            [helperTrans('api.Patient Data')],
            200
        );
    }

    public function update(PatientRequest $request, $id)
    {
        $patient = Patient::find($id);
        if (!$patient) {
            return $this->returnError(helperTrans('api.Patient Not Found'), 404);
        }
        $data = $request->validated();

        // Hash password if provided
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }

        $patient->update($data);

        return $this->returnData(
            new PatientResource($patient->load(['nationality', 'governorate', 'city', 'country', 'partner'])),
            [helperTrans('api.Patient updated successfully')],
            200
        );
    }

    public function destroy($id)
    {
        $patient = Patient::find($id);
        if (!$patient) {
            return $this->returnError(helperTrans('api.Patient Not Found'), 404);
        }
        $patient->delete();

        return $this->returnData(
            null,
            [helperTrans('api.Patient deleted successfully')],
            200
        );
    }
}
