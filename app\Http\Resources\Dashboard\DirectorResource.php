<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class DirectorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'name' => $this->name,
            'phone' => $this->phone,
            'email' => $this->email,
            'role_type' => $this->role_type,
            'imported_file_id' => $this->imported_file_id,
            'created_at' => Carbon::parse($this->created_at)->toDateString(),
        ];
    }
}
