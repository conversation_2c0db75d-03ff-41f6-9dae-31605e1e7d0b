<?php

namespace App\Http\Controllers\Admin;

use App\Exceptions\DoctoriaException;
use App\Helpers\Template;
use App\Http\Controllers\Controller;
use App\Http\Traits\Api_Trait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class TemplateController extends Controller
{
    use Api_Trait;
    public function show(string $filename){
        try {
            $attributes = getFileAttributes($filename);

            // Debug information - remove this after testing
            Log::info('Template download request', [
                'filename' => $filename,
                'file_path' => $attributes['path'],
                'file_exists' => file_exists($attributes['path']),
                'file_size' => file_exists($attributes['path']) ? filesize($attributes['path']) : 0
            ]);

            return $this->respondDownload($attributes, $filename);
        } catch (\Exception $e) {
            Log::error('Template download error', [
                'filename' => $filename,
                'error' => $e->getMessage()
            ]);

            return response()->json([
                'error' => 'File download failed: ' . $e->getMessage(),
                'filename' => $filename
            ], 404);
        }
    }

    public function debug(string $filename)
    {
        try {
            $attributes = getFileAttributes($filename);

            return response()->json([
                'filename' => $filename,
                'file_path' => $attributes['path'],
                'file_exists' => file_exists($attributes['path']),
                'file_size' => file_exists($attributes['path']) ? filesize($attributes['path']) : 0,
                'headers' => $attributes['headers'],
                'readable' => file_exists($attributes['path']) ? is_readable($attributes['path']) : false
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => $e->getMessage(),
                'filename' => $filename
            ], 404);
        }
    }
}
