<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NewBookingService extends Model
{
    use HasFactory;
    protected $table = 'new_booking_services';
    protected $fillable = [
        'new_booking_id',
        'service_id',
        'quantity',
        'price',
    ];

    public function newBooking()
    {
        return $this->belongsTo(NewBooking::class);
    }

    public function service()
    {
        return $this->belongsTo(Service::class);
    }
}
