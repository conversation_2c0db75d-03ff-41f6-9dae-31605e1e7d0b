<?php

namespace App\Http\Controllers\Api\V1\Patient;

use App\Http\Controllers\Controller;
use App\Http\Resources\DoctorLessResource;
use App\Http\Traits\Api_Trait;
use App\Models\Doctor;
use App\Models\DoctorFav;
use Illuminate\Http\Request;

class FavDoctorController extends Controller
{
    use Api_Trait;

    public function index()
    {
        $patient = auth('patient')->user();

        // get fav doctors ids only
        $doctorIds = DoctorFav::where('patient_id', $patient->id)->pluck('doctor_id')->toArray();

        // get doctors by ids
        $doctors = Doctor::whereIn('id', $doctorIds)->get();

        // return $doctors;
        return $this->returnData(DoctorLessResource::collection($doctors), [helperTrans('api.Favorite Doctors')]);
    }


    public function addToFav($id)
    {
        $patient = auth('patient')->user();
        $doctor = Doctor::find($id);
        if (!$doctor) {
            return $this->returnError([helperTrans('api.Doctor Not Found')]);
        }
        $checkdoctor = DoctorFav::where('doctor_id', (int) $id)->where('patient_id', $patient->id)->first();
        if ($checkdoctor) {
            $checkdoctor->delete();
            return $this->returnSuccessMessage([helperTrans('api.Remove Doctor From Favorites List')]);
        } else {
            DoctorFav::create([
                'doctor_id' => $id,
                'patient_id' => $patient->id,
            ]);
            return $this->returnSuccessMessage([helperTrans('api.Doctor added to favorite')]);
        }
    }

    public function delete($doctor_id)
    {

        $patient = auth('patient')->user();

        $favorite = DoctorFav::where('doctor_id', $doctor_id)->where('patient_id', $patient->id)->first();

        if ($favorite && $favorite->delete()) {
            return $this->returnSuccessMessage([helperTrans('api.Doctor removed from favorite')]);
        }
        return $this->returnError([helperTrans('api.Doctor Not Found in Favorites List')]);
    }
}
