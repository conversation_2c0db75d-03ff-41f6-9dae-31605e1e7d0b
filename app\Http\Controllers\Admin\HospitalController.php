<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\HospitalRequest;
use App\Http\Traits\ResponseTrait;
use App\Http\Traits\Upload_Files;
use App\Models\Category;
use App\Models\City;
use App\Models\Delegate;
use App\Models\DoctorContract;
use App\Models\Experience;
use App\Models\Governorate;
use App\Models\Hospital;
use App\Models\HospitalConstact;
use App\Models\ProviderCategory;
use App\Models\Specialization;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;

class HospitalController extends Controller
{
    //
    use  ResponseTrait,Upload_Files;

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $admins = Hospital::query()->where('type','hosptial')->latest();
            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {
                    $edit = '';
                    $delete = '';
                    return '
                    <a href="'. route('hospitals.edit', $admin->id) .'" ' . $edit . '  class="btn rounded-pill btn-primary waves-effect waves-light"
                                data-id="' . $admin->id . '"
                        <span class="svg-icon svg-icon-3">
                            <span class="svg-icon svg-icon-3">
                                <i class="las la-edit"></i>
                            </span>
                        </span>
                    </a>
                    <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete" data-id="' . $admin->id . '">
                        <span class="svg-icon svg-icon-3">
                            <span class="svg-icon svg-icon-3">
                                <i class="las la-trash-alt"></i>
                            </span>
                        </span>
                    </button>
                    <a  '.$edit. ' href="'. route('hospital_branches.index', ['id'=>$admin->id]) .'" class="btn rounded-pill btn-primary waves-effect waves-light">Braches</a>
            ';
                })
                ->editColumn('name', function ($row) {
                    return $row->name;
                })
                ->editColumn('desc', function ($row) {
                    return $row->desc;
                })
                ->editColumn('location', function ($row) {
                    if (is_json($row->location)) {
                        $locationData = json_decode($row->location, true);
                        $locationAr = $locationData['ar'] ?? 'N/A';
                        $locationEn = $locationData['en'] ?? 'N/A';
                        return '<span style="color:red;">' . htmlspecialchars($locationAr) . '</span> / <span style="color:blue;">' . htmlspecialchars($locationEn) . '</span>';
                    }if(is_string($row->location)){
                        return htmlspecialchars($row->location);
                    }
                })
                ->editColumn('image', function ($admin) {
                    return '
                        <a data-fancybox="" href="' . get_file($admin->image) . '">
                                <img height="60px" src="' . get_file($admin->image) . '">
                            </a>
                            ';
                })
                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);


        }
        $providerName ='Hospital';
        return view('Admin.CRUDS.hospital.index',compact('providerName'));    }


    public function create()
    {
        return view('Admin.CRUDS.hospital.parts.create');
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try{
            $data = $request->validationData();
            $workingHours = [];
            if ($request->has('working_days')) {
                foreach ($request->working_days as $day) {
                    $workingHours[$day] = [
                        'start_time' => $request->input('start_time')[$day] ?? null,
                        'end_time' => $request->input('end_time')[$day] ?? null,
                    ];
                }
            }
            $exceptedColumn = [
                'work_time',
                'start_time',
                'end_time',
                'working_days',
                'contract_start_date',
                'contract_end_date',
                'attach_contract',
                'attach_documents',
                'attach_price_list',
                'price_list_discount',
                'contract_note',
                'bank_name',
                'bank_account_number',
                'bank_iban',
                'bank_swift_code',
                'bank_branch_bank',
                'bank_mobile_number',
                'e_wallet_name',
                'e_wallet_mobile_number',
                'instapay_mobile_number',
                'instapay_email',
                'cheque_name',
                'working_hours',
                "price_list_hospital_consulation",
                "price_list_hospital_outpatient",
                "price_list_hospital_inpatient",
                "price_list_hospital_laboratory" ,
                "price_list_hospital_radiology" ,
                "price_list_hospital_discount_other" ,
                "price_list_doctor_visit",
                "price_list_nurse_visit",
                "price_list_xray",
                "price_list_ct",
                "price_list_mri",
                "price_list_dental",
                "price_list_glasses",
                "price_list_frame",
                "price_list_sessions",
                "price_list_hospital_other",
                "nickname_hospital_doctor" ,
                "specialization_hospital_id" ,
                "sub_specialization_id,",
                'clamis_due_date',
                'admin_fees',
                'name_1',
                'email_1',
                'phone_1',
                'name_2',
                'email_2',
                'phone_2',
            ];
            unset(
                $data['category_id'],
                $data['nickname'],
                $data['price_list_hematology'],
                $data['price_list_chemistry'],
                $data['price_list_hormones'],
                $data['price_list_serology'],
                $data['home_care_gross_price'],
                $data['home_care_discount'],
                $data['home_care_net_price'],
                $data['price_list_immunology'],
                $data['price_list_drug'],
                $data['price_list_microbiology'],
                $data['price_list_pathology'],
                $data['price_list_other'],
                $data['service_clinic_gross_price'],
                $data['service_clinic_discount'],
                $data['service_clinic_net_price'],
                $data['service_online_gross_price'],
                $data['service_online_discount'],
                $data['service_online_net_price'],
                $data['price_list_outpatient'],
                $data['price_list_intpatient'],
                $data['price_list_consultation'],
                $data['price_list_laboratory'],
                $data['price_list_radiologe'],
                $data['price_list_exception'],
                $data['gender'],
                $data['specialization_id'],
                $data['price_list_acutemedicine_local'],
                $data['price_list_chronicmedication_local'],
                $data['price_list_cosmetics_local'],
                $data['price_list_otherservice_local'],
                $data['price_list_acutemedicine_imported'],
                $data['price_list_chronicmedication_imported'],
                $data['price_list_cosmetics_imported'],
                $data['price_list_otherservice_imported'],
                $data["price_list_xray"],
                $data["price_list_mri"],
                $data["price_list_ct"],
                $data["price_list_other"]
            );
            if($request->image){
                $image = json_decode($request->image, true);
                $imagedata = $image['image'] ?? null;
            }
            $data['password'] = bcrypt($request->password);
            if (is_array($data['name'])) {
                $data['name'] = json_encode($data['name']);
            }
            if (is_array($data['address'])) {
                $data['address'] = $data['address'];
            }
            if (is_array($data['about'])) {
                $data['about'] = $data['about'];
            }
            $data['delegate'] = $request->has('delegate') ? 1 : 0;
            $datafinal = Arr::except($data, $exceptedColumn);
            $hospital = Hospital::create([
                "location" => $request['address'],
                "image" => $imagedata,
                "type" => "hosptial",
                "provider_type" => "hospital",
                "name" => $request['name'],
                "email" => $request['email'],
                "phone" => $request['phone'],
                "private_number" => $request['private_number'],
                "password" => $request['password'],
                "website" => $request['website'],
                "governorate_id" => $request['governorate_id'],
                "experience_id" => $request['experience_id'],
                "experience_years" => $request['experience_years'],
                "lang" => $request['lang'],
                "latitude" => $request['latitude'],
                "longitude" => $request['longitude'],
                "is_popular" => "0",
                "desc" => $request['about'],
                "about_us" => $request['about'],
                "delegate" => 0
            ]);
            $code = "hospital_$hospital->id";
            $hospital->update(['code' => $code]);
            $contractData = [
                // 'working_hours' => json_encode($workingHours),
                'hospital_id' => $hospital->id,
                'contract_start_date' => $request->contract_start_date,
                'contract_end_date' => $request->contract_end_date,
                'attach_contract' => $request->attach_contract,
                'attach_documents' => $request->attach_documents,
                'attach_price_list' => $request->attach_price_list,
                'contract_note' => $request->contract_note,
                'clamis_due_date' => $request->clamis_due_date,
                'admin_fees' => $request->admin_fees,
                'bank_name' => $request->bank_name,
                'bank_account_number' => $request->bank_account_number,
                'bank_iban' => $request->bank_iban,
                'bank_swift_code' => $request->bank_swift_code,
                'bank_branch_bank' => $request->bank_branch_bank,
                'bank_mobile_number' => $request->bank_mobile_number,
                'e_wallet_name' => $request->e_wallet_name,
                'e_wallet_mobile_number' => $request->e_wallet_mobile_number,
                'instapay_mobile_number' => $request->instapay_mobile_number,
                'instapay_email' => $request->instapay_email,
                'cheque_name' => $request->cheque_name,
            ];
            $contract = DoctorContract::create($contractData);
            $contractDatadetails = [
                'contract_id' => $contract->id ,
                'consultation' => $request->price_list_hospital_consulation,
                'outpatient' => $request->price_list_hospital_outpatient,
                'inpatient' => $request->price_list_hospital_inpatient,
                'laboratory' => $request->price_list_hospital_laboratory,
                'radiologe' => $request->price_list_hospital_radiology,
                'discount_other_service' => $request->price_list_hospital_discount_other,
                'doctor_visit' => $request->price_list_doctor_visit,
                'nurse_visit' => $request->price_list_nurse_visit,
                'other_service' => $request->price_list_hospital_other,
            ];
            HospitalConstact::create($contractDatadetails);
            if ($request->category_id)
            foreach ($request->category_id as $category_id)
            {
                ProviderCategory::create([
                    'provider_id'=>$hospital->id,
                    'category_id'=>$category_id,
                    'provider_type'=>'hospital',
                ]);
            }
            if ($request->has('delegate')) {
                Delegate::create([
                    'hospital_id ' => $hospital->id,
                    'name_1' => $request->name_1,
                    'email_1' => $request->email_1,
                    'phone_1' => $request->phone_1,
                    'name_2' => $request->name_2,
                    'email_2' => $request->email_2,
                    'phone_2' => $request->phone_2,
                ]);
            }
            DB::commit();
            return redirect()->route('hospitals.index')->with('success', 'created success');
        }catch (\Exception $e){
            DB::rollBack();
            return $this->addResponse($e->getMessage(), false);
        }
    }

    public function edit($id) {
        $row=Hospital::findOrFail($id);
        $specializations=Specialization::get();
        $sub_specializations=Specialization::where('parent_id',$row->specialization_id)->get();

        $categories=Category::whereIn('slug', ['doctors', 'visit_doctor', 'consultation'])->get();
        $governorates=Governorate::get();
        $cities=City::where('governorate_id',$row->governorate_id)->get();
        $experiences=Experience::get();


        $categoriesIdes=ProviderCategory::where('provider_id',$row->id)->where('provider_type','doctor')->pluck('category_id')->toArray();


        return view('Admin.CRUDS.hospital.parts.edit', compact('row','specializations','categories','categoriesIdes','governorates','cities','experiences','sub_specializations'));

    }

    public function update(HospitalRequest $request, $id )
    {
        $row=Hospital::findOrFail($id);
        $data = $request->validationData();
        if($request->image){
            $image = json_decode($request->image, true);
            $imagedata = $image['image'] ?? null;
            $data['image'] = $imagedata;
        }
        $row->update($data);
        return redirect()->back()->with('success','updated');

    }


    public function destroy($id)
    {
        $row = Hospital::findOrFail($id);

        if (file_exists($row->image)) {
            unlink($row->image);
        }

        $row->delete();

        return $this->deleteResponse();
    }//end fun


}
