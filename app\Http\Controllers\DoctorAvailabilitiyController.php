<?php

namespace App\Http\Controllers;

use App\Models\DoctorAvailabilitiy;
use App\Http\Requests\StoreDoctorAvailabilitiyRequest;
use App\Http\Requests\UpdateDoctorAvailabilitiyRequest;

class DoctorAvailabilitiyController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        //
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        //
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreDoctorAvailabilitiyRequest $request)
    {
        //
    }

    /**
     * Display the specified resource.
     */
    public function show(DoctorAvailabilitiy $doctorAvailabilitiy)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(DoctorAvailabilitiy $doctorAvailabilitiy)
    {
        //
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateDoctorAvailabilitiyRequest $request, DoctorAvailabilitiy $doctorAvailabilitiy)
    {
        //
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(DoctorAvailabilitiy $doctorAvailabilitiy)
    {
        //
    }
}
