<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Traits\Api_Trait;
use App\Models\ServiceCategory;
use App\Models\ImportedFile;
use App\Http\Resources\Dashboard\ServiceCategoryResource;
use App\Http\Requests\Dashboard\ServiceCategoryRequest;
use App\Http\Requests\FileRequest;
use App\Models\ProviderCategoryServiceCategory;
use App\Imports\Dashboard\ServiceCategoryImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ServiceCategoryController extends Controller
{
    use Api_Trait;

    public function index()
    {
        $service_categories = ServiceCategory::orderBy('id', 'desc')->get();
        return $this->returnData(ServiceCategoryResource::collection($service_categories), [helperTrans('api.Service Categories Data')], 200);
    }

    public function show($id)
    {
        try {
            $service_category = ServiceCategory::find($id);
            if (!$service_category) {
                return $this->returnError(helperTrans('api.Service Category Not Found'), 404);
            }
            return $this->returnData(ServiceCategoryResource::make($service_category), [helperTrans('api.Service Category Data')], 200);
        } catch (\Exception $e) {
            return $this->returnError($e->getMessage(), 500);
        }
    }

    public function store(ServiceCategoryRequest $request)
    {
        try {
            DB::beginTransaction();
            $service_category = ServiceCategory::create([
                'name' => $request->name,
            ]);
            if ($request->provider_category_id) {
                ProviderCategoryServiceCategory::create([
                    'provider_category_id' => $request->provider_category_id,
                    'service_category_id' => $service_category->id,
                ]);
            }
            DB::commit();
            return $this->returnData(ServiceCategoryResource::make($service_category), [helperTrans('api.Service Category Data')], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError($e->getMessage(), 500);
        }
    }

    public function update(ServiceCategoryRequest $request, $id)
    {
        try {
            DB::beginTransaction();
            $service_category = ServiceCategory::find($id);
            if (!$service_category) {
                return $this->returnError(helperTrans('api.Service Category Not Found'), 404);
            }
            $service_category->update([
                'name' => $request->name,
            ]);
            if ($request->provider_category_id) {
                ProviderCategoryServiceCategory::where('service_category_id', $service_category->id)->delete();
                ProviderCategoryServiceCategory::create([
                    'provider_category_id' => $request->provider_category_id,
                    'service_category_id' => $service_category->id,
                ]);
            }
            DB::commit();
            return $this->returnData(ServiceCategoryResource::make($service_category), [helperTrans('api.Service Category Data')], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError($e->getMessage(), 500);
        }
    }

    public function destroy($id)
    {
        try {
            DB::beginTransaction();
            $service_category = ServiceCategory::find($id);
            if (!$service_category) {
                return $this->returnError(helperTrans('api.Service Category Not Found'), 404);
            }
            ProviderCategoryServiceCategory::where('service_category_id', $service_category->id)->delete();
            $service_category->delete();
            DB::commit();
            return $this->returnData(ServiceCategoryResource::make($service_category), [helperTrans('api.Service Category Data')], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError($e->getMessage(), 500);
        }
    }

    public function import(FileRequest $request)
    {
        try {
            // Save file to imported_files table before import
            $importedFile = ImportedFile::saveFile(
                $request->file('file'),
                auth('sanctum')->id()
            );

            $import = new ServiceCategoryImport($importedFile->id);
            Excel::import($import, $importedFile->path);

            $response = [
                'success_count' => $import->getSuccessCount(),
                'error_count' => $import->getErrorCount(),
                'errors' => $import->getErrors(),
                'imported_file_id' => $importedFile->id,
                'filename' => $importedFile->file_name
            ];

            if ($import->getErrorCount() > 0) {
                return $this->returnData([
                    $response,
                    helperTrans('api.Service Categories Template Has Errors'),
                    "errors Count: {$import->getErrorCount()}"
                ], 422);
            }

            return $this->returnData([
                $response,
                helperTrans('api.Service Categories Imported Successfully')
            ], 200);
        } catch (\Exception $e) {
            return $this->returnError($e->getMessage(), 500);
        }
    }
}
