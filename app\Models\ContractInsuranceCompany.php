<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractInsuranceCompany extends Model
{
    use HasFactory;
    protected $table = 'contract_insurance_companies';
    protected $fillable = [
        'contract_id',
        'insurance_company_id',
    ];
    protected $guarded = [];
    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }
    public function insuranceCompany()
    {
        return $this->belongsTo(InsuranceCompany::class);
    }
}
