<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\ProviderBranchDoctorImport;
use App\Models\ProviderBranchDoctor;
use App\Models\ProviderTime;
use App\Models\Day;
use App\Http\Traits\ResponseTrait;

class ProviderBranchDoctorController extends Controller
{
    use ResponseTrait;

    /**
     * Download the Excel template for importing provider branch doctors.
     */
    public function downloadTemplate()
    {
        $path = public_path('templates/provider_doctor_template.xlsx');
        if (!file_exists($path)) {
            return redirect()->back()->with('error', 'Template file not found.');
        }
        return response()->download($path, 'provider_doctor_template.xlsx');
    }


    public function index(Request $request)
    {
        if ($request->ajax()) {
            $provider_branch_doctors = ProviderBranchDoctor::with(['providerBranch', 'doctor'])->latest();

            return DataTables::of($provider_branch_doctors)
                ->addColumn('doctor', function ($provider_branch_doctor) {
                    return json_decode($provider_branch_doctor->doctor?->nickname)->en;
                })
                ->addColumn('provider_branch', function ($provider_branch_doctor) {
                    return $provider_branch_doctor->providerBranch?->name;
                })
                ->addColumn('provider_branch_governorate', function ($provider_branch_doctor) {
                    return $provider_branch_doctor->providerBranch?->governorate?->name;
                })
                ->addColumn('provider_branch_city', function ($provider_branch_doctor) {
                    return $provider_branch_doctor->providerBranch?->city?->name;
                })
                ->addColumn('provider_branch_location', function ($provider_branch_doctor) {
                    return $provider_branch_doctor->providerBranch?->location;
                })
                ->addColumn('specialization', function ($provider_branch_doctor) {
                    return $provider_branch_doctor->doctor?->specialization?->name;
                })
                ->addColumn('price', function ($provider_branch_doctor) {
                    return $provider_branch_doctor->price;
                })
                ->addColumn('time', function ($provider_branch_doctor) {
                    $time = ProviderTime::with('day')
                        ->where('provider_type', 'provider_branch_doctor')
                        ->where('provider_id', $provider_branch_doctor->id)
                        ->select('day_id', 'from_time', 'to_time')
                        ->get();

                    return $time->map(function ($time) {
                        return $time->day?->day . ' ' . date('h:i A', strtotime($time->from_time)) . ' - ' . date('h:i A', strtotime($time->to_time));
                    })->join('<br>');
                })->addColumn('action', function ($provider_branch_doctor) {
                    $html = '<div class="dropdown d-inline-block">
                    <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ri-more-fill align-middle"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">';
                    $html .= '<li><a class="dropdown-item" href="' . route('admin.provider_doctors.edit', $provider_branch_doctor->id) . '">' . helperTrans('admin.edit') . '</a></li>';
                    $html .= '</ul></div>';
                    return $html;
                })
                ->filter(function ($query) use ($request) {
                    if ($search = $request->get('search')['value'] ?? null) {

                        $query->whereHas('doctor', function ($q) use ($search) {
                            $q->where("nickname", 'like', "%{$search}%")->orWhere("name", 'like', "%{$search}%");
                        })->orWhereHas('providerBranch', function ($q) use ($search) {
                            $q->where("name", 'like', "%{$search}%");
                        })->orWhereHas('doctor.specialization', function ($q) use ($search) {
                            $q->where("name", 'like', "%{$search}%");
                        });
                    }
                })
                ->rawColumns(['time', 'action'])
                ->make(true);
        }
        return view('Admin.CRUDS.providerBranchDoctors.index');
    }

    public function import_provider_doctor(Request $request)
    {
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        Excel::import(new ProviderBranchDoctorImport, $request->file('file'));

        return redirect()->back()->with('success', 'Hospital doctors imported successfully');
    }

    public function edit($id)
    {
        $provider_branch_doctor = ProviderBranchDoctor::with(['doctor', 'providerBranch'])->findOrFail($id);
        $days = Day::get();
        $times = ProviderTime::where('provider_type', 'provider_branch_doctor')
            ->where('provider_id', $id)
            ->get();

        return view('Admin.CRUDS.providerBranchDoctors.edit', compact('provider_branch_doctor', 'days', 'times'));
    }

    public function update(Request $request, $id)
    {
        $provider_branch_doctor = ProviderBranchDoctor::findOrFail($id);

        // Update price
        $provider_branch_doctor->update([
            'price' => $request->price
        ]);

        // Update times
        ProviderTime::where('provider_type', 'provider_branch_doctor')
            ->where('provider_id', $id)
            ->delete();

        if ($request->has('day_id')) {
            foreach ($request->day_id as $key => $day_id) {
                ProviderTime::create([
                    'provider_id' => $id,
                    'provider_type' => 'provider_branch_doctor',
                    'day_id' => $day_id,
                    'from_time' => $request->from_time[$key],
                    'to_time' => $request->to_time[$key]
                ]);
            }
        }

        return redirect()->route('admin.provider_doctors.index')->with('success', ' doctor updated successfully');
    }
}
