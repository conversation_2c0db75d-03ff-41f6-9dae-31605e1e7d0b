<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Api\V1\BookingGeneralController;
use App\Models\Booking;
use App\Models\Doctor;
use App\Models\FawaterkTest;
use App\Models\Invoice;
use App\Models\Notification;
use App\Models\Package;
use App\Models\Patient;
use App\Models\PatientSubscribe;
use App\Models\SpecializationBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class FawaterkTestController extends Controller
{
    function success(Request  $req)
    {
        Log::info('Webhook received:', $req->all());
        FawaterkTest::create([
            'response_data' => json_encode($req->all()) ?? 'no data',
            // 'response_data' => 'Request'
        ]);
        // dd([$req->all(), $req->invoice_id]);

        $invoice = Invoice::where('invoice_id', $req->invoice_id)->first();

        if ($invoice) {
            $invoice->update([
                'status' => 'paid'
            ]);

            if ($invoice->invoiceable_type == PatientSubscribe::class) {
                $invoice->invoiceable->update([
                    'status' => 'active',
                    'start_date' => Carbon::now(),
                ]);
                $patient_id = $invoice->invoiceable->patient_id;
                $patient = Patient::find($patient_id);

                $patient->update([
                    'card_id' => generate_individual_card_id($invoice->invoiceable->package_id),
                ]);

                $patient_info = ['title' => 'Payment Successful !', 'body' => "Payment Successful, Your Subscription is Active Now"];
                run_push_notification($patient_id, 'patient', $patient_info);
            } else {
                $invoice->invoiceable->update([
                    'status' => 'pending'
                ]);

                if ($invoice->invoiceable_type == Booking::class) {
                    $doctors = Doctor::where('doctor_type', 'general')->get();
                    foreach ($doctors as $doctor) {
                        $doctor_info = ['title' => 'New Booking', 'body' => "There is a new booking"];
                        run_push_notification($doctor->id, 'doctor', $doctor_info);

                        // Notification::create([
                        //     'title' => helperTrans('api.Booking '),
                        //     'body' => ' There is a New booking ',
                        //     'user_type' => 'doctor',
                        //     'user_id' => $doctor->id,
                        //     'type' => 'booking',
                        //     'foreign_id' => $invoice->invoiceable->id,
                        //     'main_service_id' => 1,
                        //     'date' => date('Y-m-d'),
                        // ]);
                    }
                } else if ($invoice->invoiceable_type == SpecializationBooking::class) {
                    $doctor_id = $invoice->invoiceable->doctor_id;
                    $doctor_info = ['title' => 'New Booking', 'body' => "Have New Consultation"];
                    run_push_notification($doctor_id, 'doctor', $doctor_info);
                    // Notification::create([
                    //     'title' => helperTrans('api.Booking '),
                    //     'body' => ' Have New Consultation ',
                    //     'user_type' => 'doctor',
                    //     'user_id' => $doctor_id,
                    //     'type' => 'booking',
                    //     'foreign_id' => $invoice->invoiceable->id,
                    //     'main_service_id' => 2,
                    //     'date' => date('Y-m-d'),
                    // ]);
                }

                $patient_id = $invoice->invoiceable->patient_id;

                $patient_info = ['title' => 'Payment Successful !', 'body' => "Payment Successful, Your Booking is Active Now"];
                run_push_notification($patient_id, 'patient', $patient_info);
            }
        }


        // $instant_booking = Booking::where('invoice_id', $req->invoice_id)->first();
        // if ($instant_booking) {
        //     $instant_booking->update([
        //         'status' => 'pending'
        //     ]);
        // }
        // $specialization_booking = SpecializationBooking::where('invoice_id', $req->invoice_id)->first();
        // if ($specialization_booking) {
        //     $specialization_booking->update([
        //         'status' => 'pending'
        //     ]);
        // }

        // $patient_subscribe = PatientSubscribe::where('invoice_id', $req->invoice_id)->first();
        // if ($patient_subscribe) {
        //     $patient_subscribe->update([
        //         'status' => 'active'
        //     ]);
        // }

        return 'success';
    }

    function fail(Request  $req)
    {

        FawaterkTest::create([
            'response_data' => 'fail',
            // 'response_data' => 'Request'
        ]);
    }



    function redirect_page(Request  $req)
    {

        return 'success';
    }
}
