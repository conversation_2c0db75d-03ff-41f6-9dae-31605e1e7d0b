<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Traits\Api_Trait;
use App\Models\Partner;
use App\Http\Resources\Dashboard\PartnerResource;
use App\Http\Requests\Dashboard\PartnerRequest;
use Illuminate\Http\Request;

class PartnerController extends Controller
{
    use Api_Trait;

    public function index(Request $request)
    {
        $partners = Partner::with(['promoCode', 'package', 'doctoria_employee', 'country'])
            ->when($request->search, function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%')
                    ->orWhere('client_no', 'like', '%' . $request->search . '%')
                    ->orWhere('contact_person', 'like', '%' . $request->search . '%')
                    ->orWhere('email', 'like', '%' . $request->search . '%')
                    ->orWhere('phone', 'like', '%' . $request->search . '%');
            })
            ->when($request->status, function ($query) use ($request) {
                $query->where('status', $request->status);
            })
            // ->when($request->package_id, function ($query) use ($request) {
            //     $query->where('package_id', $request->package_id);
            // })
            // ->when($request->promo_code_id, function ($query) use ($request) {
            //     $query->where('promo_code_id', $request->promo_code_id);
            // })
            ->paginate($request->per_page ?? 25);

        return $this->paginatedResponse(
            PartnerResource::collection($partners),
            helperTrans('api.Partner Data'),
            200
        );
    }

    public function store(PartnerRequest $request)
    {
        $data = $request->validated();

        // Add +2 prefix to phone if provided and doesn't already have it
        if (isset($data['phone']) && !str_starts_with($data['phone'], '+2')) {
            $data['phone'] = '+2' . $data['phone'];
        }

        $partner = Partner::create($data);

        return $this->returnData(
            new PartnerResource($partner->load(['promoCode', 'package'])),
            [helperTrans('api.Partner created successfully')],
            201
        );
    }

    public function show($id)
    {
        $partner = Partner::find($id);
        if (!$partner) {
            return $this->returnError(helperTrans('api.Partner Not Found'), 404);
        }
        $partner->load(['promoCode', 'package']);

        return $this->returnData(
            new PartnerResource($partner),
            [helperTrans('api.Partner Data')],
            200
        );
    }

    public function update(PartnerRequest $request, $id)
    {
        $partner = Partner::find($id);
        if (!$partner) {
            return $this->returnError(helperTrans('api.Partner Not Found'), 404);
        }
        $data = $request->validated();

        // Add +2 prefix to phone if provided and doesn't already have it
        if (isset($data['phone']) && !str_starts_with($data['phone'], '+2')) {
            $data['phone'] = '+2' . $data['phone'];
        }

        $partner->update($data);

        return $this->returnData(
            new PartnerResource($partner->load(['promoCode', 'package'])),
            [helperTrans('api.Partner updated successfully')],
            200
        );
    }

    public function destroy($id)
    {
        $partner = Partner::find($id);
        if (!$partner) {
            return $this->returnError(helperTrans('api.Partner Not Found'), 404);
        }
        $partner->delete();

        return $this->returnData(
            null,
            [helperTrans('api.Partner deleted successfully')],
            200
        );
    }
}
