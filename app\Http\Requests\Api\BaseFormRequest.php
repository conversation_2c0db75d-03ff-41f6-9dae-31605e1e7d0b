<?php

namespace App\Http\Requests\Api;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Contracts\Validation\Validator;
use Illuminate\Http\Exceptions\HttpResponseException;

use App\Http\Traits\Api_Trait;

class BaseFormRequest extends FormRequest
{
  use Api_Trait;

  /**
   * Handle a failed validation attempt.
   *
   * @param  \Illuminate\Contracts\Validation\Validator  $validator
   * @return void
   *
   * @throws \Illuminate\Http\Exceptions\HttpResponseException
   */
  protected function failedValidation(Validator $validator)
  {
    // Collect the errors, flatten them, and return them with a 403 status
    throw new HttpResponseException(
      $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403)
    );
  }
}
