<?php

namespace App\Http\Controllers\Api\V1\Doctor;

use App\Models\Analysis;
use App\Models\Radiology;
use App\Models\Laboratory;
use Illuminate\Http\Request;
use App\Http\Traits\Api_Trait;
use App\Models\RadiologyCenter;
use App\Http\Controllers\Controller;
use App\Http\Resources\AnalysResource;
use App\Http\Resources\AnalysisResource;
use App\Http\Resources\LaboratoryResource;
use App\Http\Resources\LaboratoriesResource;

class TestController extends Controller
{

    use Api_Trait;
    public function test(Request $request)
    {
        $query = $request->input('query');

        // Fetch medication units based on the query
        $Analysis = Analysis::where('name->ar', 'LIKE', "%{$query}%")
            ->orWhere('name->en', 'LIKE', "%{$query}%")
            ->get(); // Execute the query to get the results
          

        // Return the data with a resource collection
        return $this->returnData(AnalysResource::collection($Analysis), [helperTrans('api.Analysis data')], 200);
    }


    public function laboratories(Request $request)
    {
        $query = $request->input('query');

        // Fetch medication units based on the query
        $Laboratory = Laboratory::where('name->ar', 'LIKE', "%{$query}%")
        ->orWhere('name->en', 'LIKE', "%{$query}%")
        ->get(); // Execute the query to get the results


        // Return the data with a resource collection
        return $this->returnData(LaboratoriesResource::collection($Laboratory), [helperTrans('api.Analysis data')], 200);
    }


    public function radiologies(Request $request)
    {
        $query = $request->input('query');

        // Fetch medication units based on the query
        $Radiology = Radiology::where('name->ar', 'LIKE', "%{$query}%")
        ->orWhere('name->en', 'LIKE', "%{$query}%")
        ->get(); // Execute the query to get the results


        // Return the data with a resource collection
        return $this->returnData(LaboratoriesResource::collection($Radiology), [helperTrans('api.Radiologies data')], 200);
    }


    public function radiology_center(Request $request)
    {
        $query = $request->input('query');

        // Fetch medication units based on the query
        $radiology_center = RadiologyCenter::where('name->ar', 'LIKE', "%{$query}%")
        ->orWhere('name->en', 'LIKE', "%{$query}%")
        ->get(); // Execute the query to get the results


        // Return the data with a resource collection
        return $this->returnData(LaboratoriesResource::collection($radiology_center), [helperTrans('api.RadiologyCenter data')], 200);
    }

}
