<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Validation\Rule;

class AreaRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'city_id' => ['nullable', 'exists:cities,id'],
        ];

        // Handle unique validation for create vs update
        if (request()->isMethod('PUT') || request()->isMethod('PATCH')) {
            // For update requests
            $areaId = request()->route('area');

            foreach (languages() as $language) {
                $rules["name.{$language->abbreviation}"] = [
                    'required',
                    'string',
                    'max:500',
                    'min:2',
                    Rule::unique('areas', "name->{$language->abbreviation}")
                        ->ignore($areaId)
                ];
            }
        } else {
            // For create requests
            foreach (languages() as $language) {
                $rules["name.{$language->abbreviation}"] = [
                    'required',
                    'string',
                    'max:500',
                    'min:2',
                    Rule::unique('areas', "name->{$language->abbreviation}")
                ];
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        $messages = [
            'city_id.exists' => 'The selected city does not exist',
        ];

        foreach (languages() as $language) {
            $messages["name.{$language->abbreviation}.required"] = "Area name in {$language->name} is required";
            $messages["name.{$language->abbreviation}.string"] = "Area name in {$language->name} must be a valid string";
            $messages["name.{$language->abbreviation}.max"] = "Area name in {$language->name} cannot exceed 500 characters";
            $messages["name.{$language->abbreviation}.min"] = "Area name in {$language->name} must be at least 2 characters";
            $messages["name.{$language->abbreviation}.unique"] = "Area name in {$language->name} already exists";
        }

        return $messages;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        $attributes = [];

        foreach (languages() as $language) {
            $attributes["name.{$language->abbreviation}"] = "area name ({$language->name})";
        }

        return $attributes;
    }

    /**
     * Prepare the data for validation.
     *
     * @return void
     */
    protected function prepareForValidation()
    {
        // Clean and format name fields
        if (request()->has('name') && is_array(request()->input('name'))) {
            $cleanedNames = [];
            foreach (request()->input('name') as $lang => $value) {
                if ($value) {
                    $cleanedNames[$lang] = trim($value);
                }
            }
            $this->merge(['name' => $cleanedNames]);
        }
    }

    /**
     * Get the validated data from the request.
     *
     * @return array
     */
    public function validationData()
    {
        return $this->validated();
    }
}
