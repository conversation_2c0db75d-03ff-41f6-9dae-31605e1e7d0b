<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('new_bookings', function (Blueprint $table) {
            $table->id();
            $table->foreignId('booking_type_id')->constrained()->onDelete('restrict');
            $table->foreignId('patient_id')->constrained()->onDelete('cascade');
            $table->date('date')->nullable();
            $table->time('time')->nullable();
            $table->text('description')->nullable();
            $table->foreignId('status_booking_id')->constrained()->onDelete('restrict');
            $table->foreignId('payment_method_id')->constrained()->onDelete('restrict');
            $table->enum('payment', ['unpaid', 'paid', 'undefined'])->default('unpaid');
            $table->double('price')->default(0);
            $table->foreignId('promo_code_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('provider_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('provider_branch_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('doctor_id')->nullable()->constrained()->onDelete('cascade');
            $table->foreignId('recommended_doctor_id')->nullable()->constrained('doctors')->onDelete('cascade');
            $table->foreignId('referal_booking_id')->nullable()->constrained('new_bookings')->onDelete('set null');
            $table->foreignId('relative_id')->nullable()->constrained()->onDelete('set null');
            $table->foreignId('address_detail_id')->nullable()->constrained('adress_details')->onDelete('set null');
            $table->text('cancel_reason')->nullable();
            $table->timestamp('read_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('new_bookings');
    }
};
