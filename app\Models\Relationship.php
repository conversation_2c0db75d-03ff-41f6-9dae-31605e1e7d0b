<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Relationship extends Model
{
    use HasFactory;

    protected $fillable = ['name', 'code'];

    /**
     * Get the relatives for the relationship.
     */
    public function relatives()
    {
        return $this->hasMany(Relative::class);
    }

    /**
     * Scope a query to filter by name.
     */
    public function scopeWhenName($query, $name)
    {
        return $query->when($name, function ($q) use ($name) {
            $q->where('name', 'like', '%' . $name . '%');
        });
    }

    /**
     * Scope a query to filter by code.
     */
    public function scopeWhenCode($query, $code)
    {
        return $query->when($code, function ($q) use ($code) {
            $q->where('code', 'like', '%' . $code . '%');
        });
    }

    /**
     * Get display name for the relationship
     */
    public function getDisplayNameAttribute()
    {
        return $this->name . ($this->code ? " ({$this->code})" : '');
    }
}
