<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use App\Http\Traits\Upload_Files;


class FileController extends Controller
{
        use Upload_Files;
    public function upload(Request $request)
    {
        $data = [];

        if ($request->hasFile('image')) { 
            $data["image"] = $this->uploadFiles('doctors', $request->file('image'), null);
        } elseif ($request->hasFile('attach_contract')) { 
            $path = $request->file('attach_contract')->store('uploads', 'public');
            $data['attach_contract'] = 'uploads/' . basename($path);
        } elseif ($request->hasFile('attach_documents')) { 
            $path = $request->file('attach_documents')->store('uploads', 'public');
            $data['attach_documents'] = 'uploads/' . basename($path);
        } elseif ($request->hasFile('attach_price_list')) { 
            $path = $request->file('attach_price_list')->store('uploads', 'public');
            $data['attach_price_list'] = 'uploads/' . basename($path);
        }

        // If no file was uploaded
        if (empty($data)) {
            return response()->json(['error' => 'File not uploaded'], 400);
        }

        return response()->json($data, 200);
    }
    
    // public function upload(Request $request)
    // {
    //     $fileFields = ['image', 'attach_contract', 'attach_documents', 'attach_price_list'];
    
    //     foreach ($fileFields as $field) {
    //         if ($request->hasFile($field)) {
    //             if($field == 'image'){
    //                 $filed = "doctors";
    //             }
    //             $folder = 'uploads/' . $field;
    //             $path = $request->file($field)->store($folder, 'public');
    //              return response()->json(['path' => $path], 200);
    //         }
    //     }
    
    //     return response()->json(['error' => 'File not uploaded'], 400);
    // }



    public function revert(Request $request)
    {
        Storage::disk('public')->delete($request->getContent());
    }
}
