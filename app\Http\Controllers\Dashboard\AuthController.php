<?php

namespace App\Http\Controllers\Dashboard;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Models\Admin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

class AuthController extends Controller
{
    public function login(Request $request)
    {
        $request->validate([
        'email' => 'required|email',
        'password' => 'required'
    ]);

    $admin = Admin::where('email', $request->email)->first();

    if (! $admin || ! Hash::check($request->password, $admin->password)) {
        return response()->json(['message' => 'Invalid credentials'], 401);
    }
    
    $token = $admin->createToken('admin-token')->plainTextToken;
    
    return response()->json([
        'token' => $token,
        'admin' => $admin
    ]);
    }
    
}
