<?php

namespace App\Enums\Branch;

enum BranchFromType: string
{
    case HOSPITAL = 'hospital';
    case BRANCH = 'branch';
    case PHARMACY = 'pharmacy';
    public static function status($value): string
    {
        if ($value == self::HOSPITAL) {
            return '<span class="badge badge-success">Hospital</span>';
        } elseif ($value == self::BRANCH) {
            return '<span class="badge badge-primary">Branch</span>';
        } elseif ($value == self::PHARMACY) {
            return '<span class="badge badge-warning">Pharmacy</span>';
        } else {
            return '<span class="badge badge-default">' . trans('general.not_status_assigned') . '</span>';
        }
    }
}
