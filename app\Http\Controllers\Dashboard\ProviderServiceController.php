<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Traits\Api_Trait;
use App\Models\ProviderService;
use App\Http\Resources\Dashboard\ProviderServiceResource;
use App\Http\Resources\Dashboard\ServiceResource;
use App\Http\Resources\ProviderBranchResource;
use App\Http\Resources\ProviderServiceResource as ResourcesProviderServiceResource;
use App\Models\PriceList;
use App\Models\PriceListService;
use Illuminate\Validation\Validator;
use App\Models\Provider;
use App\Models\ProviderBranchService;
use Illuminate\Support\Facades\DB;

class ProviderServiceController extends Controller
{
    use Api_Trait;

    public function index(Request $request)
    {
        $provider_services = ProviderService::with(['service.service_category', 'service.service_sub_category', 'branchOverrides'])
            ->where('provider_id', $request->provider_id)
            ->when($request->service_category_id, function ($query) use ($request) {
                $query->whereHas('service', function ($query) use ($request) {
                    $query->where('service_category_id', $request->service_category_id);
                });
            })
            ->orderBy('id', 'desc')->get();

        $grouped_services = $provider_services->groupBy('service.service_sub_category_id')
            ->map(function ($services, $sub_category_id) use ($request) {
                $first_service = $services->first();
                return [
                    'service_sub_category_id' => $sub_category_id,
                    'service_sub_category_name' => optional(optional($first_service->service)->service_sub_category)->name,
                    'service_category_id' => optional(optional($first_service->service)->service_category)->id,
                    'service_category_name' => optional(optional($first_service->service)->service_category)->name,
                    'services' => $services->flatMap(function ($provider_service) use ($request) {
                        $result = [];
                        $result[] = [
                            'service_id' => $provider_service->service->id,
                            'service_name' => $provider_service->service->name,
                            'service_price' => $provider_service->price ?? 0,
                            'service_discount' => $provider_service->discount ?? 0,
                            'service_type' => $provider_service->service->type,
                            'branch_id' => null,
                            'is_branch_override' => false,
                        ];
                        foreach ($provider_service->branchOverrides as $branchOverride) {
                            $result[] = [
                                'service_id' => $provider_service->service->id,
                                'service_name' => $provider_service->service->name,
                                'service_price' => (float)$branchOverride->price ?? 0,
                                'service_discount' => $branchOverride->discount ?? 0,
                                'service_type' => $provider_service->service->type,
                                'branch_id' => $branchOverride->provider_branch_id,
                                'is_branch_override' => true,
                            ];
                        }
                        return $result;
                    })->values()
                ];
            })->values();

        return $this->returnData($grouped_services, [helperTrans('api.Price List Service Data')], 200);
    }


    public function store(Request $request)
    {
        $request->validate([
            "provider_id" => "required|exists:providers,id",
            "pricelist_id" => "required|exists:price_lists,id",
            "percentage" => "nullable|numeric",
            "service_category_id" => "nullable|exists:service_categories,id",
            "service_sub_category_id" => "nullable|exists:service_sub_categories,id",
        ]);

        $provider = Provider::find($request->provider_id);
        $priceList = PriceList::find($request->pricelist_id);

        if (!$provider || !$priceList) {
            return $this->returnError(helperTrans('api.Provider or Price List Not Found'), 404);
        }

        $services = PriceListService::where('price_list_id', $request->pricelist_id)->when($request->service_category_id, function ($query) use ($request) {
            $query->where('service_category_id', $request->service_category_id);
        })
            // ->when($request->service_sub_category_id, function ($query) use ($request) {
            //     $query->where('service_sub_category_id', $request->service_sub_category_id);
            // })
            ->select('service_category_id', 'service_sub_category_id', 'service_id', 'price')->get();
        $providerServiceData = [];


        foreach ($services as $service) {
            $providerServiceData[] = [
                'service_category_id' => $service->service_category_id,
                'service_sub_category_id' => $service->service_sub_category_id,
                'provider_id' => $provider->id,
                'service_id' => $service->service_id,
                'price' => ($service->price + ($request->percentage / 100.0 * $service->price)) ?? 0,
                'discount' => 0,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        DB::beginTransaction();

        try {
            ProviderService::insert($providerServiceData);
            DB::commit();
            return $this->returnSuccessMessage(helperTrans('api.Provider Services Stored Successfully'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError('Store failed: ' . $e->getMessage(), 500);
        }
    }


    public function update(Request $request, $id)
    {
        $provider = Provider::find($id);
        if (!$provider) {
            return $this->returnError(helperTrans('api.Provider Not Found'), 404);
        }

        $request->validate([
            'services' => 'required|array',
            'services.*.service_id' => 'required|integer',
            'services.*.price' => 'required|numeric',
            'services.*.branch_id' => 'nullable|integer',
            'services.*.discount' => 'nullable|numeric',
            'services.*.is_active' => 'nullable|boolean',
            'services.*.from_date' => 'nullable|date',
            'services.*.to_date' => 'nullable|date',
        ]);

        DB::beginTransaction();

        try {
            $provider_services = [];

            foreach ($request->services as $service) {
                $provider_service = ProviderService::where('provider_id', $provider->id)
                    ->where('service_id', $service['service_id'])
                    ->first();

                if (array_key_exists('branch_id', $service)) {
                    $branch_data = [
                        'provider_service_id' => $provider_service->id,
                        'provider_id' => $provider->id,
                        'provider_branch_id' => $service['branch_id'],
                        'service_id' => $service['service_id'],
                        'price' => $service['price'],
                        'discount' => $service['discount'] ?? 0,
                        'is_active' => $service['is_active'] ?? 1,
                        'from_date' => $service['from_date'] ?? null,
                        'to_date' => $service['to_date'] ?? null,
                    ];

                    ProviderBranchService::updateOrCreate(
                        [
                            'provider_service_id' => $provider_service->id,
                            'provider_id' => $provider->id,
                            'provider_branch_id' => $service['branch_id'],
                            'service_id' => $service['service_id']
                        ],
                        $branch_data
                    );
                } else {
                    $provider_service_data = [
                        'provider_id' => $provider->id,
                        'service_id' => $service['service_id'],
                        'price' => $service['price'],
                        'discount' => $service['discount'] ?? 0,
                    ];

                    $provider_service = ProviderService::updateOrCreate(
                        [
                            'provider_id' => $provider->id,
                            'service_id' => $service['service_id']
                        ],
                        $provider_service_data
                    );

                    $provider_services[] = $provider_service;
                }
            }

            DB::commit();

            return $this->returnData(
                $provider_services,
                [helperTrans('api.Provider Services Updated Successfully')],
                200
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError('Update failed: ' . $e->getMessage(), 500);
        }
    }


    public function destroy(Request $request)
    {
        $services = $request->input('services');

        foreach ($services as $item) {
            $serviceId = $item['service_id'] ?? null;
            $branchId = $item['branch_id'] ?? null;

            if (!$serviceId) {
                continue;
            }

            if ($branchId) {
                ProviderBranchService::where('service_id', $serviceId)
                    ->where('provider_branch_id', $branchId)
                    ->delete();
            } else {
                ProviderService::where('service_id', $serviceId)->delete();
            }
        }

        return response()->json(['message' => 'Services deleted successfully']);
    }

    public function getProviderServiceCategories(Request $request)
    {
        $request->validate([
            'provider_id' => 'required|exists:providers,id'
        ]);

        $provider = Provider::find($request->provider_id);
        if (!$provider) {
            return $this->returnError(helperTrans('api.Provider Not Found'), 404);
        }

        $categories = $provider->serviceCategories()
            ->map(function ($category) {
                return [
                    'id' => $category->id,
                    'name' => $category->name
                ];
            });

        if ($categories->isEmpty()) {
            return $this->returnError(helperTrans('api.No Service Categories Found for Provider'), 404);
        }

        return $this->returnData($categories, [helperTrans('api.Provider Service Categories')], 200);
    }
}
