<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Traits\Api_Trait;
use App\Models\ProviderService;
use App\Http\Resources\Dashboard\ProviderServiceResource;
use App\Http\Resources\Dashboard\ServiceResource;
use App\Http\Resources\ProviderBranchResource;
use App\Http\Resources\ProviderServiceResource as ResourcesProviderServiceResource;
use Illuminate\Validation\Validator;
use App\Models\Provider;
use App\Models\ProviderBranchService;
use Illuminate\Support\Facades\DB;

class ProviderServiceController extends Controller
{
    use Api_Trait;

    public function index(Request $request)
    {
        $baseServices = ProviderService::with(['service', 'branchOverrides'])->where('provider_id', $request->provider_id)->get();

        $result = [];

        foreach ($baseServices as $providerService) {

            $branchOverrides = $providerService->branchOverrides;

            if ($branchOverrides->isNotEmpty()) {
                foreach ($branchOverrides as $branchService) {
                    $result[] = [
                        "provider_id" => $providerService->provider_id,
                        'branch' => ProviderBranchResource::make($branchService->providerBranch),
                        'service' =>    ServiceResource::make($providerService->service),
                        'price' => $branchService->price ?? $providerService->price,
                        'discount' => $branchService->discount ?? $providerService->discount,
                        'image' => $branchService->image ?? $providerService->image,
                    ];
                }
            } else {

                $result[] = [
                    "provider_id" => $providerService->provider_id,
                    'branch_id' => null,
                    'service' =>    ServiceResource::make($providerService->service),
                    'price' => $providerService->price,
                    'discount' => $providerService->discount,
                    'image' => $providerService->image,
                ];
            }
        }

        return $this->returnData($result, [helperTrans('api.Provider Service Data')], 200);
    }


    public function store(Request $request)
    {
        $request->validate([
            'provider_id' => 'required|exists:providers,id',
            'services' => 'required|array',
            'services.*.service_id' => 'required|integer',
            'services.*.price' => 'required|numeric',
            'services.*.branch_id' => 'nullable|integer',
            'services.*.discount' => 'nullable|numeric',
            'services.*.is_active' => 'nullable|boolean',
            'services.*.from_date' => 'nullable|date',
            'services.*.to_date' => 'nullable|date',
        ]);

        $provider = Provider::find($request->provider_id);

        $branchServiceData = [];
        $providerServiceData = [];

        foreach ($request->services as $service) {
            $providerServiceData[] = [
                'provider_id' => $provider->id,
                'service_id' => $service['service_id'],
                'price' => $service['price'],
                'discount' => $service['discount'] ?? 0,
                'created_at' => now(),
                'updated_at' => now(),
            ];

            if (array_key_exists('branch_id', $service)) {
                $branchServiceData[] = [
                    'provider_id' => $provider->id,
                    'provider_branch_id' => $service['branch_id'],
                    'service_id' => $service['service_id'],
                    'price' => $service['price'],
                    'discount' => $service['discount'] ?? 0,
                    'is_active' => $service['is_active'] ?? 1,
                    'from_date' => $service['from_date'] ?? null,
                    'to_date' => $service['to_date'] ?? null,
                    'created_at' => now(),
                    'updated_at' => now(),
                ];
            }
        }

        DB::beginTransaction();

        try {
            ProviderService::insert($providerServiceData);
            if (!empty($branchServiceData)) {
                ProviderBranchService::insert($branchServiceData);
            }
            DB::commit();
            return $this->returnSuccessMessage(helperTrans('api.Provider Services Stored Successfully'), 200);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError('Store failed: ' . $e->getMessage(), 500);
        }
    }


    public function update(Request $request, $id)
    {
        $provider = Provider::find($id);
        if (!$provider) {
            return $this->returnError(helperTrans('api.Provider Not Found'), 404);
        }

        $request->validate([
            'services' => 'required|array',
            'services.*.service_id' => 'required|integer',
            'services.*.price' => 'required|numeric',
            'services.*.branch_id' => 'nullable|integer',
            'services.*.discount' => 'nullable|numeric',
            'services.*.is_active' => 'nullable|boolean',
            'services.*.from_date' => 'nullable|date',
            'services.*.to_date' => 'nullable|date',
        ]);

        DB::beginTransaction();

        try {
            $provider_services = [];

            foreach ($request->services as $service) {
                $provider_service_data = [
                    'provider_id' => $provider->id,
                    'service_id' => $service['service_id'],
                    'price' => $service['price'],
                    'discount' => $service['discount'] ?? 0,
                ];

                $provider_service = ProviderService::updateOrCreate(
                    [
                        'provider_id' => $provider->id,
                        'service_id' => $service['service_id']
                    ],
                    $provider_service_data
                );

                $provider_services[] = $provider_service;

                if (array_key_exists('branch_id', $service)) {
                    $branch_data = [
                        'provider_service_id' => $provider_service->id,
                        'provider_id' => $provider->id,
                        'provider_branch_id' => $service['branch_id'],
                        'service_id' => $service['service_id'],
                        'price' => $service['price'],
                        'discount' => $service['discount'] ?? 0,
                        'is_active' => $service['is_active'] ?? 1,
                        'from_date' => $service['from_date'] ?? null,
                        'to_date' => $service['to_date'] ?? null,
                    ];

                    ProviderBranchService::updateOrCreate(
                        [
                            'provider_service_id' => $provider_service->id,
                            'provider_id' => $provider->id,
                            'provider_branch_id' => $service['branch_id'],
                            'service_id' => $service['service_id']
                        ],
                        $branch_data
                    );
                }
            }

            DB::commit();

            return $this->returnData(
                $provider_services,
                [helperTrans('api.Provider Services Updated Successfully')],
                200
            );
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError('Update failed: ' . $e->getMessage(), 500);
        }
    }


    public function destroy(Request $request)
    {
        $services = $request->input('services');

        foreach ($services as $item) {
            $serviceId = $item['service_id'] ?? null;
            $branchId = $item['branch_id'] ?? null;

            if (!$serviceId) {
                continue;
            }

            if ($branchId) {
                ProviderBranchService::where('service_id', $serviceId)
                    ->where('provider_branch_id', $branchId)
                    ->delete();
            } else {
                ProviderService::where('service_id', $serviceId)->delete();
            }
        }

        return response()->json(['message' => 'Services deleted successfully']);
    }
}
