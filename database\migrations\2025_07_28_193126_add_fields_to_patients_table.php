<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->string('external_id')->nullable()->after('card_id');
            $table->string('national_id')->nullable()->after('external_id');
            $table->dropColumn('id_number');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('patients', function (Blueprint $table) {
            $table->dropColumn('external_id');
            $table->dropColumn('national_id');
            $table->string('id_number')->nullable()->after('date_of_birth');
        });
    }
};
