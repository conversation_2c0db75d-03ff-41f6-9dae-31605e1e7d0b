<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ProviderSpecialization extends Model
{
    use HasFactory;

    protected $guarded = [];
    protected $table = 'provider_specializations';
    protected $fillable = [
        'provider_id',
        'specialization_id',
        'imported_file_id',
    ];

    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }

    public function specialization()
    {
        return $this->belongsTo(Specialization::class);
    }
    public function importedFile()
    {
        return $this->belongsTo(ImportedFile::class);
    }
}
