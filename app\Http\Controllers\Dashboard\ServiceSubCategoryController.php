<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\ServiceSubCategoryRequest;
use App\Http\Traits\Api_Trait;
use App\Http\Requests\FileRequest;
use Illuminate\Http\Request;
use App\Models\ServiceSubCategory;
use App\Models\ServiceCategory;
use App\Models\ImportedFile;
use App\Http\Resources\Dashboard\ServiceSubCategoryResource;
use App\Imports\Dashboard\ServiceSubCategoryImport;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Maatwebsite\Excel\Facades\Excel;
use PhpOffice\PhpSpreadsheet\Spreadsheet;
use PhpOffice\PhpSpreadsheet\Writer\Xlsx;

class ServiceSubCategoryController extends Controller
{
    use Api_Trait;
    public function index(Request $request)
    {
        $service_sub_categories = ServiceSubCategory::when($request->service_category_id, function ($query) use ($request) {
            $query->where('service_category_id', $request->service_category_id);
        })->get();

        return $this->returnData(ServiceSubCategoryResource::collection($service_sub_categories), [helperTrans('api.Service Sub Categories Retrieved Successfully')], 200);
    }
    public function show($id)
    {
        $service_sub_category = ServiceSubCategory::find($id);
        if (!$service_sub_category) {
            return $this->returnError(helperTrans('api.Service Sub Category Not Found'), 404);
        }
        return $this->returnData(ServiceSubCategoryResource::make($service_sub_category), [helperTrans('api.Service Sub Category Retrieved Successfully')], 200);
    }
    public function store(ServiceSubCategoryRequest $request)
    {
        $service_sub_category = ServiceSubCategory::create($request->validated());
        return $this->returnData(ServiceSubCategoryResource::make($service_sub_category), [helperTrans('api.Service Sub Category Created Successfully')], 200);
    }
    public function update(ServiceSubCategoryRequest $request, $id)
    {
        $service_sub_category = ServiceSubCategory::find($id);
        if (!$service_sub_category) {
            return $this->returnError(helperTrans('api.Service Sub Category Not Found'), 404);
        }
        $service_sub_category->update($request->validated());
        return $this->returnData(ServiceSubCategoryResource::make($service_sub_category), [helperTrans('api.Service Sub Category Updated Successfully')], 200);
    }
    public function destroy($id)
    {
        $service_sub_category = ServiceSubCategory::find($id);
        if (!$service_sub_category) {
            return $this->returnError(helperTrans('api.Service Sub Category Not Found'), 404);
        }
        $service_sub_category->delete();
        return $this->returnData(ServiceSubCategoryResource::make($service_sub_category), [helperTrans('api.Service Sub Category Deleted Successfully')], 200);
    }

    public function import(FileRequest $request)
    {
        try {
            // Save file to imported_files table before import
            $importedFile = ImportedFile::saveFile(
                $request->file('file'),
                auth('sanctum')->id()
            );

            $import = new ServiceSubCategoryImport($importedFile->id);
            Excel::import($import, $importedFile->path);

            $response = [
                'success_count' => $import->getSuccessCount(),
                'error_count' => $import->getErrorCount(),
                'errors' => $import->getErrors(),
                'imported_file_id' => $importedFile->id,
                'filename' => $importedFile->file_name
            ];

            if ($import->getErrorCount() > 0) {

                return $this->returnData([
                    $response,
                    helperTrans('api.Service Sub Categories Template Has Errors'),
                    "errors Count: {$import->getErrorCount()}"
                ], 422);
            }

            return $this->returnData([
                $response,
                helperTrans('api.Service Sub Categories Imported Successfully')
            ], 200);

        } catch (\Exception $e) {
            return $this->returnError($e->getMessage(), 500);
        }
    }
}
