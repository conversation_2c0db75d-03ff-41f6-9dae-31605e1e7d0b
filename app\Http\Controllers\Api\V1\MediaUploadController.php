<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Traits\Api_Trait;
use App\Models\TempUpload;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class MediaUploadController extends Controller
{
    use Api_Trait;

    public function upload_single(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'file' => 'required|file',
        ]);

        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $temp = TempUpload::create();

        $media = $temp->addMedia($request->file('file'))->toMediaCollection('uploads');


        return $this->returnData([
            'media_id' => $media->id,
        ], [helperTrans('uploaded successfully')]);
    }

    public function upload_multiple(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'files' => 'required',
            'files.*' => 'required|file'
        ]);

        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $temp = TempUpload::create();

        foreach ($request->file('files') as $file) {

            $media = $temp->addMedia($file)->toMediaCollection('uploads');
        }


        return $this->returnData([
            'media_ids' => $temp->getMedia('uploads')->pluck('id'),
        ], [helperTrans('uploaded successfully')]);
    }
}
