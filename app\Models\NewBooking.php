<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class NewBooking extends Model
{
    use HasFactory;
    protected $guarded = [];
    protected $table = 'new_bookings';
    protected $fillable = [
        'booking_type_id',
        'patient_id',
        'date',
        'time',
        'description',
        'status_booking_id',
        'payment_method_id',
        'payment',
        'price',
        'promo_code_id',
        'provider_id',
        'provider_branch_id',
        'doctor_id',
        'recommended_doctor_id',
        'referral_booking_id',
        'relative_id',
        'address_detail_id',
        'cancel_reason',
        'read_at',
    ];
    public function patient()
    {
        return $this->belongsTo(Patient::class);
    }
    public function relative()
    {
        return $this->belongsTo(Relative::class);
    }
    public function services()
    {
        return $this->hasMany(NewBookingService::class);
    }
    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }
    public function providerBranch()
    {
        return $this->belongsTo(ProviderBranch::class);
    }
    public function doctor()
    {
        return $this->belongsTo(Doctor::class);
    }
    public function recommendedDoctor()
    {
        return $this->belongsTo(Doctor::class, 'recommended_doctor_id');
    }
    public function referalBooking()
    {
        return $this->belongsTo(NewBooking::class, 'referral_booking_id');
    }
    public function status()
    {
        return $this->belongsTo(StatusBooking::class, 'status_booking_id');
    }
    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class, 'payment_method_id');
    }
    public function promoCode()
    {
        return $this->belongsTo(PromoCode::class, 'promo_code_id');
    }
    public function type()
    {
        return $this->belongsTo(BookingType::class, 'booking_type_id');
    }
    public function addressDetail()
    {
        return $this->belongsTo(AdressDetail::class, 'address_detail_id');
    }
    public function attachments()
    {
        return $this->morphMany(GlobalAttachment::class, 'attachable');
    }
}
