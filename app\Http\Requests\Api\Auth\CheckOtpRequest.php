<?php

namespace App\Http\Requests\Api\Auth;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\Auth\Otp\OtpType;


class CheckOtpRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'otp' => 'required|string',
            'phone' => 'nullable|exists:patients,phone',
            'otp_type' => ['required', 'string', function ($attribute, $value, $fail) {
                if (!in_array($value, [OtpType::REGISTER->value, OtpType::FORGOT_PASSWORD->value])) {
                    $fail('The selected otp_type is invalid.');
                }
            }]
        ];
    }
}
