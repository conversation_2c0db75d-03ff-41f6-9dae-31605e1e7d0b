<?php

namespace App\Http\Requests\Dashboard;

use App\Exceptions\DoctoriaException;
use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class LanguageRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function store()
    {
        return [
            'title' => 'required|unique:languages,title',
            'abbreviation' => 'required|unique:languages,abbreviation',
            'status' => 'required|boolean',
        ];
    }
    public function update()
    {
        return [
            'title' => ['required', Rule::unique('languages', 'title')->ignore($this->language->id)],
            'abbreviation' => ['required', Rule::unique('languages', 'abbreviation')->ignore($this->language->id)],
            'status' => 'required|boolean',
        ];
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
