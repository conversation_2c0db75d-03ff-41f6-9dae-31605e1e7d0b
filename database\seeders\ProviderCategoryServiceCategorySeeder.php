<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProviderCategoryServiceCategorySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        DB::table('provider_category_service_category')->insertOrIgnore([
            [
                'provider_category_id' => 1,
                'service_category_id' => 1,
            ],
            [
                'provider_category_id' => 2,
                'service_category_id' => 2,
            ],
            [
                'provider_category_id' => 3,
                'service_category_id' => 3,
            ],
            [
                'provider_category_id' => 4,
                'service_category_id' => 4,
            ],
            [
                'provider_category_id' => 5,
                'service_category_id' => 4,
            ],
            [
                'provider_category_id' => 5,
                'service_category_id' => 4,
            ],
            [
                'provider_category_id' => 6,
                'service_category_id' => 4,
            ],
            [
                'provider_category_id' => 7,
                'service_category_id' => 7,
            ],
            [
                'provider_category_id' => 8,
                'service_category_id' => 4,
            ],
        ]);
    }
}
