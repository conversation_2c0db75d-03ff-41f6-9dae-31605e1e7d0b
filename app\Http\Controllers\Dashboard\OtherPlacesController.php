<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\OtherPlacesResource;
use App\Http\Traits\Api_Trait;
use App\Models\OtherPlace;
use Illuminate\Http\Request;

class OtherPlacesController extends Controller
{
    use Api_Trait;

    public function index()
    {
        $places = OtherPlace::get();
        return $this->returnData(OtherPlacesResource::collection($places), [helperTrans('api.Other places data')], 200);
    }
    public function store(Request $request)
    {
        $place = OtherPlace::create($request->all());
        return $this->returnData(new OtherPlacesResource($place), [helperTrans('api.Other place created')], 201);
    }
    public function show(OtherPlace $otherPlace)
    {
        return $this->returnData(new OtherPlacesResource($otherPlace), [helperTrans('api.Other place data')], 200);
    }
    public function update(Request $request, OtherPlace $otherPlace)
    {
        $otherPlace->update($request->all());
        return $this->returnData(new OtherPlacesResource($otherPlace), [helperTrans('api.Other place updated')], 200);
    }
    public function destroy(OtherPlace $otherPlace)
    {
        $otherPlace->delete();
        return $this->returnData(new OtherPlacesResource($otherPlace), [helperTrans('api.Other place deleted')], 200);
    }
}
