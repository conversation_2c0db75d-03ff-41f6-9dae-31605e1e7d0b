<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Traits\ResponseTrait;
use App\Http\Traits\Upload_Files;
use App\Models\Provider;
use App\Models\ProviderCategory;
use App\Models\Governorate;
use App\Models\ProviderBranch;
use App\Models\ProviderService;
use App\Models\Service;
use App\Models\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Yajra\DataTables\Facades\DataTables;

class adminProviderController extends Controller
{
    use  ResponseTrait, Upload_Files;

    public function index(Request $request)
    {
        if ($request->ajax()) {

            $providers = Provider::with('provider_category')->latest()->get();
            return DataTables::of($providers)
                ->addColumn('action', function ($provider) {
                    $html = '<div class="dropdown d-inline-block">
                    <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ri-more-fill align-middle"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="' . route('admin.new_providers.edit', ['new_provider' => $provider->id]) . '"><i class="ri-pencil-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.edit') . '</a></li>
                        <li><a class="dropdown-item" href="' . route('admin.provider_branches.index', ['provider' => $provider->id]) . '"><i class="ri-building-2-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.branches') . '</a></li>
                        <li><a class="dropdown-item" href="' . route('admin.provider_services.index', ['provider' => $provider->id]) . '"><i class="ri-service-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.services') . '</a></li>
                        <li>
                            <form action="' . route('admin.new_providers.destroy', ['new_provider' => $provider->id]) . '" method="POST" class="d-inline">
                                ' . csrf_field() . '
                                ' . method_field('DELETE') . '
                                <button type="submit" class="dropdown-item remove-item-btn" onclick="return confirm(\'' . helperTrans('admin.Are you sure?') . '\')">
                                    <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.delete') . '
                                </button>
                            </form>
                        </li>
                    </ul>
                </div>';
                    return $html;
                })->addColumn('name_ar', function ($provider) {
                    return $provider->getTranslation('name', 'ar');
                })->addColumn('name_en', function ($provider) {
                    return $provider->getTranslation('name', 'en');
                })->addColumn('provider_category', function ($provider) {
                    return $provider->provider_category->name;
                })->addColumn('about', function ($provider) {
                    return $provider->about;
                })->editColumn('working_days', function ($provider) {
                    return $provider->working_days;
                })->rawColumns(['action'])
                ->make(true);
        }
        return view('Admin.CRUDS.new_providers.index');
    }

    public function create()
    {
        $providerCategories = ProviderCategory::get();
        $governorates = Governorate::get();
        return view('Admin.CRUDS.new_providers.create', compact('providerCategories', 'governorates'));
    }

    public function store(Request $request)
    {
        $data = $request->except('password_confirmation');
        $data['phone'] = "+2" . $request->phone;
        // Hash the password
        $data['password'] = Hash::make($request->password);

        if ($request->hasFile('image')) {
            $data["image"] = $this->uploadiles('providers', $request->file('image'), null);
        }

        $provider = Provider::create($data);

        $service_category_id = ServiceCategory::where('provider_category_id', $provider->provider_category_id)?->first()?->id;

        $services = Service::where('service_category_id', $service_category_id)->get();

        foreach ($services as $service) {
            ProviderService::create([
                'provider_id' => $provider->id,
                'service_id' => $service->id,
                'price' => 1,
                'discount' => $provider->discount,
                // 'status' => 'active'
            ]);
        }

        return redirect()->route('admin.new_providers.index')
            ->with('success', helperTrans('admin.Provider created successfully.'));
    }

    public function edit(Provider $new_provider)
    {
        $providerCategories = ProviderCategory::all();
        $governorates = Governorate::all();
        return view('Admin.CRUDS.new_providers.edit', compact('new_provider', 'providerCategories', 'governorates'));
    }

    public function update(Request $request, Provider $new_provider)
    {
        $request->validate([
            'name.ar' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'email' => 'nullable|email|unique:providers,email,' . $new_provider->id,
            'phone' => 'nullable|string|max:20',
            'password' => 'nullable|min:8|confirmed',
            'provider_category_id' => 'nullable|exists:provider_categories,id',
            'governorate_id' => 'nullable|exists:governorates,id',
            'city_id' => 'nullable|exists:cities,id',
            'location' => 'nullable|string|max:255',
            'discount' => 'nullable|numeric|min:0|max:100',
            'surgery_discount' => 'nullable|numeric|min:0|max:100',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $data = $request->except(['password', 'image', 'password_confirmation']);

        if ($request->filled('password')) {
            $data['password'] = Hash::make($request->password);
        }


        if ($request->hasFile('image')) {
            $data["image"] = $this->uploadFiles('providers', $request->file('image'), $new_provider->image);
        }
        if ($request->provider_category_id != $new_provider->provider_category_id) {
            ProviderService::where('provider_id', $new_provider->id)->delete();
            $service_category_id = ServiceCategory::where('provider_category_id', $request->provider_category_id)->first()?->id;
            $services = Service::where('service_category_id', $service_category_id)->get();
            foreach ($services as $service) {
                ProviderService::create([
                    'provider_id' => $new_provider->id,
                    'service_id' => $service->id,
                    'price' => 1,
                    'discount' => $new_provider->discount,
                ]);
            }
        }


        $new_provider->update($data);

        return redirect()->route('admin.new_providers.index')
            ->with('success', helperTrans('admin.Provider updated successfully'));
    }

    public function destroy(Provider $new_provider)
    {
        ProviderService::where('provider_id', $new_provider->id)->delete();
        ProviderBranch::where('provider_id', $new_provider->id)->delete();
        $new_provider->delete();

        return redirect()->route('admin.new_providers.index')
            ->with('success', helperTrans('admin.Provider deleted successfully.'));
    }
}
