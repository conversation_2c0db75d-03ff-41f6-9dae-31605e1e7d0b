<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\PriceListRequest;
use App\Http\Traits\Api_Trait;
use Illuminate\Http\Request;
use App\Models\PriceList;
use App\Models\Service;
use App\Http\Resources\Dashboard\PriceListResource;

class PriceListController extends Controller
{
    use Api_Trait;
    public function index(Request $request)
    {
        $price_lists = PriceList::get();
        return $this->returnData(PriceListResource::collection($price_lists), [helperTrans('api.Price List Data')], 200);
    }

    public function show($id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }
        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }
    public function store(PriceListRequest $request)
    {
        $price_list = PriceList::create($request->validated());
        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }
    public function update(PriceListRequest $request, $id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }
        $price_list->update($request->validated());
        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }
    public function destroy($id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }
        $price_list->delete();
        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }

}
