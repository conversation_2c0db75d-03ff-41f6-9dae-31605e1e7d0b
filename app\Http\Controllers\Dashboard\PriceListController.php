<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\PriceListRequest;
use App\Http\Traits\Api_Trait;
use Illuminate\Http\Request;
use App\Models\PriceList;
use App\Models\Service;
use App\Http\Resources\Dashboard\PriceListResource;
use App\Models\ProviderCategoryServiceCategory;

class PriceListController extends Controller
{
    use Api_Trait;
    public function index(Request $request)
    {
        // Validate required parameter
        $request->validate([
            'provider_category_id' => 'required|exists:provider_categories,id'
        ]);

        $provider_service_category = ProviderCategoryServiceCategory::where('provider_category_id', $request->provider_category_id)->first();

        // Check if provider_service_category exists
        if (!$provider_service_category) {
            return $this->returnError(helperTrans('api.Provider Category Service Category Not Found'), 404);
        }

        $price_lists = PriceList::when($request->provider_category_id, function ($query) use ($provider_service_category) {
            $query->whereHas('service_categories', function ($q) use ($provider_service_category) {
                $q->where('service_category_id', $provider_service_category->service_category_id);
            });
        })->orderBy('id', 'desc')->get();

        return $this->returnData(PriceListResource::collection($price_lists), [helperTrans('api.Price List Data')], 200);
    }

    public function show($id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }
        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }
    public function store(PriceListRequest $request)
    {
        $price_list = PriceList::create($request->validated());
        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }
    public function update(PriceListRequest $request, $id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }
        $price_list->update($request->validated());
        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }
    public function destroy($id)
    {
        $price_list = PriceList::find($id);
        if (!$price_list) {
            return $this->returnError(helperTrans('api.Price List Not Found'), 404);
        }
        $price_list->delete();
        return $this->returnData(PriceListResource::make($price_list), [helperTrans('api.Price List Data')], 200);
    }
}
