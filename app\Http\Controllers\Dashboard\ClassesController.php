<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\ClassResource;
use App\Http\Requests\Dashboard\ClassRequest;
use App\Http\Traits\Api_Trait;
use App\Models\Classe;
use Illuminate\Http\Request;

class ClassesController extends Controller
{
    use Api_Trait;

    /**
     * Display a listing of classes with filtering and pagination
     */
    public function index(Request $request)
    {
        $classes = Classe::with(['patients', 'providers'])
            ->when($request->search, function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->search . '%')
                    ->orWhere('notes', 'like', '%' . $request->search . '%');
            })
            ->when($request->name, function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->name . '%');
            })
            ->orderBy($request->sort_by ?? 'name', $request->sort_direction ?? 'asc')
            ->paginate($request->per_page ?? 25);

        return $this->paginatedResponse(
            ClassResource::collection($classes),
            helperTrans('api.Classes Data'),
            200
        );
    }

    /**
     * Store a newly created class
     */
    public function store(ClassRequest $request)
    {
        $data = $request->validated();

        $classe = Classe::create($data);

        return $this->returnData(
            new ClassResource($classe->load(['patients', 'providers'])),
            [helperTrans('api.Class created successfully')],
            201
        );
    }

    /**
     * Display the specified class
     */
    public function show($id)
    {
        $class = Classe::find($id);
        if (!$class) {
            return $this->returnError(helperTrans('api.Class Not Found'), 404);
        }
        $class->load(['patients', 'providers']);

        return $this->returnData(
            new ClassResource($class),
            [helperTrans('api.Class Data')],
            200
        );
    }

    /**
     * Update the specified class
     */
    public function update(ClassRequest $request, $id)
    {
        $class = Classe::find($id);
        if (!$class) {
            return $this->returnError(helperTrans('api.Class Not Found'), 404);
        }
        $data = $request->validated();

        $class->update($data);

        return $this->returnData(
            new ClassResource($class->load(['patients', 'providers'])),
            [helperTrans('api.Class updated successfully')],
            200
        );
    }
    /**
     * Remove the specified class
     */
    public function destroy($id)
    {
        $class = Classe::find($id);
        if (!$class) {
            return $this->returnError(helperTrans('api.Class Not Found'), 404);
        }
        // Check if class has associated patients or providers
        if ($class->patients()->exists() || $class->providers()->exists()) {
            return $this->returnError([
                'message' => 'Cannot delete class that has associated patients or providers'
            ], 422);
        }

        $class->delete();

        return $this->returnData(
            null,
            [helperTrans('api.Class deleted successfully')],
            200
        );
    }
}
