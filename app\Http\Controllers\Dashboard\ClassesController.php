<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\ClassesResource;
use App\Http\Traits\Api_Trait;
use App\Models\Classe;
use Illuminate\Http\Request;

class ClassesController extends Controller
{
    use Api_Trait;
    public function index()
    {
        $classes = Classe::get();
        return $this->returnData(
            ClassesResource::collection($classes),
            [helperTrans('api.classes data')],
            200
        );
    }

    public function store(Request $request)
    {
        $classe = Classe::create([
            'name' => $request->name,
            'notes' => $request->notes,
        ]);
        return $this->returnData(
            new ClassesResource($classe),
            [helperTrans('api.classe created')],
            201
        );
    }

    public function show(Classe $class)
    {
        return $this->returnData(
            new ClassesResource($class),
            [helperTrans('api.classe show')],
            200
        );
    }

    public function update(Request $request, Classe $class){
        $class->update([
            'name' => $request->name,
            'notes' => $request->notes,
        ]);
        return $this->returnData(
            new ClassesResource($class),
            [helperTrans('api.classe updated')],
            200
        );
    }
    public function destroy(Classe $class)
    {
        $class->delete();
        return $this->returnData(
            new ClassesResource($class),
            [helperTrans('api.classe deleted')],
            200
        );
    }

}
