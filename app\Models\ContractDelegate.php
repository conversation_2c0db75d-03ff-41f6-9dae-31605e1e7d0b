<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractDelegate extends Model
{
    use HasFactory;
    protected $table = 'contract_delegates';
    protected $fillable = [
        'contract_id',
        'delegate_manager_id',
    ];
    protected $guarded = [];
    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }
    public function delegateManager()
    {
        return $this->belongsTo(DelegateManager::class);
    }
}
