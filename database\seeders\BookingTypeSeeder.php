<?php

namespace Database\Seeders;

use App\Models\BookingType;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class BookingTypeSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $bookingTypes = [
            [
                'id' => 1,
                'name' => 'General',
                'notes' => 'General',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'name' => 'Provider',
                'notes' => 'Provider',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];
        Schema::disableForeignKeyConstraints();
        BookingType::truncate();
        Schema::enableForeignKeyConstraints();
        
        BookingType::insert($bookingTypes);
    }
}
