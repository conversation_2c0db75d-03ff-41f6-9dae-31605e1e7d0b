<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Foundation\Http\FormRequest;

class ProviderBranchRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */

     public function store()
     {
          return [
             "provider_id" => ['required', 'exists:providers,id'],
             "name" => ['required', 'array'],
             'name.ar' => ['required', 'string', 'max:255'],
             'name.en' => ['required', 'string', 'max:255'],
             "phone" => ['required', 'string', 'max:20', 'unique:providers,phone'],
             'status' => ['nullable', 'in:active,inactive'],
             "country_id" => ['required', 'exists:countries,id'],
             "governorate_id" => ['required', 'exists:governorates,id'],
             "city_id" => ['required', 'exists:cities,id'],
             "longitude" => ['nullable', 'string'],
             "latitude" => ['nullable', 'string'],
             "location" => ['required', 'array'],
             'location.ar' => ['required', 'string', 'max:255'],
             'location.en' => ['required', 'string', 'max:255'],
             'email' => ['nullable', 'string', 'max:255'],
             "whatsapp_number" => ['nullable', 'string'],
             "tel1" => ['nullable', 'string'],
             "tel2" => ['nullable', 'string'],
             "website_link" => ['nullable', 'string'],
             "times" => ['nullable', 'array'],
             'times.ar' => ['nullable', 'string', 'max:255'],
             'times.en' => ['nullable', 'string', 'max:255'],
             "assistant_name" => ['nullable', 'string', 'max:255'],
             "assistant_phone" => ['nullable', 'string'],
             "assistant_password" => ['nullable', 'string'],
             "assistant_email" => ['nullable', 'string']
         ];
     }

     public function update()
     {
         return  [
            "provider_id" => ['required', 'exists:providers,id'],
            "name" => ['required', 'array'],
            'name.ar' => ['required', 'string', 'max:255'],
            'name.en' => ['required', 'string', 'max:255'],
            "phone" => ['required', 'string', 'max:20', 'unique:providers,phone'],
            'status' => ['nullable', 'in:active,inactive'],
            "country_id" => ['required', 'exists:countries,id'],
            "governorate_id" => ['required', 'exists:governorates,id'],
            "city_id" => ['required', 'exists:cities,id'],
            "longitude" => ['nullable', 'string'],
            "latitude" => ['nullable', 'string'],
            "location" => ['required', 'array'],
            'location.ar' => ['required', 'string', 'max:255'],
            'location.en' => ['required', 'string', 'max:255'],
            "whatsapp_number" => ['nullable', 'string'],
            "tel1" => ['nullable', 'string'],
            "tel2" => ['nullable', 'string'],
            "website_link" => ['nullable', 'string'],
            "times" => ['nullable', 'array'],
            'times.ar' => ['nullable', 'string', 'max:255'],
            'times.en' => ['nullable', 'string', 'max:255'],
            "assistant_name" => ['nullable', 'string', 'max:255'],
            "assistant_phone" => ['nullable', 'string'],
            "assistant_password" => ['nullable', 'string'],
            "assistant_email" => ['nullable', 'string']
        ];
     }

     public function rules()
     {
         if ($this->isMethod('POST')) {
             return $this->store();
         }
         if ($this->isMethod('PUT')) {
             return $this->update();
         }
     }
}
