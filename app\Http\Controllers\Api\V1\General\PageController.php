<?php

namespace App\Http\Controllers\Api\V1\General;

use App\Http\Controllers\Controller;
use App\Models\Page;
use App\Http\Traits\Api_Trait;
use App\Http\Resources\PageResource;
use Illuminate\Http\Request;

class PageController extends Controller
{
    use Api_Trait;
    public function index(Request $request)
    {
        $language = $request->header('Accept-Language', 'en');
        $pages = Page::active()->get();
        $transformedPages = $pages->map(function ($page) use ($language) {
            return [
                'id' => $page->id,
                'name' => $page->getTranslation('name', $language),
                'title' => $page->getTranslation('title', $language),
                'description' => $page->getTranslation('description', $language),
                'link' => $page->link,
                'type' => $page->type,
                'status' => $page->status,
                'image' => get_file($page->image),
                'icon' => get_file($page->icon),
            ];
        });
        return $this->returnData($transformedPages, [helperTrans('api.page data retrieved successfully')], 200);
    }
}
