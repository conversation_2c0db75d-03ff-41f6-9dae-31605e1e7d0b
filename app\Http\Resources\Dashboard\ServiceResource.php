<?php

namespace App\Http\Resources\Dashboard;

use App\Http\Resources\Dashboard\ServiceSubCategoryResource;
use App\Http\Resources\Dashboard\ServiceCategoryResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'common_name' => $this->common_name,
            'type' => $this->type,
            // 'active' => $this->status=='active',
            // 'service_category' => ServiceCategoryResource::make($this->service_category),
            // 'service_sub_category' => ServiceSubCategoryResource::make($this->service_sub_category),
        ];
    }
}
