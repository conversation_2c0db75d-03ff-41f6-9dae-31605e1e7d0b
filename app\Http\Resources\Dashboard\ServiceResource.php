<?php

namespace App\Http\Resources\Dashboard;

use App\Http\Resources\Dashboard\ServiceSubCategoryResource;
use App\Http\Resources\Dashboard\ServiceCategoryResource;
use Illuminate\Http\Resources\Json\JsonResource;

class ServiceResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name_ar' => $this->getTranslation('name', 'ar'),
            'name_en' => $this->getTranslation('name', 'en'),
            'common_name_ar' => $this->getTranslation('common_name', 'ar'),
            'common_name_en' => $this->getTranslation('common_name', 'en'),
            'type' => $this->type,
            'status' => $this->status ?? 'available',
            'service_category' => ServiceCategoryResource::make($this->service_category),
            'service_sub_category' => ServiceSubCategoryResource::make($this->service_sub_category),
        ];
    }
}
