<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class SpecializationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'name_ar' => $this->getTranslation('name', 'ar'),
            'name_en' => $this->getTranslation('name', 'en'),
            'sub_speciailizations' => $this->subSpecializations,
            'color' => $this->color,
            'parent_id' => $this->parent_id,
            'icon' => get_file($this->icon),
            'image' => get_file($this->image),
            // 'limitPopularDoctors' => DoctorResource::collection($this->whenLoaded('limitPopularDoctors')),

        ];
    }
}
