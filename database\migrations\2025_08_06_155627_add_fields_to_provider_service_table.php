<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('provider_service', function (Blueprint $table) {
            $table->foreignId('service_category_id')->after('provider_id')->nullable()->constrained('service_categories')->onDelete('set null');
            $table->foreignId('service_sub_category_id')->after('service_category_id')->nullable()->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('provider_service', function (Blueprint $table) {
            $table->dropForeign(['service_category_id']);
            $table->dropForeign(['service_sub_category_id']);
            $table->dropColumn('service_category_id');
            $table->dropColumn('service_sub_category_id');
        });
    }
};
