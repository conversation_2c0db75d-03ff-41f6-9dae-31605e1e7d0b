<?php
namespace App\Http\Controllers\Api\Auth;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Traits\Api_Trait;
use App\Models\{Doctor, Patient};
use Illuminate\Support\Str;
use App\Http\Requests\Api\Auth\{ForgotPasswordRequest, ResetPasswordRequest};
use App\Models\OtpCode;
use Illuminate\Support\Facades\{Mail,Hash};
use App\Mail\SendOtpMail;
use Carbon\Carbon;
use App\Services\Otp\OtpServiceManager;
use Illuminate\Support\Facades\Validator;
use App\Services\SmsService;



use App\Enums\Auth\Otp\OtpType;
class ResetPasswordController extends Controller {
    use Api_Trait;
    
    public function __construct(protected OtpServiceManager $otpServiceManager,protected SmsService $smsService) {
        $this->otpServiceManager = $otpServiceManager;
        $this->smsService = $smsService;

    }

    protected function getBroker($userType) {
        return $userType === 'patient' ? 'patient' : 'doctor';
    }

    public function checkOtp(Request $request) {
        //$request->validate(['otp' => 'required|string']);
        $request->validate([
            'otp' => 'required|string',
            'email' => 'nullable|exists:patients,email',
            'phone' => 'nullable|exists:patients,phone',            'otp_type' => ['required', 'string', function ($attribute, $value, $fail) {
                if (!in_array($value, [OtpType::REGISTER->value, OtpType::FORGOT_PASSWORD->value])) {
                    $fail('The selected otp_type is invalid.');
                }
            }]
        ]);
        if($request->email){
            $patient = Patient::where('email', $request->email)->first();
            if (!$patient){
                return $this->returnErrorDataNotFound([helperTrans('api.Otp not found.')]);
            }
            $otpCode = OtpCode::where('otp', $request->otp)
         
                ->where('phone_number', $patient->phone)
                ->first();
        }else{
            $otpCode = OtpCode::where('otp', $request->otp)
                
                ->where('phone_number', $request->phone)
                ->first();
        }
        if (!$otpCode){
          return $this->returnErrorDataNotFound([helperTrans('api.Otp not found.')]);
        }
            
        if (Carbon::now()->greaterThan($otpCode->expires_at)) {
            $otpCode->update(['status' => 'invalid']);
            return $this->returnInvalidData([helperTrans('api.OTP has expired')]);
        }
        if ($otpCode->otp_type->value === OtpType::REGISTER->value) {
            $patient = Patient::find($otpCode->otpable_id);
            if ($patient) {
                $patient->status = 1;
                $patient->save();
            } else {
                return $this->returnErrorDataNotFound([helperTrans('api.Patient not found')]);
            }
        }
        return $this->returnData(
            [
                'otp' => $otpCode->otp,
                'is_sent' => $otpCode->is_sent ? 'valid' : 'invalid',
                'otpStatus' => $otpCode->status,
                'otp_type' => $otpCode->otp_type,
                'time_remaining' => $otpCode->time_remaining . ' minutes',
            ],
            ['Get Otp Successfully']
        );
    }

    public function forgotPassword(Request $request) {
        if ($request->driver == 'email') {
            $validator = Validator::make($request->all(), [
                'data' => [
                    'required',
                    'email',
                    function ($attribute, $value, $fail) {
                        $allowedDomains = ['gmail.com', 'yahoo.com', 'outlook.com'];
                        $emailDomain = substr(strrchr($value, "@"), 1);
                        if (!in_array($emailDomain, $allowedDomains))
                            $fail('Only gmail, yahoo, and outlook accounts are allowed.');
                    },
                ],
                'otp_type' => ['required', 'string', function ($attribute, $value, $fail) {
                    if (!in_array($value, [OtpType::REGISTER->value, OtpType::FORGOT_PASSWORD->value])) {
                        $fail('The selected otp_type is invalid.');
                    }
                }]
            ]);
            if ($validator->fails()) {
                return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
            }
            $user = OtpServiceManager::getUserByEmail($request->data);
            if (!$user){
                return $this->returnErrorDataNotFound([helperTrans('api.User not found')]);
                }
                $user->phone_number = $user->phone;
                
            $otpService = $this->otpServiceManager->getService($request->driver);
            $otpType = $this->otpServiceManager->getOtpType($request->otp_type);
            if($request->otp_type == 'register'){
                $otpService->sendOtp($user, $request->driver, $otpType->value);
                return $this->returnSuccessDataMessage([helperTrans('api.check your email please')]);
            }
            $otpService->sendOtp($user, $request->driver, $otpType->value);
            return $this->returnSuccessDataMessage([helperTrans('api.OTP sent to your email')]);
        } elseif ($request->driver == 'whatsapp') {
            // التعامل مع واتساب
        } elseif ($request->driver == 'sms') {
            $validator = Validator::make($request->all(), [
                'data' => [
                    'required',
                ],
                'otp_type' => ['required', 'string', function ($attribute, $value, $fail) {
                    if (!in_array($value, [OtpType::REGISTER->value, OtpType::FORGOT_PASSWORD->value])) {
                        $fail('The selected otp_type is invalid.');
                    }
                }]
            ]);
            if ($validator->fails()) {
                return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
            }
            $user = Patient::where('phone',$request->data)->first();
            if (!$user){
                return $this->returnErrorDataNotFound([helperTrans('api.User not found')]);
            }
            $smsService = new SmsService();
            $response = $smsService->sendOtp($user->phone);
            return $this->returnSuccessMessage([helperTrans('api.check your phone SMS please')]);
        } else {
            return $this->returnErrorDataNotFound('Invalid driver.');
        }
    }

    public function resetPassword(Request $request) {
        if($request->driver == 'email'){
            $request->validate([
                'data' => 'required|exists:patients,email',
               'password' => 'required|min:8|max:12'
            ]);
            $user = Patient::where('email', $request->data)->first();
        }else{
            $request->validate([
                'data' => 'required|exists:patients,phone',
               'password' => 'required|min:8|max:12'
            ]);
            $user = Patient::where('phone', $request->data)->first();
        }
        if (!$user){
            return $this->returnErrorDataNotFound([helperTrans('api.patient Not Found.')]);
        }
        $user->password = Hash::make($request->password);
        $user->save();
        return $this->returnSuccessDataMessage([helperTrans('api.Password reset successful')]);
    }

}
