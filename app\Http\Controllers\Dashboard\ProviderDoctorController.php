<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\DoctorResource;
use App\Models\Doctor;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ProviderDoctorController extends Controller
{
    public function index(Request $request)
    {

   $doctors = Doctor::where('status', 1)->where('doctor_type','specialized');


   if ($request->specialization_id) {
       $specialization_id   = $request->input('specialization_id');
       $doctors->where('specialization_id', $specialization_id);
   }

   if ($request->gender) {
       $gender  = $request->input('gender');
       $doctors->where('gender', $gender);
   }

   if ($request->governorate_id) {
       $doctors->where('governorate_id', $request->governorate_id);
   } else if ($request->city_id) {
       $doctors->where('governorate_id', $request->city_id);
   }

   return response()->json(
       [
           'data' => DoctorResource::collection($doctors->get()),
           'other_doctors' => collect([]),
           "message" => "Doctors data",
           "status" => 200
       ],
       200
   );
    }



    public function store(Request $request)
    {
        try {
            DB::beginTransaction();

            $provider_data = collect($request->validated())->except([
                'site_category',
                'time',
                'main_branch_name',
                'main_branch_status',
                'assistant_name',
                'assistant_phone',
                'assistant_email',
                'assistant_password',
            ])->toArray();

            $provider_data['location'] = json_encode($request->location);
            $provider_data['language'] = json_encode($request->language);
            $provider_data['password'] = Hash::make($request->password);

            if ($request->hasFile('image')) {
                $provider_data["image"] = $this->uploadiles('providers', $request->file('image'), null);
            }

            $provider = Provider::create($provider_data);

            if ($request->site_category == 'branch') {

                $provider_branch = ProviderBranch::create([
                    'provider_id' => $provider->id,
                    'name' => $request->main_branch_name,
                    'phone' => $request->phone,
                    'degree' => "main",
                    'country_id' => $request->country_id,
                    'governorate_id' => $request->governorate_id,
                    'city_id' => $request->city_id,
                    'location' => json_encode($request->location),
                    'longitude' => $request->longitude,
                    'latitude' => $request->latitude,
                    'status' => $request->main_branch_status,
                ]);

                $assistant = Assistant::create([
                    'name' => $request->assistant_name,
                    'phone' => $request->assistant_phone,
                    'email' => $request->assistant_email,
                    'password' => $request->assistant_password,
                ]);

                AssistantBranch::create([
                    'assistant_id' => $assistant->id,
                    'provider_branch_id' => $provider_branch->id,
                ]);
            }


            DB::commit();

            return $this->returnData(ProviderResource::make($provider), [helperTrans('api.Provider Data')], 200);
        } catch (\Exception $e) {
            DB::rollBack();

            throw new DoctoriaException($e->getMessage());
        }
    }
}
