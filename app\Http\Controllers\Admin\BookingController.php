<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\ResponseTrait;
use App\Models\Booking;
use App\Models\Notification;
use App\Models\RequestBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\DataTables;

class BookingController extends Controller
{
    //
    use  ResponseTrait;

    public function index(Request $request)
    {

        if ($request->ajax()) {
            $admins = Booking::with(['patient', 'doctor', 'mainService'])->latest();
            if ($request->date) {
                $admins->where('day', $request->date);
            }
            return DataTables::of($admins)
                ->setRowAttr([
                    'style' => function ($row) {
                        return $row->read_at == null ? 'background-color: #f8d7da;' : '';
                    }
                ])
                ->addColumn('mark_read', function ($row) {
                    if ($row->read_at)
                        return '<button class="btn btn-sm btn-secondary disabled">Readed</button>';
                    else
                        return '<button class="btn btn-sm btn-primary mark-read-booking" data-id="' . $row->id . '">Mark as Read</button>';
                })
                ->addColumn('action', function ($admin) {

                    $edit = '';
                    $delete = '';
                    $view = '<a href="' . route('bookings.show', $admin->id) . '" class="btn rounded-pill btn-info waves-effect waves-light me-1" title="View Details">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-eye"></i>
                                </span>
                            </a>';

                    return $view . '
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $admin->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';
                })

                ->editColumn('main_service_id', function ($row) {
                    return $row->mainService->name ?? '';
                })
                ->editColumn('patient_id', function ($row) {
                    if ($row->relative_id)
                        return $row->patient->name . " / " . $row->relative->first_name;
                    else
                        return $row->patient->name ?? '';
                })->addColumn('patient_phone', function ($row) {
                    return $row->patient->phone ?? '';
                })
                ->editColumn('day', function ($row) {
                    return date('Y/m/d', strtotime($row->day)) . " " . date('H:i', strtotime($row->time));
                })
                ->editColumn('status', function ($row) {
                    $status = '';
                    if ($row->status == 'pending')
                        $status = "<span class='badge bg-primary'>" . helperTrans('admin.Pending') . "</span>";
                    elseif ($row->status == 'active')
                        $status = "<span class='badge bg-success'>" . helperTrans('admin.active with Dr') . " </br>" . $row->doctor->name ?? '' . " </span>";
                    elseif ($row->status == 'complete')
                        $status = "<span class='badge bg-success'>" . helperTrans('admin.Completed') . "</span>";
                    elseif ($row->status == 'cancel')
                        $status = "<span class='badge bg-danger'>" . helperTrans('admin.Cancelled') . "</span>";
                    else
                        $status = "<span class='badge bg-info'>" . $row->status . "</span>";

                    return $status;
                })

                ->editColumn('price', function ($row) {

                    $price = $row->price;
                    if ($row->operation_type == 'package')
                        $price = '<span class="badge bg-secondary">' . helperTrans('admin.Package') . '</span>';

                    return $price;
                })


                ->addColumn('docs', function ($row) {

                    $route = $row->docs->first();

                    return $route ? "<a href='$route' target='_blank' class=''><i class='fa fa-file'></i></a>" : '-';
                })
                ->addColumn('doctor', function ($row) {
                    return $row->doctor?->name ?? '-';
                })
                ->addColumn('reply', function ($row) {
                    if (in_array($row->status, ['complete', 'reappoint', 'referral', 'specialized'])) {
                        $reply = \App\Models\ReplyingBooking::where('booking_id', $row->id)->first();
                        if ($reply) {
                            return "<a href='javascript:void(0)' class='btn btn-sm btn-info view-reply' data-id='{$row->id}'>
                                <i class='las la-eye'></i>
                            </a>";
                        }
                    }
                    return '-';
                })



                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d H:i', strtotime($admin->created_at));
                })
                ->editColumn('updated_at', function ($admin) {
                    return date('Y/m/d H:i', strtotime($admin->updated_at));
                })
                ->escapeColumns([])
                ->rawColumns(['action', 'status', 'reply', 'mark_read'])
                ->make(true);
        }
        return view('Admin.CRUDS.booking.index');
    }

    public function show($id)
    {
        $booking = Booking::with([
            'patient',
            'doctor',
            'mainService',
            'replying.replyingBookingAnalysis.analysis',
            'replying.replyingBookingAnalysis.laboratory',
            'replying.replyingBookingMedicals.medicationUnit',
            'replying.replyingBookingMedicals.pharmacy',
            'replying.replyingBookingRadiology.radiology',
            'replying.replyingBookingRadiology.radiologyCenter',
            'replying.replyingBookingDiagnoses.diagnosis'
        ])->findOrFail($id);
        return view('Admin.CRUDS.booking.show', compact('booking'));
    }

    public function markAsRead($id)
    {
        try {
            $booking = Booking::findOrFail($id);

            if (is_null($booking->read_at)) {
                $booking->read_at = now();
                $booking->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Booking marked as read successfully'
                ]);
            }

            return response()->json([
                'status' => true,
                'message' => 'Booking was already marked as read'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function destroy($id)
    {
        $row = Booking::findOrFail($id);

        Notification::where('type', 'booking')->where('foreign_id', $id)->delete();

        $row->delete();

        return $this->deleteResponse();
    } //end fun

    public function booking_docs($id)
    {

        $row = Booking::with(['docs'])->findOrFail($id);

        return view('Admin.CRUDS.booking.docs', compact('row'));
    }

    public function request_booking_docs($id)
    {

        $row = RequestBooking::with(['docs'])->findOrFail($id);

        return view('Admin.CRUDS.requestBooking.docs', compact('row'));
    }

    public function getReply(Request $request)
    {
        try {
            $validator = Validator::make($request->all(), [
                'booking_id' => 'required|exists:bookings,id'
            ]);

            if ($validator->fails()) {
                return response()->json([
                    'status' => false,
                    'message' => $validator->errors()->first()
                ]);
            }

            $reply = \App\Models\ReplyingBooking::with([
                'replyingBookingAnalysis',
                'replyingBookingMedicals',
                'replyingBookingRadiology',
                'replyingBookingDiagnoses'
            ])->where('booking_id', $request->booking_id)->first();

            if (!$reply) {
                return response()->json([
                    'status' => false,
                    'message' => helperTrans('admin.No reply found')
                ]);
            }


            return response()->json([
                'status' => true,
                'message' => helperTrans('admin.Reply data retrieved successfully'),
                'data' => $reply
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ]);
        }
    }
}
