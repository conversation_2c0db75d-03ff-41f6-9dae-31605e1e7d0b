<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Foundation\Http\FormRequest;

class PriceListServiceRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        } else {
            return $this->update();
        }
        return [];
    }

    public function store()
    {
        return [
            "service_category_id" => ['required', 'exists:service_categories,id'],
            "service_sub_category_ids" => ['required'],
            "service_ids" => ['nullable'],
        ];
    }
    public function update()
    {
        return [
            'services' => 'required|array|min:1',
            'services.*.id' => 'required|exists:services,id',
            'services.*.price' => 'required|numeric|min:0',
        ];
    }
}
