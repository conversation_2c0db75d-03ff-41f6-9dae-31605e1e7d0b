<?php

namespace App\Http\Resources\Api\V2;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DoctorReviewsResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'id' => (int) $this->id,
            'patient_name' => $this->patient?->name,
            'patient_image' => asset($this->patient?->image) ?? null,
            'rating' => (int) $this->rating,
            'comment' => $this->comment,
        ];
    }
}
