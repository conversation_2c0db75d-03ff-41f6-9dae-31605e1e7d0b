<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PackageRequest;
use App\Http\Traits\ResponseTrait;
use App\Models\MainService;
use App\Models\MainServicePackage;
use App\Models\Package;
use App\Models\PackageRequest as ModelsPackageRequest;
use App\Models\Specialization;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;

class PackageController extends Controller
{
    //
    use  ResponseTrait;

    public function index(Request $request)
    {

        if ($request->ajax()) {
            $admins = Package::query()->latest();
            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {

                    $edit = '';
                    $delete = '';


                    return '
                            <button ' . $edit . '  class="editBtn btn rounded-pill btn-primary waves-effect waves-light"
                                    data-id="' . $admin->id . '"
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-edit"></i>
                                </span>
                            </span>
                            </button>
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $admin->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';
                })

                ->editColumn('name', function ($row) {
                    return $row->name ?? '';
                })


                ->editColumn('gc_specialization_id', function ($row) {
                    return $row->specialization->name ?? '';
                })




                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);
        }
        $mainServices = MainService::get();

        return view('Admin.CRUDS.package.index', compact('mainServices'));
    }


    public function create()
    {
        $mainServices = MainService::get();
        return view('Admin.CRUDS.package.parts.create', compact('mainServices'));
    }

    public function store(PackageRequest $request)
    {
        $data = $request->validationData();
        unset($data['count']);
        unset($data['main_service_id']);
        $package = Package::create($data);
        if ($request->main_service_id)
            for ($i = 0; $i < count($request->main_service_id); $i++) {
                MainServicePackage::create([
                    'package_id' => $package->id,
                    'main_service_id' => $request->main_service_id[$i],
                    'count' => $request->count[$i],
                ]);
            }
        return $this->addResponse();
    }


    public function show($id)
    {


        //
    }


    public function edit($id)
    {
        $row = Package::findOrFail($id);
        $mainServices = MainService::get();
        $packageMainServices = MainServicePackage::where('package_id', $id)->get();

        return view('Admin.CRUDS.package.parts.edit', compact('row', 'mainServices', 'packageMainServices'));
    }

    public function update(PackageRequest $request, $id)
    {
        $row = Package::findOrFail($id);
        $data = $request->validationData();
        unset($data['count']);
        unset($data['main_service_id']);
        $row->update($data);

        if ($request->main_service_id) {
            MainServicePackage::where('package_id', $id)->delete();

            for ($i = 0; $i < count($request->main_service_id); $i++) {
                MainServicePackage::updateOrCreate([
                    'package_id' => $id,
                    'main_service_id' => $request->main_service_id[$i],
                    'count' => $request->count[$i],
                ]);
            }
        } else {
            MainServicePackage::where('package_id', $id)->delete();
        }
        return $this->updateResponse();
    }


    public function destroy($id)
    {
        $row = Package::findOrFail($id);
        $row->delete();
        return $this->deleteResponse();
    } //end fun

    public function package_requests(Request $request)
    {
        // if ($request->ajax()) {
        // $package_requests = PackageRequest::all();

        if ($request->ajax()) {
            $package_requests = ModelsPackageRequest::latest()->get();
            return DataTables::of($package_requests)
                ->addColumn('action', function ($package_requests) {

                    $actions = "";

                    if ($package_requests->status == 'pending') {
                        return '
                                <a href="' . route('admin.package_requests.change_status', ['id' => $package_requests->id, 'status' => 'active']) . '"   class="btn rounded-pill btn-info w-25"  style="min-width: 70px;"
                                        data-id="' . $package_requests->id . '"
                                <span class="svg-icon svg-icon-3">
                                    <span class="svg-icon svg-icon-3">
                                        Active
                                    </span>
                                </span>
                                </a>
                                <a href="' . route('admin.package_requests.change_status', ['id' => $package_requests->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;" "
                                data-id="' . $package_requests->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                               ';
                    } else if ($package_requests->status == 'active') {
                        return  '
                        <a href="' . route('admin.package_requests.change_status', ['id' => $package_requests->id, 'status' => 'complete']) . '"   class="btn rounded-pill btn-success w-25" style="min-width: 70px;"
                                data-id="' . $package_requests->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Complete
                            </span>
                        </span>
                        </a>
                        <a href="' . route('admin.package_requests.change_status', ['id' => $package_requests->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;""
                                data-id="' . $package_requests->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                       ';
                    }
                })
                // ->editColumn('name', function ($row) {
                //     if (is_json($row->name)) {
                //         $nameData = json_decode($row->name, true);
                //         $nameAr = $nameData['ar'] ?? 'N/A';
                //         $nameEn = $nameData['en'] ?? 'N/A';
                //         return '<span style="color:red;">' . htmlspecialchars($nameAr) . '</span> / <span style="color:blue;">' . htmlspecialchars($nameEn) . '</span>';
                //     }
                //     if (is_string($row->name)) {
                //         return htmlspecialchars($row->name);
                //     }
                // })
                ->editColumn('created_at', function ($provider_request) {

                    return "<div>" . date('Y/m/d', strtotime($provider_request->created_at)) . "</div>"
                        . "<div>" . date('h:i A', strtotime($provider_request->created_at)) . "</div>";
                })
                ->addColumn('patient_name', function ($provider_request) {

                    return $provider_request->patient->name ?? '';
                })->addColumn('patient_phone', function ($provider_request) {

                    return $provider_request->patient->phone ?? '';
                })
                ->addColumn('patient_gov', function ($provider_request) {

                    return $provider_request->patient->governorate->name ?? '';
                })
                ->addColumn('patient_city', function ($provider_request) {

                    return $provider_request->patient->city->name ?? '';
                })->addColumn('patient_location', function ($provider_request) {

                    return $provider_request->patient->location ?? '';
                })->editColumn('status', function ($provider_request) {

                    if ($provider_request->status == 'pending') {
                        return '<span class="badge bg-warning" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Pending</span>';
                    } else if ($provider_request->status == 'active') {
                        return '<span class="badge bg-primary" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Active</span>';
                    } else if ($provider_request->status == 'complete') {
                        return '<span class="badge bg-success" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Complete</span>';
                    } else if ($provider_request->status == 'cancel') {
                        return '<span class="badge bg-danger"style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Cancel</span>';
                    }
                    return $provider_request->status;
                })
                //->addColumn('provider_name', function ($provider_request) {

                //     return $provider_request->provider->name ?? '';
                // })->addColumn('provider_branch_location', function ($provider_request) {

                //     return $provider_request->provider_branch->location ?? '';
                // })->addColumn('provider_branch_phone', function ($provider_request) {

                //     return $provider_request->provider_branch->phone ?? '';
                // })->addColumn('services', function ($provider_request) {
                //     $items = '';
                //     foreach ($provider_request->items as $item) {
                //         $items .= "<div>" . $item->service->name . "</div>";
                //     }
                //     return $items;
                // })->addColumn('total_price', function ($provider_request) {

                //     return $provider_request->total_price ?? '';
                // })
                // ->editColumn('specialization_id', function ($row) {
                //     return $row->specialization->name ?? '';
                // })
                // ->addColumn('doctor_times', function ($row) {
                //     $route = route('admin.doctor_times', $row->id);
                //     return "<a href='$route' class='btn btn-outline-primary'>" . helperTrans('admin.Doctor Time') . "</a>";
                // })
                // ->addColumn('doctor_contract', function ($row) {
                //     $latestContract = $row->contracts()->latest()->first();
                //     if ($latestContract) {
                //         $route = route('admin.doctor_contract_show', $latestContract->id);
                //         return "<a href='$route' class='btn btn-outline-success'>" . helperTrans('admin.Perview') . "</a>";
                //     }
                // })
                // ->setRowClass(function ($row) {
                //     $latestContract = $row->contracts()->latest()->first();
                //     if ($latestContract && strtotime($latestContract->contract_end_date) < time()) {
                //         return 'custom_warning_bg';
                //     }
                //     return '';
                // })
                // ->addColumn('update', function ($row) {
                //     $doctorUpdate = DoctorUpdate::where('doctor_id', $row->id)->latest()->first();
                //     if (isset($doctorUpdate)) {
                //         if ($doctorUpdate->status == 'pending') {
                //             $route = route('admin.update.doctor', $doctorUpdate->id);
                //             return '<a href="' . $route . '" class="btn btn-warning ">Show Update</a>';
                //         } elseif ($doctorUpdate->status == 'reject') {
                //             return '<span class="btn btn-danger">Reject</span>';
                //         } elseif ($doctorUpdate->status == 'approved') {
                //             return '<span class="btn btn-success">Approved</span>';
                //         }
                //     } else {
                //         return '<span class="btn btn-info">Not Needed</span>';
                //     }
                // })
                // ->editColumn('status', function ($row) {
                //     $active = '';
                //     $operation = '';
                //     $operation = '';

                //     if ($row->status == 1)
                //         $active = 'checked';

                //     return '<div class="form-check form-switch">
                //                <input ' . $operation . '  class="form-check-input activeBtn" data-id="' . $row->id . ' " type="checkbox" role="switch" id="flexSwitchCheckChecked" ' . $active . '  >
                //             </div>';
                // })
                // ->editColumn('image', function ($admin) {
                //     return '
                //       <a data-fancybox="" href="' . asset(get_file($admin->image)) . '">
                //         <img height="60px" src="' . asset(get_file($admin->image)) . '">
                //         </a>
                //     ';
                // })

                // ->editColumn('created_at', function ($admin) {
                //     return date('Y/m/d', strtotime($admin->created_at));
                // })
                ->escapeColumns([])
                ->make(true);
        }
        return view('Admin.CRUDS.package.Requests');

        // dd($package_requests);
        // return DataTables::of($package_requests)
        //     ->addColumn('action', function ($package_request) {

        //         $actions = "";

        //         if ($package_request->status == 'pending') {
        //             return '
        //                         <a href="' . route('admin.package_requests.change_status', ['id' => $package_request->id, 'status' => 'active']) . '"   class="btn rounded-pill btn-info w-25"  style="min-width: 70px;"
        //                                 data-id="' . $package_request->id . '"
        //                         <span class="svg-icon svg-icon-3">
        //                             <span class="svg-icon svg-icon-3">
        //                                 Active
        //                             </span>
        //                         </span>
        //                         </a>
        //                         <a href="' . route('admin.package_requests.change_status', ['id' => $package_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;" "
        //                         data-id="' . $package_request->id . '"
        //                 <span class="svg-icon svg-icon-3 bg-success">
        //                     <span class="svg-icon svg-icon-3 ">
        //                         Cancel
        //                     </span>
        //                 </span>
        //                 </a>
        //                        ';
        //         } else if ($package_request->status == 'active') {
        //             return  '
        //                 <a href="' . route('admin.package_requests.change_status', ['id' => $package_request->id, 'status' => 'complete']) . '"   class="btn rounded-pill btn-success w-25" style="min-width: 70px;"
        //                         data-id="' . $package_request->id . '"
        //                 <span class="svg-icon svg-icon-3 bg-success">
        //                     <span class="svg-icon svg-icon-3 ">
        //                         Complete
        //                     </span>
        //                 </span>
        //                 </a>
        //                 <a href="' . route('admin.package_requests.change_status', ['id' => $package_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;""
        //                         data-id="' . $package_request->id . '"
        //                 <span class="svg-icon svg-icon-3 bg-success">
        //                     <span class="svg-icon svg-icon-3 ">
        //                         Cancel
        //                     </span>
        //                 </span>
        //                 </a>
        //                ';
        //         }
        //     })
        //     // ->editColumn('name', function ($row) {
        //     //     if (is_json($row->name)) {
        //     //         $nameData = json_decode($row->name, true);
        //     //         $nameAr = $nameData['ar'] ?? 'N/A';
        //     //         $nameEn = $nameData['en'] ?? 'N/A';
        //     //         return '<span style="color:red;">' . htmlspecialchars($nameAr) . '</span> / <span style="color:blue;">' . htmlspecialchars($nameEn) . '</span>';
        //     //     }
        //     //     if (is_string($row->name)) {
        //     //         return htmlspecialchars($row->name);
        //     //     }
        //     // })
        //     // ->editColumn('created_at', function ($package_request) {

        //     //     return "<div>" . date('Y/m/d', strtotime($package_request->created_at)) . "</div>"
        //     //         . "<div>" . date('h:i A', strtotime($package_request->created_at)) . "</div>";
        //     // })->addColumn('patient_name', function ($package_request) {

        //     //     return $package_request->patient->name ?? '';
        //     // })->addColumn('patient_phone', function ($package_request) {

        //     //     return $package_request->patient->phone ?? '';
        //     // })
        //     // // ->addColumn('patient_gov', function ($package_request) {

        //     // //     return $package_request->patient->governorate->name ?? '';
        //     // // })
        //     // ->addColumn('patient_city', function ($package_request) {

        //     //     return $package_request->patient->city->name ?? '';
        //     // })->addColumn('patient_location', function ($package_request) {

        //     //     return $package_request->patient->location ?? '';
        //     // })->editColumn('status', function ($package_request) {

        //     //     if ($package_request->status == 'pending') {
        //     //         return '<span class="badge bg-warning" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Pending</span>';
        //     //     } else if ($package_request->status == 'active') {
        //     //         return '<span class="badge bg-primary" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Active</span>';
        //     //     } else if ($package_request->status == 'complete') {
        //     //         return '<span class="badge bg-success" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Complete</span>';
        //     //     } else if ($package_request->status == 'cancel') {
        //     //         return '<span class="badge bg-danger"style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Cancel</span>';
        //     //     }
        //     //     return $package_request->status;
        //     // })

        //     // ->addColumn('services', function ($package_request) {
        //     //     $items = '';
        //     //     foreach ($package_request->items as $item) {
        //     //         $items .= "<div>" . $item->service->name . "</div>";
        //     //     }
        //     //     return $items;
        //     // })

        //     ->escapeColumns([])
        //     ->make(true);
        // // }
        // return view('Admin.CRUDS.package.Requests');
    }

    public function change_status(Request $request)
    {
        // dd($request->all());
        $package_request = ModelsPackageRequest::find($request->id);
        $package_request->status = $request->status;
        $package_request->save();
        return redirect()->back();
    }
}
