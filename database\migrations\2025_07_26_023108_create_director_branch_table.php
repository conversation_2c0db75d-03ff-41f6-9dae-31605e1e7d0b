<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('director_branch', function (Blueprint $table) {
            $table->id();
            $table->foreignId('director_id')->constrained('directors')->cascadeOnDelete();
            $table->foreignId('provider_branch_id')->constrained('provider_branches')->cascadeOnDelete();
            $table->foreignId('imported_file_id')->nullable()->constrained('imported_files')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('director_branch');
    }
};
