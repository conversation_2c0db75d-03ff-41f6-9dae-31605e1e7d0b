<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Validation\Rule;

class PatientRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'nickname' => ['nullable', 'string', 'max:255'],
            'email' => ['nullable', 'email', 'max:255'],
            'phone' => ['required', 'string', 'max:20'],
            'additional_phone' => ['nullable', 'string', 'max:20'],
            'password' => ['required', 'string', 'min:6'],
            'gender' => ['required', 'in:male,female'],
            'status' => ['boolean'],
            'postcode' => ['nullable', 'string', 'max:20'],
            'refer_code' => ['nullable', 'string', 'max:50'],
            'address' => ['nullable', 'string'],
            'location' => ['nullable', 'string', 'max:300'],
            'date_of_birth' => ['nullable', 'date'],
            'national_id' => ['nullable', 'string', 'max:50'],
            'external_id' => ['nullable', 'string', 'max:50'],
            'card_id' => ['nullable', 'string', 'max:50'],

            // Foreign keys
            'nationality_id' => ['nullable', 'exists:nationalities,id'],
            'country_id' => ['nullable', 'exists:countries,id'],
            'governorate_id' => ['nullable', 'exists:governorates,id'],
            'city_id' => ['nullable', 'exists:cities,id'],
            'partner_id' => ['nullable', 'exists:partners,id'],
            'class_id' => ['nullable', 'exists:classes,id'],
        ];

        // For update requests, make password optional and handle unique email
        if (request()->isMethod('PUT') || request()->isMethod('PATCH')) {
            $rules['password'] = ['nullable', 'string', 'min:6'];

            if (request()->input('email')) {
                $rules['email'] = [
                    'nullable',
                    'email',
                    'max:255',
                    Rule::unique('patients', 'email')->ignore(request()->route('patient'))
                ];
            }

            // Handle unique phone validation for updates
            if (request()->input('phone')) {
                $rules['phone'] = [
                    'required',
                    'string',
                    'max:20',
                    Rule::unique('patients', 'phone')->ignore(request()->route('patient'))
                ];
            }

            // Handle unique additional_phone validation for updates
            if (request()->input('additional_phone')) {
                $rules['additional_phone'] = [
                    'nullable',
                    'string',
                    'max:20',
                    Rule::unique('patients', 'additional_phone')->ignore(request()->route('patient'))
                ];
            }
        } else {
            // For create requests, handle unique email and phone
            if (request()->input('email')) {
                $rules['email'] = ['nullable', 'email', 'max:255', 'unique:patients,email'];
            }

            if (request()->input('phone')) {
                $rules['phone'] = ['required', 'string', 'max:20', 'unique:patients,phone'];
            }

            if (request()->input('additional_phone')) {
                $rules['additional_phone'] = ['nullable', 'string', 'max:20', 'unique:patients,additional_phone'];
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => 'Patient name is required',
            'phone.required' => 'Phone number is required',
            'password.required' => 'Password is required',
            'password.min' => 'Password must be at least 6 characters',
            'gender.required' => 'Gender is required',
            'gender.in' => 'Gender must be either male or female',
            'email.email' => 'Please provide a valid email address',
            'email.unique' => 'This email is already registered',
            'nationality_id.exists' => 'Selected nationality does not exist',
            'country_id.exists' => 'Selected country does not exist',
            'governorate_id.exists' => 'Selected governorate does not exist',
            'city_id.exists' => 'Selected city does not exist',
            'partner_id.exists' => 'Selected partner does not exist',
            'class_id.exists' => 'Selected class does not exist',
        ];
    }
}
