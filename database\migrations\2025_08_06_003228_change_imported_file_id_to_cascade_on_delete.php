<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('doctors', function (Blueprint $table) {
            $table->dropForeign(['imported_file_id']);

            // Then, add the new foreign key with cascade on delete
            $table->foreign('imported_file_id')
                ->references('id')
                ->on('imported_files')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('cascade_on_delete', function (Blueprint $table) {
            //
        });
    }
};
