<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\ProviderCategoryResource;
use App\Http\Traits\Api_Trait;
use App\Models\ProviderCategory;
use Illuminate\Http\Request;

class ProviderCatgoriesController extends Controller
{
    use Api_Trait;
    public function index()
    {
        $provider_categories = ProviderCategory::select('id','name')->get();
        return $this->returnData(
            ProviderCategoryResource::collection($provider_categories),
            [helperTrans('api.Provider Categories Data')],
            200
        );
    }

    public function store(Request $request)
    {
        $provider_category = ProviderCategory::create([
            'name' => $request->name,
        ]);

        return $this->returnData(
            new ProviderCategoryResource($provider_category),
            [helperTrans('api.provider category created')],
            201
        );
    }

    public function show(ProviderCategory $provider_category)
    {
        return $this->returnData(
            new ProviderCategoryResource($provider_category),
            [helperTrans('api.provider category show')],
            200
        );
    }

    public function update(Request $request, ProviderCategory $provider_category)
    {
        $provider_category->update([
            'name' => $request->name,
        ]);

        return $this->returnData(
            new ProviderCategoryResource($provider_category),
            [helperTrans('api.provider category updated')],
            200
        );
    }

    public function destroy(ProviderCategory $provider_category)
    {
        $provider_category->delete();

        return response()->json([
            'status' => true,
            'message' => [helperTrans('api.provider category deleted')]
        ]);
    }
}