<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractOtherPlace extends Model
{
    use HasFactory;
    protected $table = 'contract_other_places';
    protected $fillable = [
        'contract_id',
        'other_place_id',
    ];
    protected $guarded = [];
    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }
    public function otherPlace()
    {
        return $this->belongsTo(OtherPlace::class);
    }
}
