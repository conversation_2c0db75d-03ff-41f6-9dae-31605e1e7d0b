<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Validation\Rule;

class PackageRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'description' => ['nullable', 'string', 'max:1000'],
            'price' => ['required', 'numeric', 'min:0', 'max:999999.99'],
            'image' => ['nullable', 'string', 'max:255'],
            'max_relative' => ['nullable', 'integer', 'min:0', 'max:100'],
            'show_in_app' => ['nullable', 'boolean'],
        ];

        // Handle translatable name validation for create vs update
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            // For update requests
            $packageId = $this->route('package');
            
            // Handle both translatable and simple name validation
            if (is_array($this->input('name'))) {
                // Translatable names
                foreach (languages() as $language) {
                    $rules["name.{$language->abbreviation}"] = [
                        'required',
                        'string',
                        'max:500',
                        Rule::unique('packages', "name->{$language->abbreviation}")->ignore($packageId)
                    ];
                }
            } else {
                // Simple name
                $rules['name'] = [
                    'required',
                    'string',
                    'max:500',
                    Rule::unique('packages', 'name')->ignore($packageId)
                ];
            }
        } else {
            // For create requests
            if (is_array($this->input('name'))) {
                // Translatable names
                foreach (languages() as $language) {
                    $rules["name.{$language->abbreviation}"] = [
                        'required',
                        'string',
                        'max:500',
                        Rule::unique('packages', "name->{$language->abbreviation}")
                    ];
                }
            } else {
                // Simple name
                $rules['name'] = [
                    'required',
                    'string',
                    'max:500',
                    'unique:packages,name'
                ];
            }
        }

        return $rules;
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        $messages = [
            'name.required' => 'Package name is required',
            'name.unique' => 'This package name already exists',
            'name.max' => 'Package name cannot exceed 500 characters',
            'description.max' => 'Description cannot exceed 1000 characters',
            'price.required' => 'Package price is required',
            'price.numeric' => 'Package price must be a valid number',
            'price.min' => 'Package price cannot be negative',
            'price.max' => 'Package price cannot exceed 999,999.99',
            'image.max' => 'Image path cannot exceed 255 characters',
            'max_relative.integer' => 'Maximum relatives must be a valid integer',
            'max_relative.min' => 'Maximum relatives cannot be negative',
            'max_relative.max' => 'Maximum relatives cannot exceed 100',
            'show_in_app.boolean' => 'Show in app must be true or false',
        ];

        // Add translatable name messages
        foreach (languages() as $language) {
            $messages["name.{$language->abbreviation}.required"] = "Package name in {$language->name} is required";
            $messages["name.{$language->abbreviation}.unique"] = "This package name in {$language->name} already exists";
            $messages["name.{$language->abbreviation}.max"] = "Package name in {$language->name} cannot exceed 500 characters";
        }

        return $messages;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    public function attributes()
    {
        return [
            'name' => 'package name',
            'description' => 'description',
            'price' => 'price',
            'image' => 'image',
            'max_relative' => 'maximum relatives',
            'show_in_app' => 'show in app',
        ];
    }
}
