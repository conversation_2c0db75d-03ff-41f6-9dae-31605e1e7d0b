<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('relatives', function (Blueprint $table) {
            $table->dropColumn('first_name');
            $table->dropColumn('last_name');
            $table->dropColumn('id_number');
            $table->string('name')->nullable()->after('id');
            $table->foreignId('relationship_id')->nullable()->after('name')->constrained('relationships')->nullOnDelete();
            $table->string('national_id')->nullable()->after('name');
            $table->string('external_id')->nullable()->after('name');
            $table->enum('status', ['active', 'inactive'])->default('active')->after('name');
            $table->string('phone')->nullable()->after('name');
            $table->foreignId('area_id')->nullable()->after('city_id')->constrained('areas')->nullOnDelete();
            $table->boolean('join_subscribe')->default(false)->after('gender');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('relatives', function (Blueprint $table) {
            $table->dropColumn('name');
            $table->dropColumn('relationship_id');
            $table->dropColumn('national_id');
            $table->dropColumn('external_id');
            $table->dropColumn('status');
            $table->dropColumn('phone');
            $table->dropColumn('area_id');
            $table->dropColumn('join_subscribe');
        });
    }
};
