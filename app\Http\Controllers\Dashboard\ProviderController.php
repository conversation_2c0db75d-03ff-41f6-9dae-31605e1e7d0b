<?php

namespace App\Http\Controllers\Dashboard;

use App\Exceptions\DoctoriaException;
use App\Exports\ProvidersExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\ProviderRequest as DashboardProviderRequest;
use App\Http\Requests\FileRequest;
use App\Http\Resources\Dashboard\ProviderResource;
use App\Http\Traits\Api_Trait;
use App\Http\Traits\Upload_Files;
use App\Imports\Dashboard\ProviderImport;
use App\Imports\Dashboard\AdvancedProviderImport;
use App\Models\Assistant;
use App\Models\AssistantBranch;
use App\Models\Director;
use App\Models\DirectorBranch;
use App\Models\Doctor;
use App\Models\ImportedFile;
use App\Models\Provider;
use App\Models\ProviderBranch;
use App\Models\ProviderBranchDoctor;
use App\Models\ProviderService;
use App\Models\ProviderTime;
use App\Services\Providers\ProviderStoreService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;

class ProviderController extends Controller
{
    use Api_Trait, Upload_Files;

    public function index(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'provider_category_id' => 'required|exists:provider_categories,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $providers = Provider::where('provider_category_id', $request->provider_category_id);

        if ($request->governorate_id) {
            $providers->whereHas('branches', function ($query) use ($request) {
                $query->where('governorate_id', $request->governorate_id);
            });
        }
        if ($request->city_id) {
            $providers->whereHas('branches', function ($query) use ($request) {
                $query->where('city_id', $request->city_id);
            });
        }

        $providers = $providers->paginate($request->per_page ?? 10);

        return $this->paginatedResponse(ProviderResource::collection($providers), [helperTrans('api.Providers Data')], 200);
    }


    public function store(DashboardProviderRequest $request)
    {
        $data = collect($request->validated())->except([
            'site_category',
            'times',
            'main_branch_name',
            'main_branch_status',
            'assistant_name',
            'assistant_phone',
            'assistant_email',
            'assistant_password',
            'inpatient_clinics_manager_name',
            'inpatient_clinics_manager_phone',
            'inpatient_clinics_manager_email',
            'inpatient_clinics_manager_password',
            'outpatient_clinics_manager_name',
            'outpatient_clinics_manager_phone',
            'outpatient_clinics_manager_email',
            'outpatient_clinics_manager_password',
            'pharmacy_director_name',
            'pharmacy_director_phone',
            'pharmacy_director_email',
            'pharmacy_director_password',
            'radiology_director_name',
            'radiology_director_phone',
            'radiology_director_email',
            'radiology_director_password',
            'laboratory_director_name',
            'laboratory_director_phone',
            'laboratory_director_email',
            'laboratory_director_password',
            'specialization_id',
            'sub_specialization_id',
            'experience',
            'experience_years',
            'attachment_ids'
        ])->toArray();

        $provider = (new ProviderStoreService)->store($data, $request);

        return $this->returnData(ProviderResource::make($provider), [helperTrans('api.Provider Data')], 200);
    }

    public function update(DashboardProviderRequest $request, Provider $provider)
    {
        try {
            DB::beginTransaction();

            $provider_data = collect($request->validated())->except([
                'site_category',
                'times',
                'main_branch_name',
                'main_branch_status',
                'assistant_name',
                'assistant_phone',
                'assistant_email',
                'assistant_password',
            ])->toArray();

            $provider_data['location'] = json_encode($request->location);
            $provider_data['language'] = json_encode($request->language);

            if ($request->filled('password')) {
                $provider_data['password'] = Hash::make($request->password);
            }

            if ($request->hasFile('image')) {
                $provider_data["image"] = $this->uploadFiles('providers', $request->file('image'), $provider->image);
            }

            $provider->update($provider_data);

            $provider->times()->delete();

            foreach ($request->times as $time) {
                ProviderTime::create([
                    'provider_id' => $provider->id,
                    'provider_type' => 'provider',
                    'day_id' => $time['day_id'],
                    'from_time' => $time['from_time'],
                    'to_time' => $time['to_time'],
                ]);
            }

            if ($request->site_category == 'branch') {
                $main_branch = $provider->branches()->where('degree', 'main')->first();

                if ($main_branch) {
                    $main_branch->update([
                        'name' => $request->main_branch_name,
                        'phone' => $request->phone,
                        'country_id' => $request->country_id,
                        'governorate_id' => $request->governorate_id,
                        'city_id' => $request->city_id,
                        'location' => json_encode($request->location),
                        'longitude' => $request->longitude,
                        'latitude' => $request->latitude,
                        'status' => $request->main_branch_status,
                    ]);
                }
                $main_branch->times()->delete();
                foreach ($request->times as $time) {
                    ProviderTime::create([
                        'provider_id' => $main_branch->id,
                        'provider_type' => 'provider_branch',
                        'day_id' => $time['day_id'],
                        'from_time' => $time['from_time'],
                        'to_time' => $time['to_time'],
                    ]);
                }
                if ($request->assistant_name) {
                    $main_branch->assistant()->first()->update([
                        'name' => $request->assistant_name,
                        'phone' => $request->assistant_phone,
                        'email' => $request->assistant_email,
                        'password' => $request->assistant_password,
                    ]);
                }
            }

            DB::commit();

            return $this->returnData(ProviderResource::make($provider), [helperTrans('api.Provider Data')], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DoctoriaException($e->getMessage());
        }
    }

    public function destroy(Provider $provider)
    {
        try {
            DB::beginTransaction();

            ProviderService::where('provider_id', $provider->id)->delete();

            $branches = ProviderBranch::where('provider_id', $provider->id)->get();
            foreach ($branches as $branch) {
                DirectorBranch::where('provider_branch_id', $branch->id)->delete();
                $branch->delete();
            }
            $provider->times()->delete();

            $provider->delete();

            DB::commit();

            return $this->returnData(ProviderResource::make($provider), [helperTrans('api.Provider Data')], 200);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new DoctoriaException($e->getMessage());
        }
    }
    public function importProviders(FileRequest $request)
    {
        try {
            // Save file to imported_files table before import
            $importedFile = ImportedFile::saveFile(
                $request->file('file'),
                auth('sanctum')->id()
            );
            $import = new ProviderImport($importedFile->id);
            Excel::import($import, $importedFile->path);

            $response = [
                'success_count' => $import->getSuccessCount(),
                'error_count' => $import->getErrorCount(),
                'errors' => $import->getErrors(),
                'imported_file_id' => $importedFile->id,
                'filename' => $importedFile->file_name
            ];

            if ($import->getErrorCount() > 0) {
                if ($import->getSuccessCount() === 0) {
                    $importedFile->delete();
                    unset($response['imported_file_id']);
                }
                return $this->returnData([
                    $response,
                    helperTrans('api.Providers Template Has Errors'),
                    "errors Count: {$import->getErrorCount()}"
                ], 422);
            }
            return $this->returnData($response, [
                helperTrans('api.Providers imported successfully'),
                "Successfully imported {$import->getSuccessCount()} providers"
            ], 200);
        } catch (\Exception $e) {
            return $this->returnErrorImport([
                'message' => 'Import failed: ' . $e->getMessage()
            ], 500);
        }
    }

    /**
     * Advanced import providers with branches, doctors, and relationships
     */
    public function advancedImport(FileRequest $request)
    {
        try {
            $request->validate([
                'file' => 'required|mimes:xlsx,xls,csv|max:10240'
            ]);
            // Save file to imported_files table before import
            $importedFile = ImportedFile::saveFile(
                $request->file('file'),
                auth('sanctum')->id()
            );

            $import = new AdvancedProviderImport($importedFile->id);
            Excel::import($import, $importedFile->path);

            $response = [
                'success_count' => $import->getSuccessCount(),
                'error_count' => $import->getErrorCount(),
                'errors' => $import->getErrors(),
                'imported_file_id' => $importedFile->id,
                'filename' => $importedFile->file_name
            ];

            if ($import->getErrorCount() > 0) {
                return $this->returnData([
                    $response,
                    helperTrans('api.Advanced Provider Template Has Errors'),
                    "errors Count: {$import->getErrorCount()}"
                ], 422);
            }

            return $this->returnData($response, [
                helperTrans('api.Advanced Provider data imported successfully'),
                "Successfully imported {$import->getSuccessCount()} provider records with branches and doctors"
            ], 200);
        } catch (\Exception $e) {
            return $this->returnErrorImport([
                'message' => 'Advanced import failed: ' . $e->getMessage()
            ], 500);
        }
    }

    public function export(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'provider_category_id' => 'required|exists:provider_categories,id',
            ]
        );

        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $providers = Provider::where('provider_category_id', $request->provider_category_id);

        if ($request->governorate_id) {
            $providers->whereHas('branches', function ($query) use ($request) {
                $query->where('governorate_id', $request->governorate_id);
            });
        }

        if ($request->city_id) {
            $providers->whereHas('branches', function ($query) use ($request) {
                $query->where('city_id', $request->city_id);
            });
        }

        $allProviders = $providers->get();

        return Excel::download(new ProvidersExport($allProviders), 'providers.xlsx');
    }
}
