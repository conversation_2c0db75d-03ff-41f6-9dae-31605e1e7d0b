<?php

namespace App\Http\Controllers\Api\V2;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Traits\Api_Trait;
use Illuminate\Support\Facades\Validator;
use App\Models\Provider;
use App\Http\Resources\ProviderResource;
use App\Models\Service;

class ProviderAndServicesController extends Controller
{
    use Api_Trait;

    public function providerData(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'provider_category_id' => 'required|exists:provider_categories,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }
        $query = $request->input('query');
        $providers = Provider::select('id', 'name')->where('provider_category_id', $request->provider_category_id)
            ->whereHas('branches', function ($query) use ($request) {
                if (!is_null($request->governorate_id)) {
                    $query->where('governorate_id', $request->governorate_id);
                }
                if (!is_null($request->city_id)) {
                    $query->where('city_id', $request->city_id);
                }
            })
            ->where(fn($q) => $q->where('name->ar', 'LIKE', "%{$query}%")
                ->orWhere('name->en', 'LIKE', "%{$query}%"))
            ->get()->map(function ($provider) {
                return [
                    'id' => $provider->id,
                    'name' => $provider->name,
                ];
            });

        return $this->returnData($providers, [helperTrans('api.Providers Data')], 200);
    }

    public function serviceData(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'service_category_id' => 'required|exists:service_categories,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }
        $query = $request->input('query');
        $services = Service::select('id', 'name', 'service_category_id')
            ->where('service_category_id', $request->service_category_id)
            ->where(fn($q) => $q->where('name->ar', 'LIKE', "%{$query}%")
                ->orWhere('name->en', 'LIKE', "%{$query}%"))
            ->get()
            ->map(function ($service) {
                return [
                    'id' => $service->id,
                    'name' => $service->name,
                    'service_category_id' => $service->service_category_id,
                ];
            });

        return $this->returnData($services, [helperTrans('api.Services Data')], 200);
    }
}
