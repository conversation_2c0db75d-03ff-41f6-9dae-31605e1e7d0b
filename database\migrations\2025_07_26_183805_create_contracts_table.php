<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contracts', function (Blueprint $table) {
            $table->id();
            $table->foreignId('admin_id')->constrained()->onDelete('cascade')->onUpdate('cascade');
            $table->foreignId('provider_id')->constrained()->onDelete('cascade')->onUpdate('cascade');
            $table->string('official_full_name');
            $table->string('commercial_registration_number')->nullable();
            $table->string('national_id')->nullable();
            $table->string('tax_card_number')->nullable();
            $table->date('tax_card_due_date')->nullable();
            $table->string('private_phone_number')->nullable();
            $table->string('private_email')->nullable();
            $table->string('representative_full_name')->nullable();
            $table->string('facebook_link')->nullable();
            $table->string('instagram_link')->nullable();
            $table->date('contract_start_date');
            $table->date('contract_end_date');
            $table->foreignId('payment_method_id')->nullable()->constrained()->onDelete('set null');
            $table->integer('claims_due_date')->nullable();
            $table->decimal('admin_fees')->nullable();
            $table->text('notes')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contracts');
    }
};
