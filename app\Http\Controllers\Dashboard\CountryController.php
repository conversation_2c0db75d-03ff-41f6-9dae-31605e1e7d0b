<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\CountryRequest;
use App\Http\Resources\CountryResource;
use App\Http\Traits\Api_Trait;
use App\Models\Country;

class CountryController extends Controller
{

    use Api_Trait;
    public function index()
    {
        $countries = Country::get();
        return $this->returnData(
            CountryResource::collection($countries),
            [helperTrans('api.countries data')],
            200
        );
    }

    public function store(CountryRequest $request)
    {
        $country = Country::create([
            'name' => $request->name
        ]);

        return $this->returnData(
            new CountryResource($country),
            [helperTrans('api.country created')],
            201
        );
    }

    public function show(Country $country)
    {
        return $this->returnData(
            new CountryResource($country),
            [helperTrans('api.country show')],
            200
        );
    }

    public function update(CountryRequest $request, Country $country)
    {

        if ($request->has('name')) {
            $country->setTranslations('name', $request->name);
            $country->save();
        }

        return $this->returnData(
            new CountryResource($country),
            [helperTrans('api.country updated')],
            200
        );
    }

    public function destroy(Country $country)
    {
        $country->delete();

        return response()->json([
            'status' => true,
            'message' => [helperTrans('api.country deleted')]
        ]);
    }
}
