<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\CityRequest;
use App\Http\Resources\CityResource;
use App\Http\Traits\Api_Trait;
use App\Models\City;
use Illuminate\Http\Request;

class CityController extends Controller
{

    use Api_Trait;
    public function index(Request $request)
    {

        $cities = City::with('governorate')->when($request->governorate_id, function ($query) use ($request) {
            $query->where('governorate_id', $request->governorate_id);
        })->get();
        return $this->returnData(
            CityResource::collection($cities),
            [helperTrans('api.cities data')],
            200
        );
    }

    public function store(CityRequest $request)
    {
        $city = City::create([
            'name' => $request->name,
            'governorate_id' => $request->governorate_id,
            'nationality_id' => $request->nationality_id,
        ]);

        return $this->returnData(
            new CityResource($city),
            [helperTrans('api.city created')],
            201
        );
    }

    public function show(City $city)
    {
        return $this->returnData(
            new CityResource($city),
            [helperTrans('api.city show')],
            200
        );
    }

    public function update(CityRequest $request, City $city)
    {

        $city->update($request->only(['governorate_id', 'nationality_id']));

        if ($request->has('name')) {
            $city->setTranslations('name', $request->name);
            $city->save();
        }

        return $this->returnData(
            new CityResource($city),
            [helperTrans('api.city updated')],
            200
        );
    }

    public function destroy(City $city)
    {
        $city->delete();

        return response()->json([
            'status' => true,
            'message' => [helperTrans('api.city deleted')]
        ]);
    }
}
