<?php

namespace App\Http\Requests\Api\Auth;

use App\Enums\Auth\Otp\OtpType;
use Illuminate\Foundation\Http\FormRequest;

class ForgetPasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'phone' => 'required|string',
            'driver' => 'required|in:sms,whatsapp',
            // 'user_type' => 'required|in:patient,provider',
        ];
    }
}
