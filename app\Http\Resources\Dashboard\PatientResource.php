<?php

namespace App\Http\Resources\Dashboard;

use App\Http\Resources\Dashboard\NationalityResource;
use Illuminate\Http\Resources\Json\JsonResource;

class PatientResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'card_id' => $this->card_id,
            'name' => $this->name,
            'nickname' => $this->nickname,
            'email' => $this->email,
            'phone' => $this->phone,
            'additional_phone' => $this->additional_phone,
            'gender' => $this->gender,
            'status' => $this->status,
            'postcode' => $this->postcode,
            'refer_code' => $this->refer_code,
            'address' => $this->address,
            'location' => $this->location,
            'date_of_birth' => $this->date_of_birth,
            'national_id' => $this->national_id,
            'external_id' => $this->external_id,
            'image' => $this->image ? get_file($this->image) : null,

            // Relationships
            'nationality' => $this->nationality ? NationalityResource::make($this->nationality) : null,

            'country' => $this->country ? CountryResource::make($this->country) : null,

            'governorate' => $this->governorate ? GovernorateResource::make($this->governorate) : null,

            'city' => $this->city ? CityResource::make($this->city) : null,
            'partner' => $this->partner ? PartnerResource::make($this->partner) : null,
            'class' => $this->class ? ClassResource::make($this->class) : null,

            // Additional computed fields
            'relatives_count' => $this->relatives->count() ?? 0,
            'has_additional_phone' => !empty($this->additional_phone),
            'phone_count' => collect([$this->phone, $this->additional_phone])->filter()->count(),
            'all_phones' => collect([$this->phone, $this->additional_phone])->filter()->values(),

            // Timestamps
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
