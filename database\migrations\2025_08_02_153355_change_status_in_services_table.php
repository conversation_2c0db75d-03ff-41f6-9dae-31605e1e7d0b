<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Step 1: Temporarily change to VARCHAR to allow updating values
            DB::statement("ALTER TABLE services MODIFY status VARCHAR(20)");

            // Step 2: Update the values
            DB::table('services')->where('status', 'active')->update(['status' => 'available']);
            DB::table('services')->where('status', 'inactive')->update(['status' => 'unavailable']);

            // Step 3: Change back to ENUM with new values
            DB::statement("ALTER TABLE services MODIFY status ENUM('available', 'unavailable') DEFAULT 'available'");
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('services', function (Blueprint $table) {
            // Step 1: Change to VARCHAR
            DB::statement("ALTER TABLE services MODIFY status VARCHAR(20)");

            // Step 2: Revert values
            DB::table('services')->where('status', 'available')->update(['status' => 'active']);
            DB::table('services')->where('status', 'unavailable')->update(['status' => 'inactive']);

            // Step 3: Revert ENUM
            DB::statement("ALTER TABLE services MODIFY status ENUM('active', 'inactive') DEFAULT 'active'");
        });
    }
};
