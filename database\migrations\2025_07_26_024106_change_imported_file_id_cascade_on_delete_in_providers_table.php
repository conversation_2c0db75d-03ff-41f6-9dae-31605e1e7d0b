<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            $table->dropForeign(['imported_file_id']);

            // Then, add the new foreign key with cascade on delete
            $table->foreign('imported_file_id')
                ->references('id')
                ->on('imported_files')
                ->onDelete('cascade');
        });
        Schema::table('provider_branches', function (Blueprint $table) {
            $table->dropForeign(['imported_file_id']);

            // Then, add the new foreign key with cascade on delete
            $table->foreign('imported_file_id')
                ->references('id')
                ->on('imported_files')
                ->onDelete('cascade');
        });
        Schema::table('director_branch', function (Blueprint $table) {
            $table->dropForeign(['imported_file_id']);

            // Then, add the new foreign key with cascade on delete
            $table->foreign('imported_file_id')
                ->references('id')
                ->on('imported_files')
                ->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('providers', function (Blueprint $table) {
            $table->dropForeign(['imported_file_id']);

            // Then, add the new foreign key with set null on delete
            $table->foreign('imported_file_id')
                ->references('id')
                ->on('imported_files')
                ->onDelete('set null');
        });
        Schema::table('provider_branches', function (Blueprint $table) {
            $table->dropForeign(['imported_file_id']);

            // Then, add the new foreign key with set null on delete
            $table->foreign('imported_file_id')
                ->references('id')
                ->on('imported_files')
                ->onDelete('set null');
        });
    }
};
