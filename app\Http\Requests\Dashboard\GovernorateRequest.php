<?php

namespace App\Http\Requests\Dashboard;

use App\Exceptions\DoctoriaException;
use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GovernorateRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function store()
    {
        $rules = [
            'nationality_id' => 'required|exists:nationalities,id',
        ];


        foreach (languages() as $language) {
            $rules["name.$language->abbreviation"] = [
                'required',
                Rule::unique('governorates', "name->{$language->abbreviation}")
            ];
        }
        return $rules;
    }
    public function update()
    {
        $rules = [
            'nationality_id' => 'required|exists:nationalities,id',
        ];

        foreach (languages() as $language) {
            $rules["name.$language->abbreviation"] = [
                'required',
                Rule::unique('governorates', "name->{$language->abbreviation}")
                    ->ignore($this->id)
            ];;
        }
        return $rules;
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
