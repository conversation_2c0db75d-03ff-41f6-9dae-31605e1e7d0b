<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\InsuranceCompanyResource;
use App\Http\Traits\Api_Trait;
use App\Models\InsuranceCompany;
use Illuminate\Http\Request;

class InsuranceCompanyController extends Controller
{
    use Api_Trait;
    public function index()
    {
        $campanies = InsuranceCompany::get();
        return $this->returnData(InsuranceCompanyResource::collection($campanies), [helperTrans('api.Insurace campanies data')], 200);
    }
    public function store(Request $request)
    {
        $campany = InsuranceCompany::create($request->all());
        return $this->returnData(new InsuranceCompanyResource($campany), [helperTrans('api.Insurace campany created')], 201);
    }
    public function show(InsuranceCompany $insuranceCompany)
    {
        return $this->returnData(new InsuranceCompanyResource($insuranceCompany), [helperTrans('api.Insurace campany data')], 200);
    }
    public function update(Request $request, InsuranceCompany $insuranceCompany)
    {
        $insuranceCompany->update($request->all());
        return $this->returnData(new InsuranceCompanyResource($insuranceCompany), [helperTrans('api.Insurace campany updated')], 200);
    }
    public function destroy(InsuranceCompany $insuranceCompany)
    {
        $insuranceCompany->delete();
        return $this->returnData(new InsuranceCompanyResource($insuranceCompany), [helperTrans('api.Insurace campany deleted')], 200);
    }
}
