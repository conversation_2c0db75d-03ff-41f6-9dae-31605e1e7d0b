<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('directors', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('phone')->nullable();
            $table->string('email')->nullable();
            $table->string('password')->nullable();
            $table->enum('role_type', [
                'assistant',
                'outpatient_clinics_manager',
                'inpatient_clinics_manager',
                'pharmacy_director',
                'radiology_director',
                'laboratory_director'
            ])->default('assistant');
            $table->unsignedBigInteger('imported_file_id')->nullable();
            $table->foreign('imported_file_id')->references('id')->on('imported_files')->cascadeOnDelete();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('directors');
    }
};
