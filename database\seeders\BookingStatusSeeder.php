<?php

namespace Database\Seeders;

use App\Models\StatusBooking;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class BookingStatusSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $bookingStatuses = [
            [
                'id' => 1,
                'name' => 'Pending',
                'booking_type_id' => 2,
                'notes' => 'Pending for provider means that provider accept request and waiting for payment',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 2,
                'name' => 'Upcoming',
                'booking_type_id' => null,
                'notes' => 'Upcoming Booking For 2 options means that booking time still not come',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 3,
                'name' => 'Active',
                'booking_type_id' => 1,
                'notes' => 'Active Booking when general doctor accept request',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 4,
                'name' => 'Complete',
                'booking_type_id' => null,
                'notes' => 'Complete Booking',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 5,
                'name' => 'Cancel',
                'booking_type_id' => null,
                'notes' => 'Cancelled from Patient',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 6,
                'name' => 'Rejected',
                'booking_type_id' => null,
                'notes' => 'Rejected',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 7,
                'name' => 'Reappoint',
                'booking_type_id' => 1,
                'notes' => 'Reappoint',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 8,
                'name' => 'Follow Up',
                'booking_type_id' => 1,
                'notes' => 'Follow Up For patient in another time',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 9,
                'name' => 'Transfer',
                'booking_type_id' => 1,
                'notes' => 'Transfer To Another Doctor',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
            [
                'id' => 10,
                'name' => 'Referral',
                'booking_type_id' => 1,
                'notes' => 'Referral To Another Provider',
                'color' => '#000000',
                'created_at' => now(),
                'updated_at' => now(),
            ],
        ];

        Schema::disableForeignKeyConstraints();
        StatusBooking::truncate();
        Schema::enableForeignKeyConstraints();

        StatusBooking::insert($bookingStatuses);
    }
}
