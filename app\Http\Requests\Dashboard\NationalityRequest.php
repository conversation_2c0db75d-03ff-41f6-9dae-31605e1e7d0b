<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Foundation\Http\FormRequest;

class NationalityRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function store()
    {
        return [
            'name' => ['required', 'array'],
            'nickname' => ['nullable', 'array'],
            'country_name' => ['required', 'array'],
            'phone_code' => ['nullable', 'string'],
            'country_code' => ['nullable', 'string'],
        ];
    }
    public function update()
    {
        return [
            'name' => ['sometimes', 'required', 'array'],
            'nickname' => ['nullable', 'array'],
            'country_name' => ['sometimes', 'required', 'array'],
            'phone_code' => ['nullable', 'string'],
            'country_code' => ['nullable', 'string'],
        ];
    }
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT')) {
            return $this->update();
        }
    }
}
