<?php

namespace App\Http\Controllers\Admin;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Models\Doctor;
use App\Models\DoctorUpdate;
use App\Models\PatientSubscribe;
use App\Models\Provider;
use App\Models\ProviderBranch;
use App\Models\ProviderCategory;
use App\Models\ProviderRequest;
use App\Models\ProviderRequestItem;
use App\Models\ProviderService;
use App\Models\Service;
use App\Models\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Ya<PERSON>ra\DataTables\DataTables;

class ProviderRequestController extends Controller
{
    public function newindex(Request $request)
    {
        if ($request->ajax()) {
            $provider_requests = ProviderRequest::with(
                'provider:id,name,provider_category_id',
                'provider_branch:id,location,phone',
                'patient:id,name,phone,city_id,location',
                'patient.city:id,name',
                'items.service:id,name',
                'clinic:id,name'
            )->whereNot('status', 'ask_to_cancel')->orderBy('created_at', 'desc')->get();
            $serviceCategories = Cache::remember('service_categories', 60, function () {
                return ServiceCategory::all()->keyBy('provider_category_id');
            });

            $allServices = Cache::remember('services', 60, function () {
                return Service::all()->groupBy('service_category_id');
            });

            return DataTables::of($provider_requests)

                ->addColumn('action', function ($provider_request) {

                    $actions = "";

                    if ($provider_request->status == 'pending') {
                        return '
                                <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'active']) . '"   class="btn rounded-pill btn-info w-25"  style="min-width: 70px;"
                                        data-id="' . $provider_request->id . '"
                                <span class="svg-icon svg-icon-3">
                                    <span class="svg-icon svg-icon-3">
                                        Active
                                    </span>
                                </span>
                                </a>
                                <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;" "
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                               ';
                    } else if ($provider_request->status == 'active') {
                        return  '
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'complete']) . '"   class="btn rounded-pill btn-success w-25" style="min-width: 70px;"
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Complete
                            </span>
                        </span>
                        </a>
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;""
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                       ';
                    } else if ($provider_request->status == 'ask_to_cancel') {
                        return  '
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'active']) . '"   class="btn rounded-pill btn-success w-25" style="min-width: 70px;"
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">Active</span>
                        </span>
                        </a>
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;""
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                       ';
                    }
                })
                // ->editColumn('name', function ($row) {
                //     if (is_json($row->name)) {
                //         $nameData = json_decode($row->name, true);
                //         $nameAr = $nameData['ar'] ?? 'N/A';
                //         $nameEn = $nameData['en'] ?? 'N/A';
                //         return '<span style="color:red;">' . htmlspecialchars($nameAr) . '</span> / <span style="color:blue;">' . htmlspecialchars($nameEn) . '</span>';
                //     }
                //     if (is_string($row->name)) {
                //         return htmlspecialchars($row->name);
                //     }
                // })
                ->editColumn('created_at', function ($provider_request) {

                    return "<div>" . date('Y/m/d', strtotime($provider_request->created_at)) . "</div>"
                        . "<div>" . date('h:i A', strtotime($provider_request->created_at)) . "</div>";
                })->addColumn('patient_name', function ($provider_request) {

                    return $provider_request->patient->name ?? '';
                })->addColumn('patient_phone', function ($provider_request) {

                    return $provider_request->patient->phone ?? '';
                })
                // ->addColumn('patient_gov', function ($provider_request) {

                //     return $provider_request->patient->governorate->name ?? '';
                // })
                ->addColumn('patient_city', function ($provider_request) {

                    return $provider_request->patient->city->name ?? '';
                })->addColumn('patient_location', function ($provider_request) {

                    return $provider_request->patient->location ?? '';
                })->editColumn('status', function ($provider_request) {

                    if ($provider_request->status == 'pending') {
                        return '<span class="badge bg-warning" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Pending</span>';
                    } else if ($provider_request->status == 'active') {
                        return '<span class="badge bg-primary" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Active</span>';
                    } else if ($provider_request->status == 'complete') {
                        return '<span class="badge bg-success" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Complete</span>';
                    } else if ($provider_request->status == 'cancel') {
                        return '<span class="badge bg-danger"style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Cancel</span>';
                    } else if ($provider_request->status == 'ask_to_cancel') {
                        return '<span class="badge bg-secondary"style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Ask to Cancel</span>';
                    }
                    return $provider_request->status;
                })->addColumn('provider_name', fn($req) => $req->provider->name ?? '')
                ->addColumn('provider_branch_location', fn($req) => $req->provider_branch->location ?? '')
                ->addColumn('provider_branch_phone', fn($req) => $req->provider_branch->phone ?? '')
                ->addColumn('description', fn($req) => $req->notes ?? '')
                ->addColumn('services', function ($provider_request) use ($serviceCategories, $allServices) {

                    $provider = $provider_request->provider;
                    $link = $provider_request->docs->first();
                    if (!$provider) {

                        return '<a href="' . $link . '" target="_blank" style="text-decoration: none; color: #333;">
                                <i class="fas fa-file-alt" style="margin-left: 40px;font-size: 24px; color: #007bff;"></i> surgery request
                                </a>';
                    }

                    if ($provider->provider_category_id == 4) {
                        if ($provider_request->doctor_id) {
                            return $provider_request->clinic?->name;
                        } else {
                            return '<a href="' . $link . '" target="_blank" style="text-decoration: none; color: #333;">
                            <i class="fas fa-file-alt" style="margin-left: 40px;font-size: 24px; color: #007bff;"></i> surgery request
                            </a>';
                        }
                    }

                    if ($provider_request->docs->count() > 0) {
                        $category = $serviceCategories[$provider->provider_category_id] ?? null;
                        $services = $allServices[$category?->id] ?? collect();
                        return view('Admin.CRUDS.providerRequests.service-form', compact('provider_request', 'services', 'link'));
                    }
                    $items = '';
                    foreach ($provider_request->items as $item) {
                        $items .= "<div>" . $item->service?->name . " --- " .  $item->price . " EGP</div>";
                    }
                    return $items;
                })
                ->addColumn('total_price', function ($provider_request) {
                    return '<div class="display-flex space-between">' .
                        '<div>' . $provider_request->total_price . '</div>' .
                        '<button type="button" class="btn btn-primary btn-sm edit-price-btn mt-1" ' .
                        'data-bs-toggle="modal" data-bs-target="#editPriceModal' . $provider_request->id . '">' .
                        'Edit Price</button></div>' .

                        '<div class="modal fade" id="editPriceModal' . $provider_request->id . '" tabindex="-1" aria-hidden="true">' .
                        '<div class="modal-dialog modal-dialog-centered">' .
                        '<div class="modal-content">' .
                        '<div class="modal-header">' .
                        '<h5 class="modal-title">Update Request Price</h5>' .
                        '<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>' .
                        '</div>' .
                        '<form id="editPriceForm' . $provider_request->id . '" action="' . route('admin.provider_requests.update_price') . '" method="POST">' .
                        csrf_field() .
                        '<div class="modal-body">' .
                        '<div class="mb-3">' .
                        '<label for="newPrice' . $provider_request->id . '" class="form-label">Price (EGP)</label>' .
                        '<input type="number" step="0.01" min="0" class="form-control" id="newPrice' . $provider_request->id . '" name="new_price" value="' . $provider_request->total_price . '" required>' .
                        '</div>' .
                        '<input type="hidden" id="request_id' . $provider_request->id . '" name="request_id" value="' . $provider_request->id . '">' .
                        '</div>' .
                        '<div class="modal-footer">' .
                        '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>' .
                        '<button type="submit" class="btn btn-primary" id="savePriceBtn' . $provider_request->id . '">Update Price</button>' .
                        '</div>' .
                        '</form>' .
                        '</div>' .
                        '</div>' .
                        '</div>';
                })->rawColumns(['services', 'total_price', 'mark_read'])
                ->escapeColumns([])
                ->make(true);
        }
        // $services = Service::all();
        // dd($services[0]);
        return view('Admin.CRUDS.providerRequests.index'); //compact('services')
    }

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $provider_requests = ProviderRequest::with(['provider', 'provider_branch', 'patient', 'items.service', 'clinic', 'relative', 'doctor', 'doctor.specialization'])->whereNot('status', 'ask_to_cancel')->orderBy('created_at', 'desc')->get();
            return DataTables::of($provider_requests)
                ->setRowAttr([
                    'style' => function ($row) {
                        return $row->read_at == null ? 'background-color: #f8d7da;' : '';
                    }
                ])
                ->addColumn('mark_read', function ($row) {
                    if ($row->read_at)
                        return '<button class="btn btn-sm btn-secondary disabled">Readed</button>';
                    else
                        return '<button class="btn btn-sm btn-primary mark-read" data-id="' . $row->id . '">Mark as Read</button>';
                })
                ->addColumn('action', function ($provider_request) {
                    $actions = "";

                    if ($provider_request->status == 'pending') {
                        return '
                                <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'active']) . '"   class="btn rounded-pill btn-info w-25"  style="min-width: 70px;"
                                        data-id="' . $provider_request->id . '"
                                <span class="svg-icon svg-icon-3">
                                    <span class="svg-icon svg-icon-3">
                                        Active
                                    </span>
                                </span>
                                </a>
                                <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;" "
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                               ';
                    } else if ($provider_request->status == 'active') {
                        return  '
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'complete']) . '"   class="btn rounded-pill btn-success w-25" style="min-width: 70px;"
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Complete
                            </span>
                        </span>
                        </a>
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;""
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                       ';
                    } else if ($provider_request->status == 'ask_to_cancel') {
                        return  '
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'active']) . '"   class="btn rounded-pill btn-success w-25" style="min-width: 70px;"
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">Active</span>
                        </span>
                        </a>
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;""
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                       ';
                    }
                })
                ->addColumn('change_provider', function ($provider_request) {

                    if ($provider_request->status == 'pending') {
                        // $providers = $provider_request->provider_id ? Provider::where('provider_category_id', $provider_request->provider?->provider_category_id)->get() : Provider::get();

                        if ($provider_request->provider_id) { //save 48 duplicated queries
                            $providers = Cache::remember('providers' . $provider_request->provider->provider_category_id, 60, function () use ($provider_request) {
                                return  Provider::where('provider_category_id', $provider_request->provider->provider_category_id)->get();
                            });
                        } else {
                            $providers = Cache::remember('providers', 60, function () {
                                return  Provider::get();
                            });
                        }

                        $provider_branches = Cache::remember('provider_branches' . $provider_request->provider_id, 60, function () use ($provider_request) {
                            return  ProviderBranch::where('provider_id', $provider_request->provider_id)->get();
                        });

                        return '<button type="button" class="btn rounded-pill btn-warning " style="min-width: 70px;"
                        data-bs-toggle="modal" data-bs-target="#changeProviderModal' . $provider_request->id . '">
                        <span class="svg-icon svg-icon-3">
                            <span class="svg-icon svg-icon-3">Change Provider</span>
                        </span>
                    </button>
                    <div class="modal fade" id="changeProviderModal' . $provider_request->id . '" tabindex="-1" aria-hidden="true">
                        <div class="modal-dialog modal-dialog-centered">
                            <div class="modal-content">
                                <div class="modal-header">
                                    <h5 class="modal-title">Change Provider & Branch</h5>
                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                </div>
                                <form action="' . route('admin.provider_requests.update_provider') . '" method="POST">
                                    ' . csrf_field() . '
                                    <input type="hidden" name="request_id" value="' . $provider_request->id . '">
                                    <div class="modal-body">
                                        <div class="mb-3">
                                            <label for="provider_id" class="form-label">Provider</label>
                                            <select class="form-select" name="provider_id" id="provider_id" required>
                                                <option value="">Select Provider</option>
                                                ' . $providers->map(function ($provider) use ($provider_request) {
                            return '<option value="' . $provider->id . '" ' . ($provider_request->provider_id == $provider->id ? 'selected' : '') . '>' . $provider->name . '</option>';
                        })->join('') . '
                                            </select>
                                        </div>
                                        <div class="mb-3">
                                            <label for="provider_branch_id" class="form-label">Branch</label>
                                            <select class="form-select" name="provider_branch_id" id="provider_branch_id" required>
                                                <option value="">Select Branch</option>
                                                ' . $provider_branches->map(function ($branch) use ($provider_request) {
                            return '<option value="' . $branch->id . '" ' . ($provider_request->provider_branch_id == $branch->id ? 'selected' : '') . '>' . $branch->location . '</option>';
                        })->join('') . '
                                            </select>
                                        </div>
                                    </div>
                                    <div class="modal-footer">
                                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                        <button type="submit" class="btn btn-primary">Update</button>
                                    </div>
                                </form>
                            </div>
                        </div>
                    </div>';
                    } else {
                        return '<span class="text-muted">--</span>';
                    }
                })
                ->addColumn('description', function ($provider_request) {
                    return $provider_request->notes ?? '';
                })
                // ->editColumn('name', function ($row) {
                //     if (is_json($row->name)) {
                //         $nameData = json_decode($row->name, true);
                //         $nameAr = $nameData['ar'] ?? 'N/A';
                //         $nameEn = $nameData['en'] ?? 'N/A';
                //         return '<span style="color:red;">' . htmlspecialchars($nameAr) . '</span> / <span style="color:blue;">' . htmlspecialchars($nameEn) . '</span>';
                //     }
                //     if (is_string($row->name)) {
                //         return htmlspecialchars($row->name);
                //     }
                // })
                ->editColumn('created_at', function ($provider_request) {

                    return "<div>" . date('Y/m/d', strtotime($provider_request->created_at)) . "</div>"
                        . "<div>" . date('h:i A', strtotime($provider_request->created_at)) . "</div>";
                })->addColumn('patient_name', function ($provider_request) {
                    if ($provider_request->relative_id) {
                        return $provider_request->patient->name . ' / ' . $provider_request->relative->first_name;
                    }
                    return $provider_request->patient->name ?? '';
                })->addColumn('patient_phone', function ($provider_request) {

                    return $provider_request->patient->phone ?? '';
                })
                // ->addColumn('patient_gov', function ($provider_request) {

                //     return $provider_request->patient->governorate->name ?? '';
                // })
                ->addColumn('patient_city', function ($provider_request) {

                    return $provider_request->patient->city->name ?? '';
                })->addColumn('patient_location', function ($provider_request) {

                    return $provider_request->patient->location ?? '';
                })->editColumn('status', function ($provider_request) {

                    if ($provider_request->status == 'pending') {
                        return '<span class="badge bg-warning" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Pending</span>';
                    } else if ($provider_request->status == 'active') {
                        return '<span class="badge bg-primary" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Active</span>';
                    } else if ($provider_request->status == 'complete') {
                        return '<span class="badge bg-success" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Complete</span>';
                    } else if ($provider_request->status == 'cancel') {
                        return '<span class="badge bg-danger"style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Cancel</span>';
                    } else if ($provider_request->status == 'ask_to_cancel') {
                        return '<span class="badge bg-secondary"style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Ask to Cancel</span>';
                    }
                    return $provider_request->status;
                })->addColumn('provider_name', function ($provider_request) {

                    return $provider_request->provider->name ?? '';
                })->addColumn('provider_branch_location', function ($provider_request) {

                    return $provider_request->provider_branch->location ?? '';
                })->addColumn('provider_branch_phone', function ($provider_request) {

                    return $provider_request->provider_branch->phone ?? '';
                })
                ->addColumn('services', function ($provider_request) {
                    $link = $provider_request->docs->first();
                    if (!$provider_request->provider) {

                        return '<a href="' . $link . '" target="_blank" style="text-decoration: none; color: #333;">
                                <i class="fas fa-file-alt" style="margin-left: 40px;font-size: 24px; color: #007bff;"></i> surgery request
                                </a>';
                    }

                    if ($provider_request->provider->provider_category_id == 4) {
                        if ($provider_request->doctor_id) {
                            return $provider_request->provider->name . ' / ' . $provider_request->doctor->specialization?->name;
                        } else {
                            return '<a href="' . $link . '" target="_blank" style="text-decoration: none; color: #333;">
                            <i class="fas fa-file-alt" style="margin-left: 40px;font-size: 24px; color: #007bff;"></i> surgery request
                            </a>';
                        }
                    }

                    $service_category_id = Cache::remember('service_category_id' . $provider_request->provider->provider_category_id, 60, function () use ($provider_request) {
                        return ServiceCategory::where('provider_category_id', $provider_request->provider->provider_category_id)->first()?->id;
                    });
                    $services = Cache::remember('services' . $service_category_id, 60, function () use ($service_category_id) {
                        return Service::where('service_category_id', $service_category_id)->get();
                    });

                    // if ($provider_request->docs->count() > 0) {
                    $link = $provider_request->docs->first();
                    return view('Admin.CRUDS.providerRequests.service-form', compact('provider_request', 'services', 'link'));
                    // }
                    $items = '';
                    foreach ($provider_request->items as $item) {
                        $items .= "<div>" . $item->service?->name . " --- " .  $item->price . " EGP</div>";
                    }
                    return $items;
                })
                ->addColumn('total_price', function ($provider_request) {
                    return '<div class="display-flex space-between">' .
                        '<div>' . $provider_request->total_price . '</div>' .
                        '<button type="button" class="btn btn-primary btn-sm edit-price-btn mt-1" ' .
                        'data-bs-toggle="modal" data-bs-target="#editPriceModal' . $provider_request->id . '">' .
                        'Edit Price</button></div>' .

                        '<div class="modal fade" id="editPriceModal' . $provider_request->id . '" tabindex="-1" aria-hidden="true">' .
                        '<div class="modal-dialog modal-dialog-centered">' .
                        '<div class="modal-content">' .
                        '<div class="modal-header">' .
                        '<h5 class="modal-title">Update Request Price</h5>' .
                        '<button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>' .
                        '</div>' .
                        '<form id="editPriceForm' . $provider_request->id . '" action="' . route('admin.provider_requests.update_price') . '" method="POST">' .
                        csrf_field() .
                        '<div class="modal-body">' .
                        '<div class="mb-3">' .
                        '<label for="newPrice' . $provider_request->id . '" class="form-label">Price (EGP)</label>' .
                        '<input type="number" step="0.01" min="0" class="form-control" id="newPrice' . $provider_request->id . '" name="new_price" value="' . $provider_request->total_price . '" required>' .
                        '</div>' .
                        '<input type="hidden" id="request_id' . $provider_request->id . '" name="request_id" value="' . $provider_request->id . '">' .
                        '</div>' .
                        '<div class="modal-footer">' .
                        '<button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>' .
                        '<button type="submit" class="btn btn-primary" id="savePriceBtn' . $provider_request->id . '">Update Price</button>' .
                        '</div>' .
                        '</form>' .
                        '</div>' .
                        '</div>' .
                        '</div>';
                })->rawColumns(['services', 'total_price', 'mark_read'])
                // ->editColumn('specialization_id', function ($row) {
                //     return $row->specialization->name ?? '';
                // })
                // ->addColumn('doctor_times', function ($row) {
                //     $route = route('admin.doctor_times', $row->id);
                //     return "<a href='$route' class='btn btn-outline-primary'>" . helperTrans('admin.Doctor Time') . "</a>";
                // })
                // ->addColumn('doctor_contract', function ($row) {
                //     $latestContract = $row->contracts()->latest()->first();
                //     if ($latestContract) {
                //         $route = route('admin.doctor_contract_show', $latestContract->id);
                //         return "<a href='$route' class='btn btn-outline-success'>" . helperTrans('admin.Perview') . "</a>";
                //     }
                // })
                // ->setRowClass(function ($row) {
                //     $latestContract = $row->contracts()->latest()->first();
                //     if ($latestContract && strtotime($latestContract->contract_end_date) < time()) {
                //         return 'custom_warning_bg';
                //     }
                //     return '';
                // })
                // ->addColumn('update', function ($row) {
                //     $doctorUpdate = DoctorUpdate::where('doctor_id', $row->id)->latest()->first();
                //     if (isset($doctorUpdate)) {
                //         if ($doctorUpdate->status == 'pending') {
                //             $route = route('admin.update.doctor', $doctorUpdate->id);
                //             return '<a href="' . $route . '" class="btn btn-warning ">Show Update</a>';
                //         } elseif ($doctorUpdate->status == 'reject') {
                //             return '<span class="btn btn-danger">Reject</span>';
                //         } elseif ($doctorUpdate->status == 'approved') {
                //             return '<span class="btn btn-success">Approved</span>';
                //         }
                //     } else {
                //         return '<span class="btn btn-info">Not Needed</span>';
                //     }
                // })
                // ->editColumn('status', function ($row) {
                //     $active = '';
                //     $operation = '';
                //     $operation = '';

                //     if ($row->status == 1)
                //         $active = 'checked';

                //     return '<div class="form-check form-switch">
                //                <input ' . $operation . '  class="form-check-input activeBtn" data-id="' . $row->id . ' " type="checkbox" role="switch" id="flexSwitchCheckChecked" ' . $active . '  >
                //             </div>';
                // })
                // ->editColumn('image', function ($admin) {
                //     return '
                //       <a data-fancybox="" href="' . asset(get_file($admin->image)) . '">
                //         <img height="60px" src="' . asset(get_file($admin->image)) . '">
                //         </a>
                //     ';
                // })

                // ->editColumn('created_at', function ($admin) {
                //     return date('Y/m/d', strtotime($admin->created_at));
                // })
                ->escapeColumns([])
                ->make(true);
        }
        // $services = Service::all();
        // dd($services[0]);
        $provider_categories = ProviderCategory::all();
        return view('Admin.CRUDS.providerRequests.index', compact('provider_categories')); //compact('services')
    }

    public function provider_cancel_requests(Request $request) //ask_to_cancel_requests
    {
        if ($request->ajax()) {
            $provider_requests = ProviderRequest::where('status', 'ask_to_cancel')->orderBy('created_at', 'desc')->get();
            return DataTables::of($provider_requests)
                ->setRowAttr([
                    'style' => function ($row) {
                        return $row->read_at == null ? 'background-color: #f8d7da;' : '';
                    }
                ])
                ->addColumn('mark_read', function ($row) {
                    if ($row->read_at)
                        return '<button class="btn btn-sm btn-secondary disabled">Readed</button>';
                    else
                        return '<button class="btn btn-sm btn-primary mark-read" data-id="' . $row->id . '">Mark as Read</button>';
                })
                ->addColumn('action', function ($provider_request) {

                    $actions = "";

                    if ($provider_request->status == 'pending') {
                        return '
                                <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'active']) . '"   class="btn rounded-pill btn-info w-25"  style="min-width: 70px;"
                                        data-id="' . $provider_request->id . '"
                                <span class="svg-icon svg-icon-3">
                                    <span class="svg-icon svg-icon-3">
                                        Active
                                    </span>
                                </span>
                                </a>
                                <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;" "
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                               ';
                    } else if ($provider_request->status == 'active') {
                        return  '
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'complete']) . '"   class="btn rounded-pill btn-success w-25" style="min-width: 70px;"
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Complete
                            </span>
                        </span>
                        </a>
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-danger waves-effect waves-light w-25 " style="min-width: 70px;""
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Cancel
                            </span>
                        </span>
                        </a>
                       ';
                    } else if ($provider_request->status == 'ask_to_cancel') {
                        return  '
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'cancel']) . '"   class="btn rounded-pill btn-info waves-effect waves-light w-25 " style="min-width: 70px;""
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 ">
                                Accept
                            </span>
                        </span>
                        </a>
                        <a href="' . route('admin.provider_requests.change_status', ['id' => $provider_request->id, 'status' => 'active']) . '"   class="btn rounded-pill btn-danger  w-25" style="min-width: 70px;"
                                data-id="' . $provider_request->id . '"
                        <span class="svg-icon svg-icon-3 bg-success">
                            <span class="svg-icon svg-icon-3 "> Reject </span>
                        </span>
                        </a>

                       ';
                    }
                })
                // ->editColumn('name', function ($row) {
                //     if (is_json($row->name)) {
                //         $nameData = json_decode($row->name, true);
                //         $nameAr = $nameData['ar'] ?? 'N/A';
                //         $nameEn = $nameData['en'] ?? 'N/A';
                //         return '<span style="color:red;">' . htmlspecialchars($nameAr) . '</span> / <span style="color:blue;">' . htmlspecialchars($nameEn) . '</span>';
                //     }
                //     if (is_string($row->name)) {
                //         return htmlspecialchars($row->name);
                //     }
                // })
                ->editColumn('created_at', function ($provider_request) {

                    return "<div>" . date('Y/m/d', strtotime($provider_request->created_at)) . "</div>"
                        . "<div>" . date('h:i A', strtotime($provider_request->created_at)) . "</div>";
                })->addColumn('patient_name', function ($provider_request) {

                    return $provider_request->patient->name ?? '';
                })->addColumn('patient_phone', function ($provider_request) {

                    return $provider_request->patient->phone ?? '';
                })
                // ->addColumn('patient_gov', function ($provider_request) {

                //     return $provider_request->patient->governorate->name ?? '';
                // })
                ->addColumn('patient_city', function ($provider_request) {

                    return $provider_request->patient->city->name ?? '';
                })->addColumn('patient_location', function ($provider_request) {

                    return $provider_request->patient->location ?? '';
                })->editColumn('status', function ($provider_request) {

                    if ($provider_request->status == 'pending') {
                        return '<span class="badge bg-warning" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Pending</span>';
                    } else if ($provider_request->status == 'active') {
                        return '<span class="badge bg-primary" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Active</span>';
                    } else if ($provider_request->status == 'complete') {
                        return '<span class="badge bg-success" style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Complete</span>';
                    } else if ($provider_request->status == 'cancel') {
                        return '<span class="badge bg-danger"style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Cancel</span>';
                    } else if ($provider_request->status == 'ask_to_cancel') {
                        return '<span class="badge bg-secondary"style="font-size: 0.8rem; padding: 3px 8px; min-width: 70px;">Ask to Cancel</span>';
                    }
                    return $provider_request->status;
                })->addColumn('provider_name', function ($provider_request) {

                    return $provider_request->provider->name ?? '';
                })->addColumn('provider_branch_location', function ($provider_request) {

                    return $provider_request->provider_branch->location ?? '';
                })->addColumn('provider_branch_phone', function ($provider_request) {

                    return $provider_request->provider_branch->phone ?? '';
                })
                ->addColumn('services', function ($provider_request) {
                    if ($provider_request->items->count() == 0 && $provider_request->docs->count() > 0) {
                        $link = $provider_request->docs->first();

                        $html = <<<HTML
                            <a href="$link" target="_blank" style="text-decoration: none; color: #333;">
                            <i class="fas fa-file-alt" style="margin-left: 40px;font-size: 24px; color: #007bff;"></i>
                            <span style="margin-left: 5px;font-size: 14px;">Services</span>                            </a>
                            HTML;
                        return $html;
                    }
                    $items = '';
                    foreach ($provider_request->items as $item) {
                        $items .= "<div>" . $item->service->name . "</div>";
                    }
                    return $items;
                })
                ->addColumn('total_price', function ($provider_request) {
                    if ($provider_request->items->count() == 0 && $provider_request->docs->count() > 0) {
                        return '<div class="display-flex space-between">' . '<div>' . $provider_request->total_price . '</div>' . ' <button type="button" class="btn btn-primary btn-sm edit-price-btn mt-1" data-id="' . $provider_request->id . '" data-price="' . $provider_request->total_price . '" data-bs-toggle="modal" data-bs-target="#editPriceModal">Edit Price</button></div>';
                    }
                    return $provider_request->total_price ?? '';
                })
                ->rawColumns(['mark_read'])
                ->escapeColumns([])
                ->make(true);
        }
        return view('Admin.CRUDS.providerRequests.provider_cancel_requests');
    }
    public function change_status(Request $request)
    {
        // dd($request->all());
        $provider_request = ProviderRequest::find($request->id);
        $old_status = $provider_request->status;
        $provider_request->status = $request->status;
        $provider_request->save();

        if ($old_status == 'ask_to_cancel' && $request->status == 'active') {
            run_push_notification($provider_request->provider->id, 'provider', [
                'title' => 'Cancellation Request',
                'body' => 'Your cancellation request was rejected',
                'type' => 'booking_status',
            ]);
        } else {
            if ($provider_request->provider) {
                run_push_notification($provider_request->provider->id, 'provider', [
                    'title' => 'Booking ' . $request->status,
                    'body' => 'Booking has been updated to ' . ($request->status == 'active' ? 'accepted' : $request->status),
                    'type' => 'booking_status',
                ]);
            }
            run_push_notification($provider_request->patient->id, 'patient', [
                'title' => 'Booking ' . $request->status,
                'body' => 'booking ' . ($provider_request->provider ? (' at ' . $provider_request->provider->name) : ' ') . ' has been updated to ' . ($request->status == 'active' ? 'accepted' : $request->status),
                'type' => 'booking_status',
            ]);
        }


        return redirect()->back();
    }

    public function create()
    {
        // For now, return a simple view or redirect
        // You can implement the create form later if needed
        return redirect()->route('admin.provider_requests')->with('info', 'Create functionality not implemented yet');
    }

    public function store(Request $request)
    {
        // For now, return a simple response
        // You can implement the store logic later if needed
        return redirect()->route('admin.provider_requests')->with('info', 'Store functionality not implemented yet');
    }

    public function show($id)
    {
        $provider_request = ProviderRequest::with([
            'provider',
            'provider_branch',
            'patient',
            'items.service',
            'clinic',
            'relative',
            'doctor',
            'doctor.specialization'
        ])->findOrFail($id);

        return view('Admin.CRUDS.providerRequests.show', compact('provider_request'));
    }

    public function edit($id)
    {
        // For now, return a simple response
        // You can implement the edit form later if needed
        return redirect()->route('admin.provider_requests')->with('info', 'Edit functionality not implemented yet');
    }

    public function update(Request $request, $id)
    {
        // For now, return a simple response
        // You can implement the update logic later if needed
        return redirect()->route('admin.provider_requests')->with('info', 'Update functionality not implemented yet');
    }

    public function destroy($id)
    {
        try {
            $provider_request = ProviderRequest::findOrFail($id);
            $provider_request->delete();

            return response()->json([
                'code' => 200,
                'message' => 'Provider request deleted successfully'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'code' => 500,
                'message' => 'Error deleting provider request'
            ], 500);
        }
    }

    public function markAsRead($id)
    {
        try {
            $provider_request = ProviderRequest::findOrFail($id);

            if (is_null($provider_request->read_at)) {
                $provider_request->read_at = now();
                $provider_request->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Provider request marked as read successfully'
                ]);
            }

            return response()->json([
                'status' => true,
                'message' => 'Provider request was already marked as read'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }

    public function updatePrice(Request $request)
    {
        try {
            $request->validate([
                'request_id' => 'required|exists:provider_requests,id',
                'new_price' => 'required|numeric|min:0'
            ]);

            $provider_request = ProviderRequest::with(['patient', 'provider'])->findOrFail($request->request_id);
            $old_price = $provider_request->total_price;

            $provider_request->update([
                'total_price' => $request->new_price,
                'show_for_provider' => true,
            ]);

            // Send notifications
            if ($provider_request->patient) {
                run_push_notification($provider_request->patient->id, 'patient', [
                    'title' => 'Price Update',
                    'body' => sprintf(
                        'The price for your request at %s has been updated from %s EGP to %s EGP',
                        $provider_request->provider ? $provider_request->provider->name : 'the provider',
                        number_format($old_price, 2),
                        number_format($request->new_price, 2)
                    ),
                    'type' => 'price_update'
                ]);
            }

            if ($provider_request->provider) {
                run_push_notification($provider_request->provider->id, 'provider', [
                    'title' => 'Price Update',
                    'body' => sprintf(
                        'The price for request #%d has been updated from %s EGP to %s EGP',
                        $provider_request->id,
                        number_format($old_price, 2),
                        number_format($request->new_price, 2)
                    ),
                    'type' => 'price_update'
                ]);
            }

            return response()->json([
                'status' => 'success',
                'message' => 'Price updated successfully',
                'new_price' => number_format($request->new_price, 2)
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => 'Failed to update price: ' . $e->getMessage()
            ], 500);
        }
    }

    public function addService(Request $request)
    {
        $provider_request = ProviderRequest::where('id', $request->provider_request_id)->first();
        $price = 0;
        foreach ($request->services as $key => $service_id) {
            $provider_service = ProviderService::updateOrCreate([
                'service_id' => $service_id,
                'provider_id' => $provider_request->provider_id,
            ], [
                'price' => $request->service_price[$key],
            ]);

            $price += $provider_service->price;
        }


        $patient_subscribe = PatientSubscribe::where('status', 'active')->where('patient_id', $provider_request->patient_id)->first();

        if ($patient_subscribe) {
            $price = $price * (100 - $provider_request->provider->discount) / 100;
        }

        $provider_request->total_price = $price;
        $provider_request->save();

        ProviderRequestItem::where([
            'provider_request_id' => $provider_request->id
        ])->delete();

        foreach ($request->services as $key => $service_id) {


            ProviderRequestItem::create([
                'provider_request_id' => $provider_request->id,
                'service_id' => $service_id,
                'quantity' => 1,
                'price' => $request->service_price[$key],
            ]);
        }
        // Send push notification to patient and provider
        run_push_notification($provider_request->patient->id, 'patient', [
            'title' => 'Booking Status',
            'body' => ' Services in ' . ($provider_request->provider ? $provider_request->provider->name : ' ') . ' on ' . date('l, d M Y, \a\t h:i A', strtotime($provider_request->created_at)) . ' will be cost ' . $provider_request->total_price . ' EGP',
            'type' => 'booking_status',
        ]);
        if ($provider_request->provider) {
            run_push_notification($provider_request->provider->id, 'provider', [
                'title' => 'Booking Update',
                'body' => 'A booking has been modified',
                'type' => 'booking_status',
            ]);
        }

        return redirect()->back();
    }

    public function updateProvider(Request $request)
    {
        try {
            $request->validate([
                'request_id' => 'required|exists:provider_requests,id',
                'provider_id' => 'required|exists:providers,id',
                'provider_branch_id' => 'required|exists:provider_branches,id'
            ]);

            $provider_request = ProviderRequest::with(['patient', 'provider'])->findOrFail($request->request_id);
            $old_provider = $provider_request->provider;
            $old_branch = $provider_request->provider_branch;

            $provider_request->update([
                'provider_id' => $request->provider_id,
                'provider_branch_id' => $request->provider_branch_id
            ]);

            // Update service price in provider request items
            $provider_request_items = ProviderRequestItem::where('provider_request_id', $provider_request->id)->get();
            $total_price = 0;
            foreach ($provider_request_items as $item) {
                $provider_service = ProviderService::where('provider_id', $request->provider_id)->where('service_id', $item->service_id)->first();
                if ($provider_service) {
                    $item->price = $provider_service->price;
                    $item->save();
                    $total_price += $item->price;
                }
            }
            // Apply discount if user has subscription
            if ($provider_request->patient && $provider_request->patient->subscribes->count() > 0) {
                $new_provider = Provider::find($request->provider_id);
                $discount = $new_provider?->discount;
                $total_price = $total_price - ($total_price * ($discount / 100));
            }

            // dd([$total_price,$discount]);

            $provider_request->total_price = $total_price;
            $provider_request->save();


            // Send notifications
            if ($provider_request->patient && $old_provider) {
                run_push_notification($provider_request->patient->id, 'patient', [
                    'title' => 'Provider Changed',
                    'body' => sprintf(
                        'Your request provider has been changed from %s to %s',
                        $old_provider->name,
                        $provider_request->provider->name
                    ),
                    'type' => 'provider_change'
                ]);
            }

            if ($old_provider && $old_provider->id != $provider_request->provider_id) {
                run_push_notification($old_provider->id, 'provider', [
                    'title' => 'Request Transferred',
                    'body' => "Request #$provider_request->id has been transferred to another provider",
                    'type' => 'request_transfer'
                ]);
            }

            if ($request->provider_id && $request->provider_id != $old_provider->id) {
                run_push_notification($request->provider_id, 'provider', [
                    'title' => 'New Request',
                    'body' => "Request #$provider_request->id has been transferred to you",
                    'type' => 'request_transfer'
                ]);
            }

            return redirect()->back()->with('success', 'Provider and branch updated successfully');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'Failed to update provider: ' . $e->getMessage());
        }
    }

    public function get_provider_branches($provider_id)
    {
        $branches = \App\Models\ProviderBranch::where('provider_id', $provider_id)
            ->select('id', 'location')
            ->get(); //Required

        return response()->json($branches);
    }

    public function getServicePrice($provider_id, $service_id)
    {
        try {
            $providerService = ProviderService::where('provider_id', $provider_id)
                ->where('service_id', $service_id)
                ->first();

            if ($providerService) {
                return response()->json([
                    'success' => true,
                    'price' => $providerService->price,
                    'discount' => $providerService->discount ?? 0
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Service price not found for this provider',
                    'price' => 0
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving service price',
                'price' => 0
            ], 500);
        }
    }

    public function getProviderIdFromRequest($request_id)
    {
        try {
            $providerRequest = ProviderRequest::find($request_id);

            if ($providerRequest && $providerRequest->provider_id) {
                return response()->json([
                    'success' => true,
                    'provider_id' => $providerRequest->provider_id
                ]);
            } else {
                return response()->json([
                    'success' => false,
                    'message' => 'Provider request not found or no provider assigned'
                ]);
            }
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Error retrieving provider request'
            ], 500);
        }
    }
}
