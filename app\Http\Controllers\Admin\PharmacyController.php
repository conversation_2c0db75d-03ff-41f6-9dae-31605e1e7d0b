<?php

namespace App\Http\Controllers\Admin;

use App\Exports\PharmacyExport;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PharmacyRequest;
use App\Http\Traits\ResponseTrait;
use App\Http\Traits\Upload_Files;
use App\Imports\PharmacyImport;
use App\Models\Category;
use App\Models\Pharmacy;
use App\Models\ProviderCategory;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Models\DoctorContract;
use Illuminate\Support\Arr;
use App\Models\Delegate;
use Illuminate\Support\Facades\DB;




class PharmacyController extends Controller
{
    //
    use  ResponseTrait,Upload_Files;

    public function index(Request $request)
    {

        if ($request->ajax()) {
            $admins = Pharmacy::query()->latest();
            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {

                    $edit = '';
                    $delete = '';


                    return '
                            <button ' . $edit . '  class="editBtn btn rounded-pill btn-primary waves-effect waves-light"
                                    data-id="' . $admin->id . '"
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-edit"></i>
                                </span>
                            </span>
                            </button>
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $admin->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';


                })

                ->addColumn('pharmacy_branches', function ($row) {
                    $route=route('pharmacy_branches.index').'?pharmacy_id='.$row->id;
                    return "<a href='$route' class='form-control'>".helperTrans('api.Show Branches')."</a>";
                })



                ->editColumn('name', function ($row) {
                    return $row->name;
                })

                ->editColumn('desc', function ($row) {
                    return $row->desc;
                })

                ->editColumn('image', function ($admin) {
                    return '
                              <a data-fancybox="" href="' . get_file($admin->image) . '">
                                <img height="60px" src="' . get_file($admin->image) . '">
                            </a>
                             ';
                })



                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);


        }
        return view('Admin.CRUDS.pharmacy.index');
    }


    public function create()
    {

        $categories=Category::get();


        return view('Admin.CRUDS.pharmacy.parts.create',compact('categories'));
    }

    public function store(Request $request)
    {
        DB::beginTransaction();
        try{ 
            $data = $request->validationData();
            $workingHours = [];
                if ($request->has('working_days')) {
                    foreach ($request->working_days as $day) {
                        $workingHours[$day] = [
                            'start_time' => $request->input('start_time')[$day] ?? null,
                            'end_time' => $request->input('end_time')[$day] ?? null,
                        ];
                    }
                }
            $exceptedColumn = [
                'work_time',
                'start_time',
                'end_time',
                'working_days',
                'contract_start_date',
                'contract_end_date',
                'attach_contract',
                'attach_documents',
                'attach_price_list',
                'price_list_discount',
                'contract_note',
                'bank_name',
                'bank_account_number',
                'bank_iban',
                'bank_swift_code',
                'bank_branch_bank',
                'bank_mobile_number',
                'e_wallet_name',
                'e_wallet_mobile_number',
                'instapay_mobile_number',
                'instapay_email',
                'cheque_name',
                'working_hours',
                "price_list_acutemedicine_local",
                "price_list_chronicmedication_local",
                "price_list_cosmetics_local",
                "price_list_otherservice_local",
                "price_list_acutemedicine_imported",
                "price_list_chronicmedication_imported",
                "price_list_cosmetics_imported",
                "price_list_otherservice_imported",
                'clamis_due_date',
                'admin_fees',
                'name_1',
                'email_1',
                'phone_1',
                'name_2',
                'email_2',
                'phone_2',
    
            ];
            unset(
                $data['category_id'],
                $data['price_list_hematology'],
                $data['price_list_chemistry'],
                $data['price_list_hormones'],
                $data['price_list_serology'],
                $data['home_care_gross_price'],
                $data['home_care_discount'],
                $data['home_care_net_price'],
                $data['price_list_immunology'],
                $data['price_list_drug'],
                $data['price_list_microbiology'],
                $data['price_list_pathology'],
                $data['price_list_other'],
                $data['service_clinic_gross_price'],
                $data['service_clinic_discount'],
                $data['service_clinic_net_price'],
                $data['service_online_gross_price'],
                $data['service_online_discount'],
                $data['service_online_net_price'],
                $data['price_list_outpatient'],
                $data['price_list_intpatient'],
                $data['price_list_consultation'],
                $data['price_list_laboratory'],
                $data['price_list_radiologe'],
                $data['price_list_exception'],
                $data['gender'],
                $data['specialization_id'],
                $data['experience_id'],
                $data["price_list_hospital_consulation"],
                $data["price_list_hospital_outpatient"],
                $data["price_list_hospital_inpatient"],
                $data["price_list_hospital_laboratory" ],
                $data["price_list_hospital_radiology" ],
                $data["price_list_hospital_discount_other" ],
                $data["price_list_doctor_visit"],
                $data["price_list_nurse_visit"],
                $data["price_list_hospital_other"],
                $data["nickname_hospital_doctor" ],
                $data["specialization_hospital_id" ],
                $data["sub_specialization_id]"],
                $data["price_list_xray"],
                $data["price_list_mri"],
                $data["price_list_ct"],
                $data["price_list_other"]
            );
    
            if ($request->image)
            $data["image"] = $this->uploadFiles('pharmacies', $request->file('image'), null);
    
            $data['password'] = bcrypt($request->password);
            if (is_array($data['name'])) {
                $data['name'] = json_encode($data['name']);
            }
            if (is_array($data['address'])) {
                $data['address'] = json_encode($data['address']);
            }
            if (is_array($data['about'])) {
                $data['about'] = json_encode($data['about']);
            }
            $data['delegate'] = $request->has('delegate') ? 1 : 0;
            $datafinal = Arr::except($data, $exceptedColumn);
            $pharmacy=Pharmacy::create($datafinal);
    
            $code = "pharmacy_$pharmacy->id";
            $pharmacy->update(['code' => $code]);
                $contractData = [
                    'working_hours' => json_encode($workingHours),
                    'pharmacy_id' => $pharmacy->id,
                    'contract_start_date' => $request->contract_start_date,
                    'contract_end_date' => $request->contract_end_date,
                    'attach_contract' => $request->attach_contract, // if you have a file to upload, handle it similarly to images
                    'attach_documents' => $request->attach_documents,
                    'attach_price_list' => $request->attach_price_list,
                    'price_list_chronicmedication_local' => $request->price_list_chronicmedication_local,
                    'price_list_acutemedicine_local' => $request->price_list_acutemedicine_local,
                    'price_list_chronicmedication_imported' => $request->price_list_chronicmedication_imported,
                    'price_list_acutemedicine_imported' => $request->price_list_acutemedicine_imported,
                    'price_list_cosmetics_local' => $request->price_list_cosmetics_local,
                    'price_list_cosmetics_imported' => $request->price_list_cosmetics_imported,
                    'price_list_otherservice_local' => $request->price_list_otherservice_local,
                    'price_list_otherservice_imported' => $request->price_list_otherservice_imported,
                    'contract_note' => $request->contract_note,
                    'clamis_due_date' => $request->clamis_due_date,
                    'admin_fees' => $request->admin_fees,
                    'bank_name' => $request->bank_name,
                    'bank_account_number' => $request->bank_account_number,
                    'bank_iban' => $request->bank_iban,
                    'bank_swift_code' => $request->bank_swift_code,
                    'bank_branch_bank' => $request->bank_branch_bank,
                    'bank_mobile_number' => $request->bank_mobile_number,
                    'e_wallet_name' => $request->e_wallet_name,
                    'e_wallet_mobile_number' => $request->e_wallet_mobile_number,
                    'instapay_mobile_number' => $request->instapay_mobile_number,
                    'instapay_email' => $request->instapay_email,
                    'cheque_name' => $request->cheque_name,
                ];
                DoctorContract::create($contractData);
            if ($request->category_id)
                foreach ($request->category_id as $category_id)
                {
                    ProviderCategory::create([
                        'provider_id'=>$pharmacy->id,
                        'category_id'=>$category_id,
                        'provider_type'=>'pharmacy',
                    ]);
                }
                if ($request->has('delegate')) {
                    Delegate::create([
                        'pharmacy_id ' => $pharmacy->id,
                        'name_1' => $request->name_1,
                        'email_1' => $request->email_1,
                        'phone_1' => $request->phone_1,
                        'name_2' => $request->name_2,
                        'email_2' => $request->email_2,
                        'phone_2' => $request->phone_2,
                    ]);
                }
    
                DB::commit();
                return redirect()->route('pharmacies.index')->with('success', 'created success');
        }catch (\Exception $e){
            DB::rollBack();
            return redirect()->back()->with('fail','happen error when add data');
        }

    }


    public function show($id)
    {


        //
    }


    public function edit($id )
    {


        $row=Pharmacy::findOrFail($id);

        $categories=Category::get();
        $categoriesIdes=ProviderCategory::where('provider_id',$row->id)->where('provider_type','pharmacy')->pluck('category_id')->toArray();





        return view('Admin.CRUDS.pharmacy.parts.edit', compact('row','categories','categoriesIdes'));

    }

    public function update(PharmacyRequest $request, $id )
    {

        $row=Pharmacy::findOrFail($id);
        $data = $request->validationData();
        unset($data['category_id']);

        if ($request->image)
            $data["image"] = $this->uploadFiles('pharmacies', $request->file('image'), $row->image);

        $row->update($data);

        if ($request->category_id) {
            ProviderCategory::where('provider_id',$id)->where('provider_type','pharmacy')->whereNotIn('category_id',$request->category_id)->delete();

            foreach ($request->category_id as $category_id) {
                ProviderCategory::updateOrCreate([
                    'category_id' => $category_id,
                    'provider_id' => $id,
                    'provider_type'=>'pharmacy',
                ]);
            }
        }
        else{
            ProviderCategory::where('provider_id',$id)->where('provider_type','pharmacy')->delete();
        }
        return $this->updateResponse();

    }


    public function destroy($id)
    {
        $row = Pharmacy::findOrFail($id);

        if (file_exists($row->image)) {
            unlink($row->image);
        }

        $row->delete();

        return $this->deleteResponse();
    }//end fun


    public function export()
    {
        return Excel::download(new PharmacyExport, 'pharmacies.xlsx');
    }

    public function import(){
        return view('Admin.CRUDS.pharmacy.parts.import');
    }

    public function update_import(Request $request){

        $file = $request->file('file');

        Excel::import(new PharmacyImport, $request->file('file'));

        return $this->addResponse();
    }
}
