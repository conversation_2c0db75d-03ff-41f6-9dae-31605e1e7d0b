<?php

namespace Database\Seeders;

use App\Models\Specialization;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class SpecializationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $specializations =
            [
                [
                    'id' => '1',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"رمد","en":"ophthalmology"}',
                    'color' => '#b0ec09',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.03 (3).jpeg_1725786830.jpeg',
                    'image' => 'specializations/jpeg_WhatsApp Image 2024-01-03 at 5.15.20 PM.jpeg_1707748888.jpeg',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '3',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جراحة عامة","en":"General Surgery"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.02.jpeg_1725786813.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '4',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"اسنان","en":"Dentist"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.02 (2).jpeg_1725786792.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '5',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"الطب النفسي","en":"Psychiatry"}',
                    'color' => '#070b0d',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.11.59 (1).jpeg_1725786769.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '6',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"علاج طبيعي","en":"Physiotherapy"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.01.jpeg_1725786715.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '7',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"نساء وتوليد","en":"obstatric and Gynecology"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-08-29 at 15.10.16 (1).jpeg_1725786691.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '8',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جراحة عظام","en":"Orthopedic surgery"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.00 (4).jpeg_1725786672.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '10',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"انف واذن وحنجرة","en":"E.N.T"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.03 (2).jpeg_1725786564.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '11',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"قلب واوعية دموية","en":"cardiology"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.11.24 (1).jpeg_1725786151.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '12',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"امراض الدم","en":"hematology"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.11.58.jpeg_1725786529.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '14',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جراحة اطفال","en":"pediatric surgery"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.00 (3).jpeg_1723621705.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '15',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جراحة تجميل","en":"Plastic surgery"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.04 (1).jpeg_1725786357.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '16',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"حساسية ومناعة","en":"Allergy and Immunology"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.11.23.jpeg_1725786322.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '17',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"طب مسنين","en":"Elderly Medicine"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.01 (4).jpeg_1725786303.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '18',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"مسالك بولية","en":"Urology"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.04.jpeg_1725786285.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '19',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"مخ واعصاب","en":"neurology"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.00 (1).jpeg_1725786266.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '21',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جراحة سمنة","en":"Obesity surgery"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.00 (2).jpeg_1725786249.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '22',
                    'relative_id' => NULL,
                    'parent_id' => '3',
                    'name' => '{"ar":"جراحة مناظير والاوعية والسمنة","en":"surgy"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '23',
                    'relative_id' => NULL,
                    'parent_id' => '17',
                    'name' => '{"ar":"نسا","en":"gynecology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '24',
                    'relative_id' => NULL,
                    'parent_id' => '17',
                    'name' => '{"ar":"عظام","en":"orthopedic"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '26',
                    'relative_id' => NULL,
                    'parent_id' => '17',
                    'name' => '{"ar":"علاج طبيعي","en":"physical therapy"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '28',
                    'relative_id' => NULL,
                    'parent_id' => '8',
                    'name' => '{"ar":"0","en":"0"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '30',
                    'relative_id' => NULL,
                    'parent_id' => '29',
                    'name' => '{"ar":"جهاز هضمي وسكر","en":"Gastroenterologist and Diabetologist"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '32',
                    'relative_id' => NULL,
                    'parent_id' => '31',
                    'name' => '{"ar":"علاج اورام","en":"Oncology treatment"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '33',
                    'relative_id' => NULL,
                    'parent_id' => '7',
                    'name' => '{"ar":"نساء وتوليد","en":"obstatric and Gynecology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '34',
                    'relative_id' => NULL,
                    'parent_id' => '1',
                    'name' => '{"ar":"طب وجراحة العيون","en":"ophthalmology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '35',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جلدية","en":"Dermatology"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.11.59 (2).jpeg_1725786201.jpeg',
                    'image' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.11.59 (2).jpeg_1723621212jpeg',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '36',
                    'relative_id' => NULL,
                    'parent_id' => '35',
                    'name' => '{"ar":"جلدية وتناسلية","en":"Dermatology and Venereology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '37',
                    'relative_id' => NULL,
                    'parent_id' => '2',
                    'name' => '{"ar":"جهاز هضمى","en":"Gastroenterologist"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '38',
                    'relative_id' => NULL,
                    'parent_id' => '2',
                    'name' => '{"ar":"كبد","en":"Hepatology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '39',
                    'relative_id' => NULL,
                    'parent_id' => '2',
                    'name' => '{"ar":"كلى","en":"Nephrologist"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '40',
                    'relative_id' => NULL,
                    'parent_id' => '2',
                    'name' => '{"ar":"غدد صماء","en":"Endocrinologist"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '41',
                    'relative_id' => NULL,
                    'parent_id' => '2',
                    'name' => '{"ar":"امراض دم","en":"Hematologist"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '42',
                    'relative_id' => NULL,
                    'parent_id' => '2',
                    'name' => '{"ar":"اورام","en":"Oncologist"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '43',
                    'relative_id' => NULL,
                    'parent_id' => '11',
                    'name' => '{"ar":"0","en":"0"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '44',
                    'relative_id' => NULL,
                    'parent_id' => '9',
                    'name' => '{"ar":"صحة نفسية وعلاج امان","en":"addication psychiatrist"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '45',
                    'relative_id' => NULL,
                    'parent_id' => '5',
                    'name' => '{"ar":"نفسية وعلاج ادمان","en":"psychiatry and addiction cure"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '46',
                    'relative_id' => NULL,
                    'parent_id' => '10',
                    'name' => '{"ar":"0","en":"0"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '48',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جراحة مخ واعصاب","en":"Neuro-surgery"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.11.24.jpeg_1725786901.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '49',
                    'relative_id' => NULL,
                    'parent_id' => '3',
                    'name' => '{"ar":"جراحة اورام","en":"Oncology surgery"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '51',
                    'relative_id' => NULL,
                    'parent_id' => '11',
                    'name' => '{"ar":"قلب وقسطرة","en":"Cardiology and Intervention"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '52',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"طب التخاطب","en":"Phoniatrics"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '53',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"باطنة عامه","en":"internal medicine"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-05-04 at 14.12.04 (2).jpeg_1725786989.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '54',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"تغذية علاجية","en":"Nutrition Therapy"}',
                    'color' => '#000000',
                    'icon' => 'specializations/jpeg_WhatsApp Image 2021-08-29 at 15.10.16.jpeg_1725786951.jpeg',
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '55',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"أمراض روماتزمية","en":"Rheumatology"}',
                    'color' => '#b0ec09',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '56',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"امراض صدرية","en":"Chest"}',
                    'color' => '#b0ec09',
                    'icon' => '',
                    'image' => '',
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '57',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"اطفال","en":"Pediatrics"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '58',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"مخ واعصاب اطفال","en":"pediatric neurology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '59',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"قلب اطفال","en":"pediatric cardiology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '60',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"اطفال","en":"Pediatrics"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '61',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"سكر و غدد صماء اطفال","en":"pediatric endocrinology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '62',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"كبد وجهاز هضمى ومناظير اطفال","en":"pediatric Gastroenterologist"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '63',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"كلى اطفال","en":"pediatric nephrology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '64',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"مسالك بولية اطفال","en":"pediatric urology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '65',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"حساسية ومناعة اطفال","en":"pediatric immunology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '66',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"امراض دم اطفال","en":"pediatric haematology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '67',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"روماتيزم اطفال","en":"pediatric rheumatology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '68',
                    'relative_id' => NULL,
                    'parent_id' => '57',
                    'name' => '{"ar":"جراحة اطفال","en":"pediatric surgery"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '69',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"سمعيات","en":"Audiology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '70',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جراحه اوعيه دمويه","en":"vascular surgery"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '71',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جراحه قلب وصدر","en":"Cardiothoracic surgery"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '72',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"ذكورة و عقم","en":"Andrology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '73',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"علاج ألم","en":"Pain therapy"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '74',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"اورام","en":"Oncology"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ],
                [
                    'id' => '75',
                    'relative_id' => NULL,
                    'parent_id' => NULL,
                    'name' => '{"ar":"جراحة اورام","en":"Oncology surgery"}',
                    'color' => '#000000',
                    'icon' => NULL,
                    'image' => NULL,
                    'created_at' => now(),
                    'updated_at' => now()
                ]
            ];
            Schema::disableForeignKeyConstraints();
            Specialization::truncate();
            Schema::enableForeignKeyConstraints();
            Specialization::insert($specializations);
    }
}
