<?php

namespace App\Http\Controllers\Api\V2;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Http\Resources\ProviderBranchResource;
use App\Http\Resources\ProviderCategoryResource;
use App\Http\Resources\ProviderRequestResource;
use App\Http\Resources\ProviderResource;
use App\Http\Resources\ProviderServiceResource;
use App\Http\Resources\ProviderTimeResource;
use App\Http\Resources\ServiceResource;
use App\Http\Traits\Api_Trait;
use App\Imports\AnalysisImport;
use App\Imports\AreaImport;
use App\Imports\MedicalCenterImport;
use App\Imports\OpticalCenterImport;
use App\Imports\ProviderBranchImport;
use App\Imports\ProviderImport;
use App\Models\Provider;
use App\Models\ProviderBranch;
use App\Models\ProviderBranchDoctor;
use App\Models\ProviderCategory;
use App\Models\ProviderRequest;
use App\Models\ProviderRequestItem;
use App\Models\ProviderService;
use App\Models\ProviderTime;
use App\Models\Service;
use App\Services\Providers\GetServices;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Spatie\MediaLibrary\MediaCollections\Models\Media;

class ProviderController extends Controller
{
    use Api_Trait;

    private $is_subscribed;

    public function __construct()
    {
        $this->is_subscribed = (auth('patient')->user()?->subscribes()->exists()) ?? false;
    }


    public function provider_categories()
    {
        $categories = ProviderCategory::select('id', 'name')->get();
        return $this->returnData(ProviderCategoryResource::collection($categories), [helperTrans('api.Service Categories Data')], 200);
    }
    public function main_providers(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'provider_category_id' => 'required|exists:provider_categories,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $providers = Provider::where('provider_category_id', $request->provider_category_id);
        if ($request->ll && $request->lg) {
            $lat = $request->ll;
            $lng = $request->lg;
            $providers->selectRaw("
                providers.*,
                (
                    CASE
                        WHEN latitude IS NOT NULL AND longitude IS NOT NULL THEN (
                            6371 * acos(
                                cos(radians(?)) *
                                cos(radians(latitude)) *
                                cos(radians(longitude) - radians(?)) +
                                sin(radians(?)) *
                                sin(radians(latitude))
                            )
                        )
                        ELSE NULL
                    END
                ) as distance
            ", [$lat, $lng, $lat]);

            // Sort by distance (NULLs last)
            $providers->orderByRaw('distance IS NULL') // FALSE (0) comes first (valid coords), TRUE (1) last
                ->orderBy('distance');
        }
        $providers = $providers->get();

        return $this->returnData(ProviderResource::collection($providers), [helperTrans('api.Providers Data')], 200);
    }
    public function provider_details(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'provider_id' => 'required|exists:providers,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $provider = Provider::where('id', $request->provider_id)->first();
        // throw new DoctoriaException($providers);

        return $this->returnData(ProviderResource::make($provider), [helperTrans('api.Provider Data')], 200);
    }

    public function main_provider_branches(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'main_provider_id'   => 'required|exists:providers,id',
            ],
            []
        );

        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $provider = Provider::find($request->main_provider_id);

        $branches = collect([]);
        if ($request->ll && $request->lg) {
            $lat = $request->ll;
            $lng = $request->lg;
            $branches = $provider->branches()
                ->selectRaw("
            provider_branches.*,
            CASE
                WHEN latitude IS NOT NULL AND longitude IS NOT NULL THEN (
                    6371 * acos(
                        cos(radians(?)) *
                        cos(radians(latitude)) *
                        cos(radians(longitude) - radians(?)) +
                        sin(radians(?)) *
                        sin(radians(latitude))
                    )
                )
                ELSE NULL
            END AS distance
        ", [$lat, $lng, $lat])
                ->orderByRaw("CASE
            WHEN latitude IS NULL OR longitude IS NULL THEN 1
            ELSE 0
        END")
                ->orderBy('distance')
                ->get();
        } else {
            $branches = $provider->branches;
        }
        return $this->returnData(ProviderBranchResource::collection($branches), [helperTrans('api.Branches Data')], 200);
    }

    public function main_provider_services(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'branch_id' => 'nullable|exists:provider_branches,id',
                'provider_id' => 'nullable|exists:providers,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }
        $getServices = new GetServices();
        $services = [];
        if ($request->branch_id) {
            $services = $getServices->getBranchServices($request->branch_id);
        } else if ($request->provider_id) {
            $services = $getServices->getProviderServices($request->provider_id);
        }
        return $this->returnData($services, [helperTrans('api.Services Data')], 200);
    }
    public function provider_category_services(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'provider_category_id' => 'required|exists:provider_categories,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $provider_category = ProviderCategory::find($request->provider_category_id);

        $services = Service::where('service_category_id', $request->provider_category_id)->get();

        return $this->returnData(ServiceResource::collection($services), [helperTrans('api.Services Data')], 200);
    }

    public function main_provider_by_services(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'services' => 'required|array',
                'services.*' => 'required|exists:services,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $providerservices = ProviderService::whereIn('service_id', $request->services)->get();

        $result = [];

        // foreach ($providerservices as $providerservice) {


        //     $result[$providerservice->provider_id] = [
        //         'id' => $providerservice->provider_id,
        //         'provider' => ProviderResource::make($providerservice->provider),
        //         'services' => []
        //     ];

        //     $result[$providerservice->provider_id]['services'][] = [
        //         'id' => $providerservice->service_id,
        //         'name' => $providerservice->service->name,
        //         'price' => $providerservice->price
        //     ];
        // }
        foreach ($providerservices as $providerservice) {
            if (!$providerservice->provider || !$providerservice->service) {
                continue; // Skip this iteration if provider or service is null
            }

            if (!isset($result[$providerservice->provider_id])) {
                $result[$providerservice->provider_id] = [
                    'id' => $providerservice->provider_id,
                    'provider' => new ProviderResource($providerservice->provider),
                    'services' => []
                ];
            }
            $price = $providerservice->price ?? 0;
            $discount = $providerservice->discount ?? 0;
            $net_price = $providerservice->price - ($providerservice->discount / 100 * $providerservice->price);
            $result[$providerservice->provider_id]['services'][] = [
                'id' => $providerservice->service_id,
                'name' => $providerservice->service->name,
                'common_name' => $providerservice->service->common_name,
                'pricing' => [
                    'gross_price' => number_format($price, 2),
                    'discount' => number_format($discount, 2),
                    'net_price' => number_format($net_price, 2),
                    'is_subscribed' => $this->is_subscribed,
                ],
                'is_subscribed' => $this->is_subscribed,
                'image' => $providerservice->image,
            ];
        }


        $result = array_values($result);


        return $this->returnData($result, [helperTrans('api.Providers Data')], 200);
    }


    public function available_provider_time($branch_id, $date)
    {
        $validator = Validator::make(
            [
                'branch_id' => $branch_id,
                'date' => $date
            ],
            [
                'branch_id' => 'required|exists:provider_branches,id',
                'date' => 'required|date',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $day_en_order = Carbon::parse($date)->dayOfWeek;
        $day_orders = [
            0 => 2,
            1 => 3,
            2 => 4,
            3 => 5,
            4 => 6,
            5 => 7,
            6 => 1
        ];

        $date_day = $day_orders[$day_en_order];

        $provider_times = ProviderTime::select(['from_time', 'to_time'])->where([
            'provider_type' => 'provider_branch',
            'provider_id'   => $branch_id,
            'day_id'        =>  $date_day
        ])->first();

        if (!$provider_times) {
            return $this->returnErrorNotFound(helperTrans('not found branch dates'));
        }

        $booking_times = ProviderRequest::whereNotIn('status', ['complete', 'cancel'])->select(['time'])->where([
            'provider_branch_id' => $branch_id,
            'date'      => $date
        ])->pluck('time')->map(function ($item) {
            return Carbon::parse($item)->format('H:i');
        })->toArray();

        $available_time = [];

        $start_time = Carbon::parse($provider_times['from_time']);
        $end_time =  Carbon::parse($provider_times['to_time']);
        $time = $start_time;

        while (true) {
            if ($time->addMinutes(30)->lessThanOrEqualTo($end_time)) {
                $available_time[] = $time->subMinutes(30)->format('H:i');
                $time->addMinutes(30);
            } else {
                break;
            }
        }
        return $this->returnData(array_values(array_diff($available_time, $booking_times)), '');
    }

    public function provider_requests(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'status' => 'in:not_paid,pending,active,complete,cancel',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();
        $patient_requests = ProviderRequest::with(['provider', 'provider_branch', 'items'])->where('patient_id', $patient->id);

        $provider_requests = $patient_requests->where('status', $request->status)->latest()->get();


        return $this->returnData(ProviderRequestResource::collection($provider_requests), [helperTrans('api.Provider Requests Data')], 200);
    }


    public function make_provider_booking(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'provider_id' => 'nullable|exists:providers,id',
                'branch_id' => 'nullable|exists:provider_branches,id',
                'doctor_id' => 'nullable|exists:doctors,id',
                'pay_type' => 'required|in:cash,paid_online',
                'services' => 'nullable|array',
                'services.*.id' => 'required|exists:services,id',
                'services.*.quantity' => 'required|integer|min:1',
                'phone' => 'nullable',
                'governorate_id' => 'nullable|exists:governorates,id',
                'city_id' => 'nullable|exists:cities,id',
                'location' => 'nullable',
                'longitude' => 'nullable',
                'latitude' => 'nullable',
                'notes' => 'nullable',
                'date' => 'nullable|date',
                'time' => 'nullable',
            ]

        );

        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        /** @var Patient $patient */
        $patient = auth('patient')->user();

        $provider_request = ProviderRequest::create([
            'id'   => getNextSharedId(),
            'patient_id' => $patient->id,
            'provider_id' => $request->provider_id,
            'provider_branch_id' => $request->branch_id,
            'doctor_id' => $request->doctor_id,
            'relative_id' => $request->relative_id,
            'pay_type' => $request->pay_type,
            'status' => 'pending',
            'date' => $request->date ?? null,
            'time' => $request->time ?? null,
            'phone' => $request->phone ?? null,
            'governorate_id' => $request->governorate_id ?? null,
            'city_id' => $request->city_id ?? null,
            'location' => $request->location ?? null,
            'longitude' => $request->longitude ?? null,
            'latitude' => $request->latitude ?? null,
            'notes' => $request->notes ?? null,
            'show_for_provider' => isset($request->show_for_provider) ? $request->show_for_provider : true,
        ]);
        $total_price = 0;
        ##todo : ckeck if patient has package
        if ($request->services) {

            foreach ($request->services as $service) {

                $price = ProviderService::where([
                    'provider_id' => $request->provider_id,
                    'service_id' => $service['id'],
                ])->first()?->price;

                ProviderRequestItem::create([
                    'provider_request_id' => $provider_request->id,
                    'service_id' => $service['id'],
                    'quantity' => $service['quantity'],
                    'price' => ($price * (int)$service['quantity']),
                ]);
                $total_price += $price * (int)$service['quantity'];
            }
        } else if ($request->doctor_id) {

            $price = ProviderBranchDoctor::where([
                'provider_branch_id' => $request->branch_id,
                'doctor_id' => $request->doctor_id,
            ])->first()?->price;

            $total_price = $price;
        }
        if ($provider_request->provider && $patient->subscribes()->exists()) {
            $total_price = $total_price - ($total_price * ($provider_request->provider?->discount / 100));
        }

        $provider_request->total_price = round($total_price, 2) ?? 0;
        $provider_request->save();


        if ($request['files_ids'] && count($request['files_ids']) > 0) {
            foreach ($request['files_ids'] as $file_id) {
                $media = Media::find($file_id);
                if ($media) {
                    $media->model_type = ProviderRequest::class;
                    $media->model_id = $provider_request->id;
                    $media->save();
                }
            }
        }
        run_push_notification($provider_request->patient->id, 'patient', [
            'title' => 'Booking Created',
            'body' => 'Booking has been created successfully',
            'type' => 'booking_status',
        ]);
        if ($provider_request->provider) {
            if ($provider_request->show_for_provider) {
                run_push_notification($provider_request->provider->id, 'provider', [
                    'title' => 'New Booking',
                    'body' => 'You have a new booking',
                    'type' => 'booking_status',
                ]);
            }
        }
        return $this->returnData(ProviderRequestResource::make($provider_request), [helperTrans('api.Booking Done')], 200);
    }

    public function cancel_provider_request(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'id' => 'required|exists:provider_requests,id',
                'reason' => 'nullable',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();
        $provider_request = ProviderRequest::where('id', $request->id)->where('patient_id', $patient->id)->first();

        if (!$provider_request) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $provider_request->update([
            'status' => 'cancel',
            'cancel_reason' => $request->reason
        ]);

        run_push_notification($provider_request->patient->id, 'patient', [
            'title' => 'Booking Cancel',
            'body' => 'Booking has been cancelled successfully',
            'type' => 'booking_status',
        ]);
        if ($provider_request->provider) {
            if ($provider_request->show_for_provider) {
                run_push_notification($provider_request->provider->id, 'provider', [
                    'title' => 'Booking Cancel',
                    'body' => 'Have a booking cancel',
                    'type' => 'booking_status',
                ]);
            }
        }
        return $this->returnData(ProviderRequestResource::make($provider_request), [helperTrans('api.Response added successfully')]);
    }

    public function import_providers(Request $request)
    {


        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        Excel::import(new ProviderImport($request->provider_category_id), $request->file('file'));

        return back()->with('success', 'Data imported successfully into multiple tables.');
    }

    public function import_provider_branches(Request $request)
    {


        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        Excel::import(new ProviderBranchImport($request->provider_category_id), $request->file('file'));

        return back()->with('success', 'Data imported successfully into multiple tables.');
    }

    public function import_areas(Request $request)
    {


        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        Excel::import(new AreaImport, $request->file('file'));

        return back()->with('success', 'Data imported successfully into multiple tables.');
    }
    public function import_data(Request $request)
    {


        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        Excel::import(new AnalysisImport, $request->file('file'));

        return back()->with('success', 'Data imported successfully into multiple tables.');
    }
    public function add_times()
    {
        $provider_branches = ProviderBranch::where('provider_id', '>=', 101)->get();
        foreach ($provider_branches as $branch) {
            for ($i = 1; $i <= 7; $i++) {

                if ($i == 7) {
                    ProviderTime::create([
                        "provider_id" => $branch->id,
                        "day_id" => $i,
                        "provider_type" => "provider_branch",
                        "type" => "offline",
                        "from_time" => "10:00:00",
                        "to_time" => "24:00:00"
                    ]);
                } else {

                    ProviderTime::create([
                        "provider_id" => $branch->id,
                        "day_id" => $i,
                        "provider_type" => "provider_branch",
                        "type" => "offline",
                        "from_time" => "09:00:00",
                        "to_time" => "24:00:00"
                    ]);
                }
            }
        }
    }
    public function assign_services_to_providers()
    {
        $providers = Provider::where('provider_category_id', 1)->get();
        foreach ($providers as $provider) {
            $services = Service::where('service_category_id', 2)->get();
            foreach ($services as $service) {
                ProviderService::updateOrCreate([
                    'provider_id' => $provider->id,
                    'service_id' => $service->id,
                    'price' => 1
                ], []);
            }
        }
        return $this->returnSuccess('done');
    }
    public function provider_branch_doctors($branch_id, $specialization_id)
    {
        $validator = Validator::make(
            [
                'branch_id' => $branch_id,
                'specialization_id' => $specialization_id
            ],
            [
                'branch_id' => 'required|exists:provider_branches,id',
                'specialization_id' => 'required|exists:specializations,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }


        $lang = request()->header('lang', 'en'); // fallback to 'en' if not sent
        $data = ProviderBranchDoctor::where('provider_branch_id', $branch_id)
            ->with(['providerBranch.provider', 'doctor.doctor_level'])
            ->whereHas('doctor', function ($query) use ($specialization_id) {
                $query->where('specialization_id', $specialization_id);
            })
            ->get()
            ->map(function ($branch_doctor) use ($branch_id, $lang) {
                $doctor = $branch_doctor->doctor;
                $price = $branch_doctor->price ?? 0;
                $discount = $branch_doctor->providerBranch?->provider?->discount ?? 0;
                $net_price = $price - ($discount / 100 * $price);
                // throw new DoctoriaException($$doctor?->doctor_level);
                return [
                    'doctor_id' => $branch_doctor->doctor_id,
                    'doctor_name' => $doctor?->getTranslation('nickname', $lang),
                    'doctor_level' => $doctor?->doctor_level?->getTranslation('name', $lang),
                    'image' => new_get_file($doctor->image ?? null, 'person'),
                    'pricing' => [
                        'gross_price' => number_format($price, 2),
                        'discount' => number_format($discount, 2),
                        'net_price' => number_format($net_price, 2),
                        'is_subscribed' => $this->is_subscribed,
                    ],
                    'times' => $doctor
                        ? ProviderTimeResource::collection($doctor->branch_times($branch_id))
                        : [],
                ];
            });

        return $this->returnData($data, [helperTrans('api.doctors data')], 200);
    }

    public function available_provider_doctor_time($branch_id, $doctor_id, $date)
    {
        $validator = Validator::make(
            [
                'branch_id' => $branch_id,
                'doctor_id' => $doctor_id,
                'date' => $date
            ],
            [
                'branch_id' => 'required|exists:provider_branches,id',
                'doctor_id' => 'required|exists:doctors,id',
                'date' => 'required|date',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $day_en_order = Carbon::parse($date)->dayOfWeek;
        $day_orders = [
            0 => 2,
            1 => 3,
            2 => 4,
            3 => 5,
            4 => 6,
            5 => 7,
            6 => 1
        ];

        $date_day = $day_orders[$day_en_order];

        $provider_branch_doctor = ProviderBranchDoctor::where([
            'provider_branch_id' => $branch_id,
            'doctor_id' => $doctor_id
        ])->first();

        if (!$provider_branch_doctor) {
            return $this->returnErrorNotFound(helperTrans('not found branch doctor'));
        }

        $provider_times = ProviderTime::select(['from_time', 'to_time'])->where([
            'provider_type' => 'provider_branch_doctor',
            'provider_id'   => $provider_branch_doctor->id,
            'day_id'        =>  $date_day
        ])->first();

        if (!$provider_times) {
            return $this->returnErrorNotFound(helperTrans('not found branch dates'));
        }

        $booking_times = ProviderRequest::whereNotIn('status', ['complete', 'cancel'])->select(['time'])->where([
            'provider_branch_id' => $branch_id,
            'doctor_id'      => $doctor_id,
            'date'      => $date
        ])->pluck('time')->map(function ($item) {
            return Carbon::parse($item)->format('H:i');
        })->toArray();

        $available_time = [];

        $start_time = Carbon::parse($provider_times['from_time']);
        $end_time =  Carbon::parse($provider_times['to_time']);
        $time = $start_time;

        while (true) {
            if ($time->addMinutes(30)->lessThanOrEqualTo($end_time)) {
                $available_time[] = $time->subMinutes(30)->format('H:i');
                $time->addMinutes(30);
            } else {
                break;
            }
        }
        return $this->returnData(array_values(array_diff($available_time, $booking_times)), '');
    }
    public function provider_specializations($branch_id)
    {
        $validator = Validator::make(
            [
                'branch_id' => $branch_id,
            ],
            [
                'branch_id' => 'required|exists:provider_branches,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $specializations = ProviderBranchDoctor::where('provider_branch_id', $branch_id)->has('doctor.specialization')
            ->with('doctor.specialization')->get()->pluck('doctor.specialization')->unique('id')->values()->map(function ($specialization) {
                return  [
                    'id' => (int)$specialization->id,
                    'name' => $specialization->getTranslation('name', session_lang()),
                    'color' => $specialization->color,
                    'icon' => get_file($specialization->icon),
                    'image' => get_file($specialization->icon), // until mobile team correct it
                ];
            });



        return $this->returnData($specializations, [helperTrans('api.specializations data')], 200);
    }

    public function import_medical_center(Request $request)
    {


        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        Excel::import(new MedicalCenterImport(), $request->file('file'));

        return back()->with('success', 'Data imported successfully into multiple tables.');
    }
    public function import_optical_center(Request $request)
    {


        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        Excel::import(new OpticalCenterImport(), $request->file('file'));

        return back()->with('success', 'Data imported successfully into multiple tables.');
    }
}
