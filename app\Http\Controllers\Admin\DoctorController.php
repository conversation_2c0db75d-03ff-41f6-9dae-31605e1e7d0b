<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\DoctorRequest;
use App\Http\Requests\Admin\ProviderTimeRequest;
use App\Http\Traits\ResponseTrait;
use App\Http\Traits\Upload_Files;
use App\Models\Category;
use App\Models\City;
use App\Models\Day;
use App\Models\Doctor;
use App\Models\DoctorUpdate;
use App\Models\DoctorCategory;
use App\Models\Delegate;
use App\Models\Experience;
use App\Models\Governorate;
use App\Models\ProviderCategory;
use App\Models\ProviderTime;
use App\Models\Specialization;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;
use App\Models\DoctorContract;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Admin\PharmacyController;
use App\Http\Controllers\Admin\RadiologyCenterController;
use App\Http\Requests\Admin\PharmacyRequest;
use App\Http\Requests\Admin\LaboratoryRequest;
use App\Http\Requests\Admin\HospitalRequest;
use App\Http\Requests\Admin\RadiologyCenterRequest;

use App\Http\Controllers\Admin\Str;
use App\Http\Requests\Admin\UpdateDoctorRequest;

class DoctorController extends Controller
{
    use  ResponseTrait, Upload_Files;

    public function __construct(
        private PharmacyController $pharmacyController,
        private LaboratoryController $laboratoryController,
        private HospitalController $hospitalController,
        private RadiologyCenterController $radiologyCenterController,
        private SpecializCenterController $specializCenterController,
        private PolyCenterController $polyEnterController



    ) {}

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $admins = Doctor::with('latestContract')->get();
            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {
                    $edit = '';
                    $delete = '';
                    return '
                            <a href="' . route('doctors.edit', $admin->id) . '" ' . $edit . '  class="btn rounded-pill btn-primary waves-effect waves-light"
                                    data-id="' . $admin->id . '"
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-edit"></i>
                                </span>
                            </span>
                            </a>
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $admin->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                            <a href="' . route('admin.doctors_branches.index', $admin->id) . '" class="btn rounded-pill btn-info waves-effect waves-light">Branches</a>
                    ';
                })
                ->editColumn('nickname', function ($row) {
                    if (is_json($row->nickname)) {
                        $nameData = json_decode($row->nickname, true);
                        $nameAr = $nameData['ar'] ?? 'N/A';
                        $nameEn = $nameData['en'] ?? 'N/A';
                        return '<span>' . htmlspecialchars($nameAr) . '</span> / <span>' . htmlspecialchars($nameEn) . '</span>';
                    }
                    if (is_string($row->nickname)) {
                        return htmlspecialchars($row->nickname);
                    }
                })->editColumn('name', function ($row) {
                    if (is_json($row->name)) {
                        $nameData = json_decode($row->name, true);
                        $nameAr = $nameData['ar'] ?? 'N/A';
                        $nameEn = $nameData['en'] ?? 'N/A';
                        return '<span>' . htmlspecialchars($nameAr) . '</span> / <span>' . htmlspecialchars($nameEn) . '</span>';
                    }
                    if (is_string($row->name)) {
                        return htmlspecialchars($row->name);
                    }
                })


                ->addColumn('working_hours', function ($doctor) {
                    $workHours = $doctor->getWorkHours();
                    //dd($workHours);
                    if ($workHours) {
                        $formattedHours = '';
                        foreach ($workHours as $day => $times) {
                            $formattedHours .= ucfirst($day) . ': ' . $times['start_time'] . ' - ' . $times['end_time'] . '<br>';
                        }
                        return $formattedHours;
                    }
                    return 'لا توجد أوقات عمل';
                })
                ->editColumn('specialization_id', function ($row) {
                    return $row->specialization->name ?? '';
                })->editColumn('sub_specialization_id', function ($row) {
                    return $row->sub_specialization->name ?? '';
                })
                ->addColumn('doctor_times', function ($row) {
                    $route = route('admin.doctor_times', $row->id);
                    return "<a href='$route' class='btn btn-outline-primary'>" . helperTrans('admin.Doctor Time') . "</a>";
                })
                ->addColumn('doctor_contract', function ($row) {
                    $latestContract = $row->contracts()->latest()->first();
                    if ($latestContract) {
                        $route = route('admin.doctor_contract_show', $latestContract->id);
                        return "<a href='$route' class='btn btn-outline-success'>" . helperTrans('admin.Perview') . "</a>";
                    }
                })
                ->setRowClass(function ($row) {
                    $latestContract = $row->contracts()->latest()->first();
                    if ($latestContract && strtotime($latestContract->contract_end_date) < time()) {
                        return 'custom_warning_bg';
                    }
                    return '';
                })
                ->addColumn('update', function ($row) {
                    $doctorUpdate = DoctorUpdate::where('doctor_id', $row->id)->latest()->first();
                    if (isset($doctorUpdate)) {
                        if ($doctorUpdate->status == 'pending') {
                            $route = route('admin.update.doctor', $doctorUpdate->id);
                            return '<a href="' . $route . '" class="btn btn-warning ">Show Update</a>';
                        } elseif ($doctorUpdate->status == 'reject') {
                            return '<span class="btn btn-danger">Reject</span>';
                        } elseif ($doctorUpdate->status == 'approved') {
                            return '<span class="btn btn-success">Approved</span>';
                        }
                    } else {
                        return '<span class="btn btn-info">Not Needed</span>';
                    }
                })
                ->editColumn('status', function ($row) {
                    $active = '';
                    $operation = '';
                    $operation = '';

                    if ($row->status == 1)
                        $active = 'checked';

                    return '<div class="form-check form-switch">
                               <input ' . $operation . '  class="form-check-input activeBtn" data-id="' . $row->id . ' " type="checkbox" role="switch" id="flexSwitchCheckChecked" ' . $active . '  >
                            </div>';
                })
                ->editColumn('image', function ($admin) {
                    return '
                      <a data-fancybox="" href="' . asset(get_file($admin->image)) . '">
                        <img height="60px" src="' . asset(get_file($admin->image)) . '">
                        </a>
                    ';
                })

                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);
        }
        return view('Admin.CRUDS.doctor.index');
    }

    public function create()
    {
        $categories = Category::whereIn('slug', ['doctors', 'visit_doctor', 'consultation'])->get();
        $specializations = Specialization::get();
        $governorates = Governorate::get();
        $experiences = Experience::get();
        $days = Day::get();
        return view('Admin.CRUDS.doctor.parts.create', compact('specializations', 'categories', 'governorates', 'experiences', 'days'));
    }

    public function providerStore($request)
    {
        DB::transaction(function () use ($request) {
            $workingHours = [];
            if ($request->has('working_days')) {
                foreach ($request->working_days as $day) {
                    $workingHours[$day] = [
                        'start_time' => $request->input('start_time')[$day] ?? null,
                        'end_time' => $request->input('end_time')[$day] ?? null,
                    ];
                }
            }
            $exceptedColumn = [
                'work_time',
                "attach_contract",
                "attach_documents",
                "attach_price_list",
                'start_time',
                'end_time',
                'working_days',
                'contract_start_date',
                'contract_end_date',
                'price_list_discount',
                'contract_note',
                'home_care_gross_price',
                'home_care_discount',
                'home_care_net_price',
                'service_clinic_gross_price',
                'service_clinic_discount',
                'service_clinic_net_price',
                'service_online_gross_price',
                'service_online_discount',
                'service_online_net_price',
                'price_list_outpatient',
                'price_list_intpatient',
                "price_list_consultation",
                "price_list_laboratory",
                "price_list_radiologe",
                "price_list_exception",
                "price_list_acutemedicine_local",
                "price_list_chronicmedication_local",
                "price_list_cosmetics_local",
                "price_list_otherservice_local",
                "price_list_acutemedicine_imported",
                "price_list_chronicmedication_imported",
                "price_list_cosmetics_imported",
                "price_list_otherservice_imported",
                "price_list_imported",
                'price_list_hematology',
                'price_list_chemistry',
                'price_list_hormones',
                'price_list_serology',
                'price_list_immunology',
                'price_list_drug',
                'price_list_microbiology',
                'price_list_pathology',
                'price_list_other',
                'name_1',
                'email_1',
                'phone_1',
                'name_2',
                'email_2',
                'phone_2',
                'clamis_due_date',
                'admin_fees',
                'bank_name',
                'bank_account_number',
                'bank_iban',
                'bank_swift_code',
                'bank_branch_bank',
                'bank_mobile_number',
                'e_wallet_name',
                'e_wallet_mobile_number',
                'instapay_mobile_number',
                'instapay_email',
                'cheque_name',
                'working_hours',
                'price_list_xray_radiologyCenter',

            ];
            $data = $request->except($exceptedColumn);
            unset(
                $data['category_id'],
                $data["price_list_hospital_consulation"],
                $data["price_list_hospital_outpatient"],
                $data["price_list_hospital_inpatient"],
                $data["price_list_hospital_laboratory"],
                $data["price_list_hospital_radiology"],
                $data["price_list_hospital_discount_other"],
                $data["price_list_doctor_visit"],
                $data["price_list_nurse_visit"],
                $data["price_list_hospital_other"],
                $data["nickname_hospital_doctor"],
                $data["specialization_hospital_id"],
                $data["sub_specialization_id]"],
                $data["price_list_xray"],
                $data["price_list_mri"],
                $data["price_list_ct"],
                $data["price_list_other"],
                $data["price_list_dental"],
                $data["price_list_glasses"],
                $data["price_list_frame"],
                $data["price_list_sessions"],
                $data['price_list_xray_radiologyCenter'],
                $data['price_list_other_radiologyCenter'],

            );
            if ($request->image) {
                $image = json_decode($request->image, true);
                $imagedata = $image['image'] ?? null;
                $data['image'] =  $imagedata;
            }
            $data['password'] = bcrypt($request->password);
            $data['delegate'] = $request->has('delegate') ? 1 : 0;
            // if (is_array($data['name'])) {
            //     $data['name'] = $data['name'];
            // }
            // if (is_array($data['address'])) {
            //     $data['address'] = json_encode($data['address'], true);
            // }
            // if (is_array($data['about'])) {
            //     $data['about'] = json_encode($data['about'], true);
            // }

            $data["nickname"] = json_encode($data["nickname"], true);
            $data['location'] = json_encode($data['address'], true);

            try {
                $doctor = Doctor::create($data);
                foreach ($workingHours as $key => $workingtime) {
                    $dayId = Day::where('day->en', strtolower($key))->first()->id;
                    ProviderTime::create([
                        'provider_id' => $doctor->id,
                        'provider_type' => 'doctor',
                        'day_id' => $dayId,
                        'from_time' => $workingHours[$day]['start_time'] ?? null,
                        'to_time' => $workingHours[$day]['end_time'] ?? null,
                        'type' => 'online',
                    ]);
                }
            } catch (\Exception $e) {
                dd($e->getMessage());
            }
            $code = "doctor_$doctor->id";
            $doctor->update(['code' => $code]);
            $contractData = [
                'working_hours' => json_encode($workingHours),
                'doctor_id' => $doctor->id,
                'contract_start_date' => $request->contract_start_date,
                'contract_end_date' => $request->contract_end_date,
                'attach_contract' => $request->attach_contract, // if you have a file to upload, handle it similarly to images
                'attach_documents' => $request->attach_documents,
                'attach_price_list' => $request->attach_price_list,
                'home_care_gross_price' => $request->home_care_gross_price,
                'home_care_discount' => $request->home_care_discount,
                'service_home_care_price' => $request->service_home_care_price,
                'home_care_net_price' => $request->home_care_net_price,
                'service_clinic_price' => $request->service_clinic_price,
                'service_clinic_gross_price' => $request->service_clinic_gross_price,
                'service_clinic_discount' => $request->service_clinic_discount,
                'service_clinic_net_price' => $request->service_clinic_net_price,
                'service_online_gross_price' => $request->service_online_gross_price,
                'service_online_discount' => $request->service_online_discount,
                'service_online_net_price' => $request->service_online_net_price,
                'price_list_outpatient' => $request->price_list_outpatient,
                'price_list_intpatient' => $request->price_list_intpatient,
                'contract_note' => $request->contract_note,
                'clamis_due_date' => $request->clamis_due_date,
                'admin_fees' => $request->admin_fees,
                'bank_name' => $request->bank_name,
                'bank_account_number' => $request->bank_account_number,
                'bank_iban' => $request->bank_iban,
                'bank_swift_code' => $request->bank_swift_code,
                'bank_branch_bank' => $request->bank_branch_bank,
                'bank_mobile_number' => $request->bank_mobile_number,
                'e_wallet_name' => $request->e_wallet_name,
                'e_wallet_mobile_number' => $request->e_wallet_mobile_number,
                'instapay_mobile_number' => $request->instapay_mobile_number,
                'instapay_email' => $request->instapay_email,
                'cheque_name' => $request->cheque_name,
            ];
            if ($request->attach_contract) {
                $attach_contractJson = $request->input('attach_contract');
                $attach_contractData = json_decode($attach_contractJson, true);
                $attach_contractPath = $attach_contractData['path'] ?? null;
                $contractData['attach_contract'] = $attach_contractPath;
            } else if ($request->attach_documents) {
                $attach_documentsJson = $request->input('attach_documents');
                $attach_documentsData = json_decode($attach_documentsJson, true);
                $attach_documentsPath = $attach_documentsData['path'] ?? null;
                $contractData['attach_documents'] = $attach_documentsPath;
            } else if ($request->attach_price_list) {
                $attach_price_listJson = $request->input('attach_price_list');
                $attach_price_listData = json_decode($attach_price_listJson, true);
                $attach_price_listPath = $attach_price_listData['path'] ?? null;
                $contractData['attach_price_list'] = $attach_price_listPath;
            }
            DoctorContract::create($contractData);
            // if ($request->category_id) {
            //     foreach ($request->category_id as $category_id) {
            //         ProviderCategory::create([
            //             'provider_id' => $doctor->id,
            //             'category_id' => $category_id,
            //             'provider_type' => 'doctor',
            //         ]);
            //     }
            // }
            if ($request->has('delegate')) {
                Delegate::create([
                    'doctor_id' => $doctor->id,
                    'name_1' => $request->name_1,
                    'email_1' => $request->email_1,
                    'phone_1' => $request->phone_1,
                    'name_2' => $request->name_2,
                    'email_2' => $request->email_2,
                    'phone_2' => $request->phone_2,
                ]);
            }
        });
        return redirect()->url('admin/doctors')->with('success', 'created success');
    }

    public function store(Request $request)
    {
        if ($request->provider_type == 'doctor') {
            $this->providerStore($request);
            return redirect()->route('doctors.index')->with('success', 'created success');
        } else if ($request->provider_type == 'pharmacie') {
            $pharmacyRequest = PharmacyRequest::createFrom($request);
            $this->pharmacyController->store($pharmacyRequest);
            return redirect()->route('pharmacies.index')->with('success', 'created success');
        } else if ($request->provider_type == 'lab') {
            $labRequest = LaboratoryRequest::createFrom($request);
            $this->laboratoryController->store($labRequest);
            return redirect()->route('laboratories.index')->with('success', 'created success');
        } else if ($request->provider_type == 'hospital') {
            $hospitalRequest = HospitalRequest::createFrom($request);
            $this->hospitalController->store($hospitalRequest);
            return redirect()->route('hospitals.index')->with('success', 'created success');
        } else if ($request->provider_type == 'radiologyCenter') {
            $radiologyCenterRequest = RadiologyCenterRequest::createFrom($request);
            $this->radiologyCenterController->store($radiologyCenterRequest);
            return redirect()->route('radiology_centers.index')->with('success', 'created success');
        } else if ($request->provider_type == 'specializCenter') {
            $specializeCenterRequest = HospitalRequest::createFrom($request);
            $this->specializCenterController->store($specializeCenterRequest);
            return redirect()->route('specializecenter.index')->with('success', 'created success');
        } else if ($request->provider_type == 'polyCenter') {
            $polyCenterRequest = HospitalRequest::createFrom($request);
            $this->polyEnterController->store($polyCenterRequest);
            return redirect()->route('polycenter.index')->with('success', 'created success');
        }
    }


    public function show($id)
    {
        //
    }


    public function edit($id)
    {
        $row = Doctor::findOrFail($id);
        if (is_json($row->name)) {
            $nameData = json_decode($row->name, true);
            $row->name = $nameData['ar'] ?? $nameData['en'];
        }

        $specializations = Specialization::get();
        $sub_specializations = Specialization::where('parent_id', $row->specialization_id)->get();

        $categories = Category::whereIn('slug', ['doctors', 'visit_doctor', 'consultation'])->get();
        $governorates = Governorate::get();
        $cities = City::where('governorate_id', $row->governorate_id)->get();
        $experiences = Experience::get();


        $categoriesIdes = [];


        return view('Admin.CRUDS.doctor.parts.edit', compact('row', 'specializations', 'categories', 'categoriesIdes', 'governorates', 'cities', 'experiences', 'sub_specializations'));
    }

    // public function update(UpdateDoctorRequest $request, $id) {
    //     $row=Doctor::findOrFail($id);
    //     // Create the doctor
    //     $doctorData = $request->only(['name', 'email', 'nickname', 'phone', 'private_number', 'password', 'gender', 'specialization_id',
    //     'sub_specialization_id', 'governorate_id', 'city_id', 'lang', 'weight', 'location', 'is_popular', 'image', 'service_price_online',
    //     'service_price_home', 'latitude', 'longitude', 'experience_years']);
    //     $doctorData['password'] = bcrypt($request->password);

    //     if ($request->image)
    //         $doctorData["image"] = $this->uploadFiles('doctors', $request->file('image'), null);

    //     if ($request->password) {
    //         $doctorData['password'] = bcrypt($request->password);
    //     } else {
    //         unset($doctorData['password']);
    //     }


    //     unset($doctorData['category_id']);

    //     $row->update($doctorData);

    //     if ($request->category_id) {
    //         ProviderCategory::where('provider_id',$id)->where('provider_type','doctor')->whereNotIn('category_id',$request->category_id)->delete();
    //         ProviderTime::where('provider_id',$id)->where('provider_type','doctor')->whereNotIn('category_id',$request->category_id)->delete();

    //         foreach ($request->category_id as $category_id) {
    //             ProviderCategory::updateOrCreate([
    //                 'category_id' => $category_id,
    //                 'provider_id' => $id,
    //                 'provider_type'=>'doctor',
    //             ]);
    //         }
    //     }

    //     else{
    //         ProviderCategory::where('provider_id',$id)->where('provider_type','doctor')->delete();
    //         ProviderTime::where('provider_id',$id)->where('provider_type','doctor')->delete();
    //     }
    //     $doctorContract = new DoctorContract();
    //     $doctorContract->doctor_id = $row->id;
    //     $doctorContract->contract_start_date = $request->contract_start_date;
    //     $doctorContract->contract_end_date = $request->contract_end_date;
    //     $doctorContract->home_care_net_price = $request->home_care_net_price;
    //     $doctorContract->home_care_gross_price = $request->home_care_gross_price;
    //     $doctorContract->home_care_discount = $request->home_care_discount;
    //     $doctorContract->service_clinic_gross_price = $request->service_clinic_gross_price;
    //     $doctorContract->service_clinic_net_price = $request->service_clinic_net_price;
    //     $doctorContract->service_clinic_discount = $request->service_clinic_discount;
    //     $doctorContract->price_list_outpatient = $request->price_list_outpatient;
    //     $doctorContract->price_list_intpatient = $request->price_list_intpatient;
    //     $doctorContract->service_online_gross_price = $request->service_online_gross_price;
    //     $doctorContract->service_online_net_price = $request->service_online_net_price;
    //     $doctorContract->service_online_discount = $request->service_online_discount;
    //     $doctorContract->contract_note = $request->contract_note;
    //     if ($request->hasFile('attach_contract')) {
    //         $contractPath = $this->uploadFiles('contracts', $request->file('attach_contract'), null);
    //         $doctorContract->attach_contract = $contractPath;
    //     }
    //     if ($request->hasFile('attach_documents')) {
    //         $documentsPath = $this->uploadFiles('documents', $request->file('attach_documents'), null);
    //         $doctorContract->attach_documents = $documentsPath;
    //     }
    //     if ($request->hasFile('attach_price_list')) {
    //         $priceListPath = $this->uploadFiles('price_lists', $request->file('attach_price_list'), null);
    //         $doctorContract->attach_price_list = $priceListPath;
    //     }
    //     $doctorContract->save();
    //     return redirect()->route('doctors.index')->with('success','update success');
    // }

    public function update(UpdateDoctorRequest $request, $id)
    {
        $row = Doctor::findOrFail($id);
        if (!$row) {
            return redirect()->back()->with('error', 'not doctor not found');
        }
        $doctorData = $request->only([
            'name',
            'email',
            'nickname',
            'phone',
            'private_number',
            'password',
            'gender',
            'specialization_id',
            'sub_specialization_id',
            'governorate_id',
            'city_id',
            'experience_years',
            'experience_id'
        ]);
        if ($request->password) {
            $doctorData['password'] = bcrypt($request->password);
        }
        if ($request->image) {
            $image = json_decode($request->image, true);
            $imagedata = $image['image'] ?? null;
            $doctorData['image'] =  $imagedata;
        }
        // $doctorData['password'] = bcrypt($request->password);
        $row->update($doctorData);
        return redirect()->route('doctors.index')->with('success', 'update success');
    }

    public function destroy($id)
    {
        $row = Doctor::findOrFail($id);
        $row->delete();
        return $this->deleteResponse();
    } //end fun

    public function doctor_times($id)
    {
        $doctor = Doctor::findOrFail($id);
        $doctorTimes = ProviderTime::where('provider_type', 'doctor')->where('provider_id', $id)->get();
        $days = Day::get();
        $categoriesIdes = [];
        $categories = Category::whereIn('id', $categoriesIdes)->get();
        return view('Admin.CRUDS.doctor.times', compact('doctor', 'doctorTimes', 'categories', 'days'));
    }
    // -- Start Doctor Contract Logic -- //
    public function doctor_contract($id)
    {
        $doctor = Doctor::findOrFail($id);
        $categories = Category::whereIn('slug', ['doctors', 'visit_doctor', 'consultation'])->get();
        $specializations = Specialization::get();
        $governorates = Governorate::get();
        $experiences = Experience::get();
        return view('Admin.CRUDS.doctor.contract.contract', compact('doctor', 'specializations', 'categories', 'governorates', 'experiences'));
    }

    public function addDoctorContract(Request $request, $id)
    {
        $request->validate([
            // 'name'          => 'required',
            // 'email'         => "required|email|unique:doctors,email",
            // 'nickname'      => 'required|unique:doctors,nickname',
            // 'phone'             => 'required|unique:patients,phone|unique:doctors,phone',
            // 'private_number'    => 'required',
            // 'password'  => 'required|min:6',
            // 'gender'=>'required|in:male,female',
            // 'specialization_id'=>'required|exists:specializations,id',
            // 'sub_specialization_id'=>'required|exists:specializations,id',
            // 'governorate_id'=>'required|exists:governorates,id',
            // 'city_id'=>'required|exists:cities,id',
            // 'lang'=>'required',
            // 'weight'=>'nullable',
            // 'location'=>'required',
            // 'is_popular'=>'required|in:0,1',
            // 'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp',
            // 'latitude'=>'required',
            // 'longitude'=>'required',
            // 'experience_years'=>'nullable',
            'contract_start_date' => 'required|date',
            'contract_end_date' => 'required|date|after:contract_start_date',
            'home_care_net_price' => 'nullable|numeric',
            'home_care_gross_price' => 'nullable|numeric',
            'home_care_discount' => 'nullable|string',
            'service_clinic_gross_price' => 'nullable|numeric',
            'service_clinic_net_price' => 'nullable|numeric',
            'service_clinic_discount' => 'nullable|string',
            'price_list_outpatient' => 'nullable|string',
            'price_list_intpatient' => 'nullable|string',
            'service_online_gross_price' => 'nullable|numeric',
            'service_online_net_price' => 'nullable|numeric',
            'service_online_discount' => 'nullable|string',
            'attach_contract' => 'nullable|file|mimes:pdf,doc,docx,jpg,png',
            'attach_documents' => 'nullable|file|mimes:pdf,doc,docx,jpg,png',
            'attach_price_list' => 'nullable|file|mimes:pdf,doc,docx,jpg,png',
            'contract_note' => 'nullable|string',
        ]);

        // Create the doctor
        // $doctorData = $request->only(['name', 'email', 'nickname', 'phone', 'private_number', 'password', 'gender', 'specialization_id',
        // 'sub_specialization_id', 'governorate_id', 'city_id', 'lang', 'weight', 'location', 'is_popular', 'image', 'service_price_online',
        // 'service_price_home', 'latitude', 'longitude', 'experience_years']);
        // $doctorData['password'] = bcrypt($request->password);

        // if ($request->hasFile('image')) {
        //     $doctorData['image'] = $this->uploadFiles('doctors', $request->file('image'), null);
        // }

        // $doctor = Doctor::create($doctorData);

        $doctor = Doctor::findOrFail($id);

        $doctorContract = new DoctorContract();
        $doctorContract->doctor_id = $doctor->id;
        $doctorContract->contract_start_date = $request->contract_start_date;
        $doctorContract->contract_end_date = $request->contract_end_date;
        $doctorContract->home_care_net_price = $request->home_care_net_price;
        $doctorContract->home_care_gross_price = $request->home_care_gross_price;
        $doctorContract->home_care_discount = $request->home_care_discount;
        $doctorContract->service_clinic_gross_price = $request->service_clinic_gross_price;
        $doctorContract->service_clinic_net_price = $request->service_clinic_net_price;
        $doctorContract->service_clinic_discount = $request->service_clinic_discount;
        $doctorContract->price_list_outpatient = $request->price_list_outpatient;
        $doctorContract->price_list_intpatient = $request->price_list_intpatient;
        $doctorContract->service_online_gross_price = $request->service_online_gross_price;
        $doctorContract->service_online_net_price = $request->service_online_net_price;
        $doctorContract->service_online_discount = $request->service_online_discount;
        $doctorContract->contract_note = $request->contract_note;
        if ($request->hasFile('attach_contract')) {
            $contractPath = $this->uploadFiles('contracts', $request->file('attach_contract'), null);
            $doctorContract->attach_contract = $contractPath;
        }
        if ($request->hasFile('attach_documents')) {
            $documentsPath = $this->uploadFiles('documents', $request->file('attach_documents'), null);
            $doctorContract->attach_documents = $documentsPath;
        }
        if ($request->hasFile('attach_price_list')) {
            $priceListPath = $this->uploadFiles('price_lists', $request->file('attach_price_list'), null);
            $doctorContract->attach_price_list = $priceListPath;
        }
        $doctorContract->save();
        return redirect()->route('doctors.index')->with('success', 'Contract Created successfully.');
    }

    public function showContract(DoctorContract $contract)
    {
        $doctor = Doctor::find($contract->doctor_id);
        return view('Admin.CRUDS.doctor.contract.doctor_contract_show', compact(['contract', 'doctor']));
    }

    public function destroyContract(DoctorContract $contract)
    {
        if ($contract->attach_contract)
            Storage::disk('public')->delete('storage/uploads/' . $contract->attach_contract);

        if ($contract->attach_documents)
            Storage::disk('public')->delete('storage/uploads/' . $contract->attach_documents);

        if ($contract->attach_price_list)
            Storage::disk('public')->delete('storage/uploads/' . $contract->attach_price_list);

        $contract->delete();
        return redirect()->route('doctors.index')->with('success', 'Contract deleted successfully.');
    }

    public function renewContract($contract_id, $doctor_id)
    {
        try {
            $contract = DoctorContract::findOrFail($contract_id);
            $doctor = Doctor::findOrFail($doctor_id);
            return view('Admin.CRUDS.doctor.contract.contract_renewal', [
                'contract' => $contract,
                'doctor' => $doctor
            ]);
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'An error occurred while processing your request.');
        }
    }

    public function storeRenewal(Request $request)
    {
        $request->validate([
            'doctor_id' => 'required|exists:doctors,id',
            'doctor_contract_id' => 'required|exists:doctor_contracts,id',
            'renewal_start_date' => 'required|date|after_or_equal:today',
            'renewal_end_date' => 'nullable|date|after:renewal_start_date',
            'renewed_attach_contract' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'renewed_attach_documents' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'renewed_attach_price_list' => 'nullable|file|mimes:pdf,jpg,jpeg,png|max:2048',
            'renewed_home_care_gross_price' => 'nullable|numeric|min:0',
            'renewed_home_care_discount' => 'nullable|string',
            'renewed_home_care_net_price' => 'nullable|numeric|min:0',
            'renewed_service_clinic_gross_price' => 'nullable|numeric|min:0',
            'renewed_service_clinic_discount' => 'nullable|string',
            'renewed_service_clinic_net_price' => 'nullable|numeric|min:0',
            'renewed_price_list_outpatient' => 'nullable|string',
            'renewed_price_list_intpatient' => 'nullable|string',
            'renewed_service_online_gross_price' => 'nullable|numeric|min:0',
            'renewed_service_online_discount' => 'nullable|string',
            'renewed_service_online_net_price' => 'nullable|numeric|min:0',
            'renewal_note' => 'nullable|string|max:1000',
        ]);
        try {
            $data = $request->all();
            if ($request->hasFile('renewed_attach_contract'))
                $data['renewed_attach_contract'] = $request->file('renewed_attach_contract')->store('contracts');

            if ($request->hasFile('renewed_attach_documents'))
                $data['renewed_attach_documents'] = $request->file('renewed_attach_documents')->store('documents');

            if ($request->hasFile('renewed_attach_price_list'))
                $data['renewed_attach_price_list'] = $request->file('renewed_attach_price_list')->store('price_lists');

            $contract = DoctorContract::findOrFail($request->doctor_contract_id);
            $contract->renewals()->create($data);
            return redirect()->route('doctors.index')->with('success', 'Contract renewed successfully.');
        } catch (\Exception $e) {
            return redirect()->back()->with('error', 'An error occurred while processing your request.');
        }
    }
    // -- End Doctor Contract Logic -- //
    public function update_doctor_times($id, ProviderTimeRequest $request)
    {
        $doctor = Doctor::findOrFail($id);
        ProviderTime::where('provider_id', $id)->where('provider_type', 'doctor')->delete();
        if ($request->type)
            for ($i = 0; $i < count($request->type); $i++) {
                ProviderTime::updateOrCreate([
                    'provider_id' => $id,
                    'category_id' => $request->category_id[$i] ?? null,
                    'day_id' => $request->day_id[$i],
                    'type' => $request->type[$i],
                    'from_time' => $request->from_time[$i],
                    'to_time' => $request->to_time[$i],
                    'provider_type' => 'doctor',
                ]);
            }


        return $this->updateResponse();
    }

    public function get_city_by_governorate(Request $request)
    {
        $governorate = Governorate::findOrFail($request->governorate_id);
        $cities = City::where('governorate_id', $governorate->id)->get();
        return view('Admin.CRUDS.doctor.parts.cities', compact('cities'));
    }

    public function get_sub_specialization(Request $request)
    {
        $specialization = Specialization::findOrFail($request->specialization_id);
        $sub_specializations = Specialization::where('parent_id', $specialization->id)->get();
        return view('Admin.CRUDS.doctor.parts.sub_specializations', compact('sub_specializations'));
    }

    public function activate(Request $request)
    {

        $admin = Doctor::findOrFail($request->id);
        if ($admin->status == true) {
            $admin->status = 0;
            $admin->save();
        } else {
            $admin->status = 1;
            $admin->save();
        }

        return $this->successResponse();
    } //end fun

    public function approved($id)
    {
        $row = DoctorUpdate::findOrFail($id);
        $specializations = Specialization::get();
        $sub_specializations = Specialization::where('parent_id', $row->specialization_id)->get();
        $categories = Category::get();
        $governorates = Governorate::get();
        $cities = City::where('governorate_id', $row->governorate_id)->get();
        $experiences = Experience::get();
        $categoriesIdes = ProviderCategory::where('provider_id', $row->id)->where('provider_type', 'doctor')->pluck('category_id')->toArray();

        return view('Admin.CRUDS.doctor.parts.update', compact('row', 'specializations', 'categories', 'categoriesIdes', 'governorates', 'cities', 'experiences', 'sub_specializations'));
    }

    public function approvedUpdate(Request $request, $id)
    {
        $row = DoctorUpdate::findOrFail($id);
        if ($request->submit == 'Reject') {
            $row->update(['status' => 'reject']);
            session()->flash('success', 'Reject Update Doctor Successfully');
        } else {
            $doctor = Doctor::findOrFail($row->doctor_id);
            $doctor->update([
                'name'                  => $row->name,
                'email'                 => $row->email,
                'image'                 => $row->image,
                'phone'                 => $row->phone,
                'location'              => $row->location,
                'gender'                => $row->gender,
                'nickname'              => $row->nickname,
            ]);
            $row->update(['status' => 'approved']);
            session()->flash('success', 'Approved Update Doctor Successfully');
        }
        return redirect()->route('doctors.index');
    }

    public function new_branch() {}
}
