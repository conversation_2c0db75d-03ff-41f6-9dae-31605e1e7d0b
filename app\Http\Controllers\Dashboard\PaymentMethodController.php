<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\PaymentMethodResource;
use App\Http\Traits\Api_Trait;
use App\Models\PaymentMethod;
use Illuminate\Http\Request;

class PaymentMethodController extends Controller
{
    use Api_Trait;
    public function index()
    {
        $payment_methods = PaymentMethod::get();
        return $this->returnData(PaymentMethodResource::collection($payment_methods), [helperTrans('api.Payment Methods Data')], 200);
    }
    public function store(Request $request)
    {
        $payment_method = PaymentMethod::create($request->all());
        return $this->returnData(new PaymentMethodResource($payment_method), [helperTrans('api.Payment Method created')], 201);
    }
    public function show(PaymentMethod $paymentMethod)
    {
        return $this->returnData(new PaymentMethodResource($paymentMethod), [helperTrans('api.Payment Method Data')], 200);
    }
    public function update(Request $request, PaymentMethod $paymentMethod)
    {
        $paymentMethod->update($request->all());
        return $this->returnData(new PaymentMethodResource($paymentMethod), [helperTrans('api.Payment Method updated')], 200);
    }
    public function destroy(PaymentMethod $paymentMethod)
    {
        $paymentMethod->delete();
        return $this->returnData(new PaymentMethodResource($paymentMethod), [helperTrans('api.Payment Method deleted')], 200);
    }
}
