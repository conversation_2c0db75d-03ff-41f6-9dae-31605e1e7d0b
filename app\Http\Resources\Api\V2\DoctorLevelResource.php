<?php

namespace App\Http\Resources\Api\V2;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DoctorLevelResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     */
    public function toArray(Request $request): array
    {

        $res = [
            'id' => (int)$this->id,
            'name' => $this->getTranslation('name', session_lang()),
        ];


        return $res;
    }
}
