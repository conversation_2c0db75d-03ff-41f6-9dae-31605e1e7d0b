<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GovernorateRequest;
use App\Http\Traits\ResponseTrait;
use App\Models\Governorate;
use App\Models\Nationality;
use Illuminate\Http\Request;
use Ya<PERSON>ra\DataTables\DataTables;

class GovernorateController extends Controller
{
    //
    use  ResponseTrait;

    public function index(Request $request)
    {

        if ($request->ajax()) {
            $admins = Governorate::query()->latest();
            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {

                    $edit = '';
                    $delete = '';


                    return '
                            <button ' . $edit . '  class="editBtn btn rounded-pill btn-primary waves-effect waves-light"
                                    data-id="' . $admin->id . '"
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-edit"></i>
                                </span>
                            </span>
                            </button>
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $admin->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';


                })

                ->editColumn('name', function ($row) {
                    return $row->name;
                })

                ->editColumn('nationality_id', function ($row) {
                    return $row->nationality->name??'';
                })


                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);


        }
        return view('Admin.CRUDS.governorate.index');
    }


    public function create()
    {

        $nationalities=Nationality::get();

        return view('Admin.CRUDS.governorate.parts.create',compact('nationalities'));
    }

    public function store(GovernorateRequest $request)
    {
        $data = $request->validationData();
        Governorate::create($data);
        return $this->addResponse();

    }


    public function show($id)
    {


        //
    }


    public function edit($id )
    {


        $row=Governorate::findOrFail($id);

        $nationalities=Nationality::get();



        return view('Admin.CRUDS.governorate.parts.edit', compact('row','nationalities'));

    }

    public function update(GovernorateRequest $request, $id )
    {

        $row=Governorate::findOrFail($id);
        $data = $request->validationData();
        $row->update($data);
        return $this->updateResponse();

    }


    public function destroy($id)
    {
        $row = Governorate::findOrFail($id);

        $row->delete();

        return $this->deleteResponse();
    }//end fun
}
