<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class ServiceSubCategoryRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'service_category_id' => ['required', 'exists:service_categories,id'],
            'name' => ['required', 'array'],
            'name.ar' => ['required', 'string', 'max:255'],
            'name.en' => ['required', 'string', 'max:255'],
        ];

        return $rules;
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array
     */
    // public function attributes()
    // {
    //     return [
    //         'service_category_id' => trans('dashboard.service_categories.parent_category'),
    //         'name.ar' => trans('dashboard.sub_categories.name_ar'),
    //         'name.en' => trans('dashboard.sub_categories.name_en'),
    //         'description.ar' => trans('dashboard.sub_categories.description_ar'),
    //         'description.en' => trans('dashboard.sub_categories.description_en'),
    //         'image' => trans('dashboard.sub_categories.image'),
    //     ];
    // }
}
