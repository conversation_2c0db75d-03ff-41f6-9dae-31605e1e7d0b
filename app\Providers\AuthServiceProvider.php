<?php

namespace App\Providers;

// use Illuminate\Support\Facades\Gate;
use Illuminate\Foundation\Support\Providers\AuthServiceProvider as ServiceProvider;
use Laravel\Sanctum\Sanctum;

class AuthServiceProvider extends ServiceProvider
{
    /**
     * The model to policy mappings for the application.
     *
     * @var array<class-string, class-string>
     */
    protected $policies = [
        //
    ];

    /**
     * Register any authentication / authorization services.
     */
    public function boot(): void
    {
        Sanctum::authenticateAccessTokensUsing(function ($token, $isValid) {
        // Get admin model only if tokenable_type is Admin
        if ($token->tokenable_type === \App\Models\Admin::class) {
            return \App\Models\Admin::find($token->tokenable_id);
        }

        return null;
    });
    }
}
