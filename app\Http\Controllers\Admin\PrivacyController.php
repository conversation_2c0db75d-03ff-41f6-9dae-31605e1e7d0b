<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\ResponseTrait;
use App\Models\Privacy;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;

class PrivacyController extends Controller
{
    use  ResponseTrait;
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $privacies = Privacy::with('_parent')->latest();
            return DataTables::of($privacies)
                ->addColumn('action', function ($privacy) {
                    $edit = '';
                    $delete = '';
                    return '
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $privacy->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';
                })
                ->editColumn('note', function ($row) {
                    return $row->note;
                })
                ->editColumn('parent_id', function ($row) {
                    return $row->_parent ? $row->_parent->note : null;
                })

                ->editColumn('created_at', function ($privacy) {
                    return date('Y/m/d', strtotime($privacy->created_at));
                })
                ->escapeColumns([])
                ->make(true);
        }
        return view('Admin.CRUDS.privacy.index');
    }

    public function create()
    {
        $mainPrivacy = Privacy::get();
        return view('Admin.CRUDS.privacy.parts.create', compact('mainPrivacy'));
    }

    public function edit($id)
    {
        $row = Privacy::findOrFail($id);
        return view('Admin.CRUDS.privacy.parts.edit', compact('row'));
    }

    public function store(Request $request)
    {
        //dd($request->all());
        $data = $request->all();
        Privacy::create($data);
        return $this->addResponse();
    }

    public function destroy($id)
    {
        $row = Privacy::findOrFail($id);
        $row->delete();
        return $this->deleteResponse();
    }
}
