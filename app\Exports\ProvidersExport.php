<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class ProvidersExport implements FromCollection , WithHeadings
{
    protected $providers;

    public function __construct($providers)
    {
        $this->providers = $providers;
    }

    /**
    * @return \Illuminate\Support\Collection
    */
    public function collection()
    {
        return $this->providers->map(function ($provider) {
            return [
                'ID' => $provider->id,
                'Name' => $provider->name,
                'Category ID' => $provider->provider_category_id,
                'Country' => optional($provider->country)->name,
                'Governorate' => optional($provider->governorate)->name,
                'City' => optional($provider->city)->name,
                'Phone' => $provider->phone,
                'Email' => $provider->email,
                'Longitude' => $provider->longitude,
                'Latitude' => $provider->latitude,
                'Location' => $provider->location,
                'Work Number' => $provider->work_number,
                'WhatsApp Number' => $provider->whatsapp_number,
                'Tel1' => $provider->tel1,
                'Tel2' => $provider->tel2,
                'Website Link' => $provider->website_link,
                

                // Add more fields as needed
            ];
        });
    }

    public function headings(): array
    {
        return [
            'ID',
            'Name',
            'Category ID',
            'Country',
            'Governorate',
            'City',
            'Phone',
            'Email',
            'Longitude',
            'Latitude',
            'Location',
            'Work Number',
            'WhatsApp Number',
            'Tel1',
            'Tel2',
            'Website Link',
            // Add more column names as needed
        ];
    }
}
