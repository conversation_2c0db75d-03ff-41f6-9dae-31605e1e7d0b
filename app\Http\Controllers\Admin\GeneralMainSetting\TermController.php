<?php

namespace App\Http\Controllers\Admin\GeneralMainSetting;

use App\Http\Controllers\Controller;
use App\Http\Traits\ResponseTrait;
use App\Models\Term;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;

class TermController extends Controller
{
    use  ResponseTrait;
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $privacies = Term::with('_parent')->latest();
            return DataTables::of($privacies)
                ->addColumn('action', function ($privacy) {
                    $edit = '';
                    $delete = '';
                    return '
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $privacy->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';
                })
                ->editColumn('note', function ($row) {
                    return $row->note;
                })
                ->editColumn('parent_id', function ($row) {
                    return $row->_parent ? $row->_parent->note : null;
                })

                ->editColumn('created_at', function ($privacy) {
                    return date('Y/m/d', strtotime($privacy->created_at));
                })
                ->escapeColumns([])
                ->make(true);
        }
        return view('Admin.CRUDS.term.index');
    }

    public function create()
    {
        $mainTerm = Term::get();
        return view('Admin.CRUDS.term.parts.create', compact('mainTerm'));
    }

    public function edit($id)
    {
        $row = Term::findOrFail($id);
        return view('Admin.CRUDS.term.parts.edit', compact('row'));
    }

    public function store(Request $request)
    {
        //dd($request->all());
        $data = $request->all();
        Term::create($data);
        return $this->addResponse();
    }

    public function destroy($id)
    {
        $row = Term::findOrFail($id);
        $row->delete();
        return $this->deleteResponse();
    }
}
