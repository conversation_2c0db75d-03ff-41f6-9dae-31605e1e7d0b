<?php

namespace App\Http\Controllers\Api\V2;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Http\Resources\Api\V2\BookingResource;
use App\Http\Resources\Api\V2\BookingStatusResource;
use App\Http\Resources\Api\V2\BookingTypeResource;
use App\Http\Traits\Api_Trait;
use App\Http\Traits\Upload_Files;
use App\Models\ApiSetting;
use App\Models\Booking;
use App\Models\BookingType;
use App\Models\MainServicePackage;
use App\Models\Package;
use App\Models\PatientSubscribe;
use App\Models\PromoCode;
use App\Models\Relative;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use App\Models\GlobalAttachment;
use App\Models\NewBooking;
use App\Models\StatusBooking;
use Illuminate\Support\Facades\DB;

class BookingController extends Controller
{
    use Api_Trait, Upload_Files;
    public function getTypes()
    {
        $types = BookingType::get();
        return $this->returnData(BookingTypeResource::collection($types), [helperTrans('api.Booking Types Data')], 200);
    }
    public function getStatus()
    {
        $status = StatusBooking::get();
        return $this->returnData(BookingStatusResource::collection($status), [helperTrans('api.Booking Status Data')], 200);
    }
    public function index(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'status_ids' => 'required|array',
                'status_ids.*' => 'exists:status_bookings,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();

        $bookings = NewBooking::where('patient_id', $patient->id)
            ->whereIntegerInRaw('status_booking_id', $request->status_ids)->get();



        return $this->returnData(BookingResource::collection($bookings), [helperTrans('api.Bookings Data')], 200);
    }
    public function store(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'booking_type_id' => 'required|exists:booking_types,id',
                'date' => 'nullable|date',
                'time' => 'nullable|date_format:H:i',
                'description' => 'nullable|string',
                'payment_method_id' => 'required|exists:payment_methods,id',
                'price' => 'numeric',
                'promo_code_id' => 'nullable|exists:promo_codes,id',
                'provider_id' => 'required_if:booking_type_id,2|nullable|exists:providers,id',
                'provider_branch_id' => 'required_if:booking_type_id,2|nullable|exists:provider_branches,id',
                'doctor_id' => 'nullable|exists:doctors,id',
                'relative_id' => 'nullable|exists:relatives,id',
                'address_detail_id' => 'nullable|exists:adress_details,id',

                // Conditionally require the whole services array
                'services' => 'array',

                // Conditionally validate each service entry
                'services.*.service_id' => 'nullable|exists:services,id',
                'services.*.quantity'   => 'nullable|integer',
                'services.*.price'      => 'nullable|numeric',

                // Attachments
                'file_ids' => 'array|nullable',
                'file_ids.*' => 'required|exists:attachments,id',
            ]
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $validated = $validator->validated();
        DB::beginTransaction();

        try {
            $validated['patient_id'] = auth('patient')->id();
            $validated['payment'] = 'unpaid';
            $validated['status_booking_id'] = $validated['booking_type_id'] == 1 ? 2 : 1;
            $validated['price'] = $validated['booking_type_id'] == 1
                ? price_after_promocode(ApiSetting::get('general_consultation_price', 100), $request->promo_code_id)
                : $validated['price'];

            // Create the booking
            $booking = NewBooking::create($validated);

            // Create services
            if (!empty($validated['services'])) {
                foreach ($validated['services'] as $service) {
                    $booking->services()->create([
                        'service_id' => $service['service_id'],
                        'quantity' => $service['quantity'] ?? 1,
                        'price' => $service['price'],
                    ]);
                }
            }

            // Attach files
            foreach ($validated['file_ids'] ?? [] as $attachmentId) {
                GlobalAttachment::create([
                    'attachment_id' => $attachmentId,
                    'attachable_id' => $booking->id,
                    'attachable_type' => NewBooking::class,
                ]);
            }
            if ($booking) {
                $promoCode = PromoCode::find($request->promo_code_id);
                if ($promoCode && $promoCode->is_active) {
                    $promoCode->current_redemptions++;
                    $promoCode->save();

                    // If the usage limit per patient is reached, set the is_active to false
                    if ($promoCode && $promoCode->current_redemptions == $promoCode->max_redemptions) {
                        $promoCode->is_active = false;
                        $promoCode->save();
                    }
                }
            }
            if ($booking->doctor) {
                run_push_notification($booking->doctor->id, 'doctor', [
                    'title' => 'new Booking',
                    'body' => 'There is a new booking',
                    'type' => 'new_booking',
                ]);
            }

            run_push_notification($booking->patient->id, 'patient', [
                'title' => 'Booking Request',
                'body' => 'Booking has been created successfully',
                'type' => 'new_booking',
            ]);

            DB::commit();
            $bookingResource = new BookingResource($booking);
            return $this->returnData($bookingResource, [helperTrans('api.booking successfully, please complete payment')]);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json([
                'message' => 'Failed to create booking',
                'error' => $e->getMessage(),
            ], 500);
        }
    }
}
