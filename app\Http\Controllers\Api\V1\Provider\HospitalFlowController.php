<?php

namespace App\Http\Controllers\Api\V1\Provider;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Http\Requests\FileRequest;
use App\Http\Resources\HospitalDoctorResource;
use App\Http\Traits\Api_Trait;
use App\Imports\HospitalDoctorImport;
use App\Models\BranchClinic;
use App\Models\BranchClinicDoctor;
use App\Models\Clinic;
use App\Models\Doctor;
use App\Models\DoctorClinic;
use App\Models\ProviderBranch;
use App\Models\ProviderRequest;
use Illuminate\Http\Request;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Validator;
use Carbon\Carbon;

class HospitalFlowController extends Controller
{
    use Api_Trait;
    public function getBranchClinics(ProviderBranch $providerBranch)
    {
        $clinics = $providerBranch->clinics->map(function ($clinic) {
            return [
                'id' => $clinic->id,
                'name' => $clinic->getTranslation('name', request()->header('lang')),
                'notes' => $clinic->notes,
            ];
        });
        return $this->returnData($clinics, [helperTrans('api.Clinic Data')], 200);
    }

    public function getClinicDoctors(ProviderBranch $providerBranch, Clinic $clinic)
    {
        $doctors = BranchClinicDoctor::whereHas(
            'branchClinic',
            fn($q) => $q->where('provider_branch_id', $providerBranch->id)
                ->where('clinic_id', $clinic->id)
        )
            ->with(['doctor', 'branchClinic'])
            ->get()->unique(function ($item) {
                return $item->branch_clinic_id . $item->doctor_id;
            })->values()->map(function ($branchClinicDoctor) use ($providerBranch) {
                return [
                    // 'branch_name' => $branchClinicDoctor->branchClinic?->branch?->name ?? '',
                    // 'branch_id' => $branchClinicDoctor->branchClinic?->branch?->id ?? '',
                    // 'branch_name' => $branchClinicDoctor->branchClinic?->branch?->name ?? '',
                    // 'branch_id' => $branchClinicDoctor->branchClinic?->branch?->id ?? '',
                    'doctor_id' => $branchClinicDoctor->doctor?->id ?? '',
                    'doctor_name' => request()->header('lang') == 'ar' ? json_decode($branchClinicDoctor->doctor?->nickname)->ar : json_decode($branchClinicDoctor->doctor?->nickname)->en,
                    'doctor_level'=>$branchClinicDoctor->doctor->doctor_level?->getTranslation('name', request()->header('lang')),
                    'image'=> new_get_file($branchClinicDoctor->doctor?->image,'person'),
                    'price' => $branchClinicDoctor->price ?? 0,
                    'discount'=>$branchClinicDoctor->branchClinic->branch->provider->discount ?? 0,
                    'times'=>$branchClinicDoctor->doctor->doctor_hospital_clinic()->where('id', $providerBranch->id)->first()['times']
                ];
            });
        return $this->returnData($doctors, [helperTrans('api.Clinic Doctors Data')], 200);
    }



    public function getDoctorTimes(ProviderBranch $providerBranch, Clinic $clinic, Doctor $doctor, $date)
    {
        $validator = Validator::make(
            [
                'date' => $date
            ],
            [
                'date' => 'required|date',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $day_en_order = Carbon::parse($date)->dayOfWeek;
        $day_orders = [
            0 => 2,
            1 => 3,
            2 => 4,
            3 => 5,
            4 => 6,
            5 => 7,
            6 => 1
        ];

        $date_day = $day_orders[$day_en_order];

        $doctor_times = BranchClinicDoctor::select(['from_time', 'to_time'])->wherehas(
            'branchClinic',
            fn($q) => $q->where('provider_branch_id', $providerBranch->id)
                ->where('clinic_id', $clinic->id)
        )->where([
            'doctor_id' => $doctor->id,
            'day_id' => $date_day
        ])->first();

        if (!$doctor_times) {
            return $this->returnErrorNotFound(helperTrans('not found doctor times'));
        }

        $booking_times = ProviderRequest::whereNotIn('status', ['complete', 'cancel'])->select(['time'])->where([
            'provider_branch_id' => $providerBranch->id,
            'clinic_id'      => $clinic->id,
            'doctor_id'      => $doctor->id,
            'date'      => $date
        ])->pluck('time')->map(function ($item) {
            return Carbon::parse($item)->format('H:i');
        })->toArray();

        $available_time = [];

        $start_time = Carbon::parse($doctor_times['from_time']);
        $end_time =  Carbon::parse($doctor_times['to_time']);
        $time = $start_time;

        while (true) {
            if ($time->addMinutes(30)->lessThanOrEqualTo($end_time)) {
                $available_time[] = $time->subMinutes(30)->format('H:i');
                $time->addMinutes(30);
            } else {
                break;
            }
        }
        return $this->returnData(array_values(array_diff($available_time, $booking_times)), '');
    }

    // public function import_hospital_doctor(FileRequest $request)
    // {
    //     Excel::import(new HospitalDoctorImport, $request->file('file'));
    //     return $this->returnData(null, [helperTrans('api.Imported successfully')], 200);
    // }
}
