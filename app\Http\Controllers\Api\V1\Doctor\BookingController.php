<?php

namespace App\Http\Controllers\Api\V1\Doctor;

use App\Http\Controllers\Controller;
use App\Http\Resources\AgoraRoomResource;
use App\Http\Resources\BookingResource;
use App\Http\Resources\DoctorResource;
use App\Http\Resources\ReplayingBookingResource;
use App\Http\Resources\SelectProviderResource;
use App\Http\Resources\SpecializationBookingResource;
use App\Http\Traits\Api_Trait;
use App\Http\Traits\NotificationFirebaseTrait;
use App\Models\AgoraRoom;
use App\Models\Booking;
use App\Models\BookingDoc;
use App\Models\Category;
use App\Models\Doctor;
use App\Models\Patient;
use App\Models\FirebaseToken;
use App\Models\MainService;
use App\Models\Notification;
use App\Models\ProviderCategory;
use App\Models\ReplyingBooking;
use App\Models\ReplyingBookingAnalysis;
use App\Models\ReplyingBookingDiagnosis;
use App\Models\ReplyingBookingMedical;
use App\Models\ReplyingBookingRadiology;
use App\Models\SelectProvider;
use App\Models\SpecializationBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use App\Notifications\PushFireBaseNotification;
use App\Rules\ExistsInBothBookingTables;
use Barryvdh\DomPDF\Facade\Pdf;
use Carbon\Carbon;
use Illuminate\Support\Facades\Notification as FacadesNotification;
use Laravel\Prompts\Table;
use Illuminate\Support\Facades\Storage;
use Spatie\Browsershot\Browsershot;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\View;
use Spatie\Browsershot\Exceptions\CouldNotTakeBrowsershot;

class BookingController extends Controller
{
    use Api_Trait, NotificationFirebaseTrait;

    //

    public function index(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'status' => 'required|in:assigned,pending,active,complete,cancel',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        // // Fetch category IDs associated with the doctor
        // $categoryIds = ProviderCategory::where('provider_type', 'doctor')
        //     ->where('provider_id', $doctor->id)
        //     ->pluck('category_id')
        //     ->toArray();
        // // Fetch main service IDs associated with the categories
        // $mainServiceIds = MainService::whereHas('categories', function ($query) use ($categoryIds) {
        //     $query->whereIn('categories.id', $categoryIds);
        // })->pluck('id')->toArray();


        if ($doctor->doctor_type == 'general') {

            $general_status = [
                'pending' => ['pending'],
                'assigned' => ['follow up', 'transfer'],
                'active' => ['active'],
                'complete' => ['complete', 'reappoint', 'referral', 'specialized']
            ];

            if ($request->status == 'pending') {
                $bookings = Booking::with('patient')
                    ->where('main_service_id', 1)
                    ->whereNull('doctor_id')
                    ->whereIn('status', $general_status[$request->status])
                    ->latest()
                    ->get();
            } else {
                $bookings = Booking::with('patient')
                    ->where('main_service_id', 1)
                    ->where('doctor_id', $doctor->id)
                    ->whereIn('status', $general_status[$request->status] ?? [])
                    ->latest()
                    ->get();
            }
        } else if ($doctor->doctor_type == 'specialized') {

            $bookings = SpecializationBooking::with('patient')
                ->where('doctor_id', $doctor->id)
                ->where('status', $request->status)
                ->latest()
                ->get();
        }
        // Fetch bookings based on status
        // if ($request->status == 'pending') {
        //     // Pending bookings can be either unassigned or assigned to this doctor
        //     $instant_bookings = Booking::with('patient')
        //         ->where('main_service_id', 1)
        //         ->where('status', $request->status)
        //         ->where(function ($query) use ($doctor) {
        //             $query->whereNull('doctor_id')->orWhere('doctor_id', $doctor->id);
        //         })->latest()->get();

        //     $specialized_bookings = SpecializationBooking::with('patient')
        //         ->where('status', $request->status)
        //         ->Where('doctor_id', $doctor->id)->latest()->get();

        //     // dd($specialized_bookings);

        //     $bookings = $instant_bookings->merge($specialized_bookings);
        // } else if ($request->status == 'active') {
        //     // Pending bookings can be either unassigned or assigned to this doctor
        //     $instant_bookings = Booking::with('patient')
        //         ->where('main_service_id', 1)
        //         ->where('status', $request->status)
        //         ->where('doctor_id', $doctor->id)->latest()->get();

        //     $specialized_bookings = SpecializationBooking::with('patient')
        //         ->where('status', $request->status)
        //         ->Where('doctor_id', $doctor->id)->latest()->get();

        //     // dd($specialized_bookings);

        //     $bookings = $instant_bookings->merge($specialized_bookings);
        // } else {
        //     // Bookings with a Specific status assigned to this doctor
        //     $instant_bookings = Booking::with('patient')
        //         ->latest()
        //         ->where('doctor_id', $doctor->id)
        //         ->whereIn('status', ['complete', 'reappoint', 'follow up', 'transfer', 'referral', 'specialized'])
        //         ->get();

        //     $specialized_bookings = SpecializationBooking::with('patient')
        //         ->where('status', $request->status)
        //         ->Where('doctor_id', $doctor->id)->latest()->get();

        //     $bookings = $instant_bookings->merge($specialized_bookings);
        // }

        return $this->returnData(BookingResource::collection($bookings), [helperTrans('api.Booking Data')], 200);
    }

    // SendNotification
    public function SendNotification($title, $message, $token, $data = [])
    {
        try {
            $firebase = (new \Kreait\Firebase\Factory())->withServiceAccount(public_path('firebase.json'))->createMessaging();

            $message = \Kreait\Firebase\Messaging\CloudMessage::withTarget('token', $token)
                ->withNotification([
                    'title' => $title,
                    'body' => $message,
                    'sound' => true
                ])
                ->withData([
                    'order_id' => $data['order_id'],
                    'priority' => $data['priority'],
                    'type' => $data['type'],
                    'android_channel_id' => $data['android_channel_id']
                ]);

            return $firebase->send($message);
        } catch (\Kreait\Firebase\Exception\Messaging\NotFound $e) {
            // Log the error with details
            Log::error("Firebase Messaging Error: Token not found. Exception: " . $e->getMessage());
            return response()->json(['error' => 'Notification could not be sent. Token not recognized by Firebase project.'], 404);
        } catch (\Kreait\Firebase\Exception\MessagingException $e) {
            // Handle other Firebase messaging exceptions
            Log::error("Firebase Messaging Error: " . $e->getMessage());
            return response()->json(['error' => 'Notification could not be sent.'], 500);
        } catch (\Exception $e) {
            // General exception handling
            Log::error("An unexpected error occurred: " . $e->getMessage());
            return response()->json(['error' => 'An unexpected error occurred.'], 500);
        }
    }

    public function accept_booking(Request $request)
    {

        $doctor = auth('doctor')->user();

        if ($doctor->doctor_type == 'general') {

            $validator = Validator::make($request->all(), ['booking_id' => 'required|exists:bookings,id',], []);
            if ($validator->fails()) {
                return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
            }
            $booking = Booking::where('status', 'pending')->find($request->booking_id);

            if (!$booking)
                return $this->returnError([helperTrans('api.not valid ')]);


            $booking->doctor_id = $doctor->id;
            $booking->status = 'active';
            $booking->save();
        } else if ($doctor->doctor_type == 'specialized') {
            $validator = Validator::make($request->all(), ['booking_id' => 'required|exists:specialization_bookings,id',], []);
            if ($validator->fails()) {
                return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
            }
            $booking = SpecializationBooking::where('status', 'pending')->where('doctor_id', $doctor->id)->find($request->booking_id);
            if (!$booking)
                return $this->returnError([helperTrans('api.not valid')]);

            $booking->status = 'active';
            $booking->save();
        }


        // $validator = Validator::make($request->all(), ['booking_id' => 'required|exists:bookings,id',], []);





        $body = helperTrans('api.It was asked by the doctor and will be answered now');

        $patient_info = ['title' => 'Booking accepted', 'body' => "Doctor {$doctor->name} has accept your booking"];
        run_push_notification($booking->patient_id, 'patient', $patient_info);


        // Notification::create([
        //     'title' => helperTrans('api.Accept Booking'),
        //     'body' => $body,
        //     'user_type' => 'patient',
        //     'user_id' => $booking->patient_id,
        //     'type' => 'accept',
        //     'foreign_id' => $booking->id,
        //     'main_service_id' => 1,
        //     'date' => date('Y-m-d'),
        // ]);

        // if ($booking->patient_id && $booking->main_service_id) {
        //     // Notification::create([
        //     //     'title' => helperTrans('api.Accept Booking'),
        //     //     'body' => $body,
        //     //     'user_type' => 'patient',
        //     //     'user_id' => $booking->patient_id,
        //     //     'type' => 'accept',
        //     //     'foreign_id' => $booking->id,
        //     //     'main_service_id' => $booking->main_service_id,
        //     //     'date' => date('Y-m-d'),
        //     // ]);
        // } else {
        //     return $this->returnError([helperTrans('api.patient or service data missing')]);
        // }

        return $this->returnSuccessMessage(['api.accepted successfully']);
    }


    public function booking_details(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $booking = Booking::with([
            'patient',
            'mainService',
            'replying.replyingBookingDiagnoses',
            'replying.replyingBookingAnalysis',
            'replying.replyingBookingMedicals',
            'replying.replyingBookingRadiology',
        ])->find($request->booking_id);


        // Get the referral booking if exists
        if ($booking->refer_to) {
            if ($booking->status === 'specialized') {
                $booking->referral_specialized_booking = SpecializationBooking::where('refer_from', $booking->refer_to)->first();
            } else {
                $booking->referral_booking = Booking::where('refer_from', $booking->refer_to)->first();
            }
        }
        $response_data = BookingResource::make($booking);

        return $this->returnData($response_data, [helperTrans('api.Booking Data')], 200);
    }

    public function make_replying(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                // General
                'booking_id' => [new ExistsInBothBookingTables()],
                'desc' => 'nullable',
                'files_ids' => 'nullable|array',

                // medication
                'medication' => 'nullable|array',
                'medication.*.pharmacy_id' => 'nullable|exists:pharmacies,id',
                'medication.*.medication_unit_id' => 'nullable|exists:medication_units,id',
                'medication.*.medication_way_id' => 'nullable|exists:medication_ways,id',
                'medication.*.note' => 'nullable|string',

                // laboratory
                'laboratory' => 'nullable|array',
                'laboratory.*.laboratory_id' => 'nullable|exists:laboratories,id',
                'laboratory.*.analysis_id' => 'nullable|exists:analyses,id',

                // radiology_center
                'radiology_center' => 'nullable|array',
                'radiology_center.*.radiology_center_id' => 'nullable|exists:radiology_centers,id',
                'radiology_center.*.radiology_id' => 'nullable|exists:radiologies,id',

                // diagnoses
                'diagnoses' => 'nullable|array',
                'diagnoses.*.diagnosis_id' => 'nullable|exists:diagnoses,id',

                // follow up
                'follow_up' => 'nullable',
                'follow_up.day' => 'required_with:follow_up|date',
                'follow_up.time' => 'required_with:follow_up|date_format:H:i',

                // referral
                'referral' => 'nullable',
                'referral.doctor_id' => 'required_with:referral|exists:doctors,id',

                // specialized
                'specialized' => 'nullable',
                'specialized.doctor_id' => 'required_with:specialized|exists:doctors,id',
                'specialized.booking' => 'nullable',
                'specialized.booking_details.visit' => 'nullable|in:offline,home,online',
                'specialized.booking_details.visit_data' => 'required_with:specialized.booking.visit|nullable',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }
        $doctor = auth('doctor')->user();

        // validate specialized.booking_details.visit_data -> date, time, price, and if the visit is offline validate the branch_id as well
        if ($doctor->doctor_type == 'general' && $request->specialized && $request->specialized['booking']) {

            $visit_data = (object)$request->specialized['booking_details']['visit_data'];

            if (!isset($visit_data->date) || !isset($visit_data->time) || !isset($visit_data->price)) {
                return $this->returnError([helperTrans('api.booking [date - time - price] are required')]);
            }

            if ($request->specialized['booking_details']['visit'] === 'offline') {
                if (!isset($visit_data->branch_id)) {
                    return $this->returnError([helperTrans('api.Clinic branch is required')]);
                }
            }
        }

        $booking_id = $request->booking_id;

        // $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($booking_id);

        if ($doctor->doctor_type == 'general') {
            $booking = Booking::whereIn('status', ['active', 'follow up', 'transfer'])->where('doctor_id', $doctor->id)->find($booking_id);
        } else if ($doctor->doctor_type == 'specialized') {
            $booking = SpecializationBooking::where('status', 'active')->where('doctor_id', $doctor->id)->find($booking_id);
        }

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replaying = ReplyingBooking::where('booking_id', $booking_id)->first();

        if ($replaying) {
            return $this->returnError([helperTrans('api.You are Replaying Before')]);
        }

        // Main reply
        $replaying = ReplyingBooking::create([
            'booking_id' => $booking->id,
            // 'patient_id' => $booking->patient_id,
            // 'doctor_id' => $doctor->id,
            'desc' => $request->desc,
        ]);

        if ($request['files_ids'] && count($request['files_ids']) > 0) {
            foreach ($request['files_ids'] as $file_id) {
                $media = Media::find($file_id);
                if ($media) {
                    $media->model_type = ReplyingBooking::class;
                    $media->model_id = $replaying->id;
                    $media->save();
                }
            }
        }

        if (!$replaying) {
            return $this->returnError([helperTrans('api.Cannot Replay to patient case')]);
        }

        // Close booking, but may be changed if follow up, referral, or specialized
        $booking->update([
            'status' => 'complete',
        ]);


        // medication reply
        if ($request->medication) {
            $medicalsData = $request->input('medication');
            foreach ($medicalsData as $medicalData) {
                $data = [];

                // $data['booking_id'] = $booking->id;
                // $data['patient_id'] = $booking->patient_id;
                // $data['doctor_id'] = $booking->doctor_id;
                $data['replying_booking_id'] = $replaying->id ?? null;
                $data['pharmacy_id'] = $medicalData['pharmacy_id'] ?? null;
                $data['medication_unit_id'] = $medicalData['medication_unit_id'] ?? null;
                $data['medication_way_id'] = $medicalData['medication_way_id'] ?? null;
                $data['duration'] = $medicalData['duration'] ?? null;
                $data['note'] = $medicalData['note'] ?? null;

                ReplyingBookingMedical::create($data);
            }
        }

        // laboratory reply
        if ($request->laboratory) {
            $analysisData = $request->input('laboratory');
            foreach ($analysisData as $analysData) {
                $data = [];

                // $data['booking_id'] = $booking->id;
                // $data['patient_id'] = $booking->patient_id;
                // $data['doctor_id'] = $booking->doctor_id;
                $data['replying_booking_id'] = $replaying->id ?? null;
                $data['laboratory_id'] = $analysData['laboratory_id'] ?? null;
                $data['analysis_id'] = $analysData['analysis_id'] ?? null;
                // $data['note'] = $analysData['note'];

                ReplyingBookingAnalysis::create($data);
            }
        }

        // radiology_center reply
        if ($request->radiology_center) {
            $radiologyData = $request->input('radiology_center');
            foreach ($radiologyData as $radiologData) {
                $data = [];
                // $data['booking_id'] = $booking->id;
                // $data['patient_id'] = $booking->patient_id;
                // $data['doctor_id'] = $booking->doctor_id;
                $data['replying_booking_id'] = $replaying->id ?? null;
                $data['radiology_center_id'] = $radiologData['radiology_center_id'] ?? null;
                $data['radiology_id'] = $radiologData['radiology_id'] ?? null;
                // $data['note'] = $radiologData['note'];

                ReplyingBookingRadiology::create($data);
            }
        }

        // diagnoses reply
        if ($request->diagnoses) {

            $diagnosesData = $request->input('diagnoses');
            foreach ($diagnosesData as $diagnosisData) {
                $data = [];
                // $data['booking_id'] = $booking->id;
                // $data['patient_id'] = $booking->patient_id;
                // $data['doctor_id'] = $booking->doctor_id;
                $data['replying_booking_id'] = $replaying->id ?? null;
                $data['diagnosis_id'] = $diagnosisData['diagnosis_id'] ?? null;
                ReplyingBookingDiagnosis::create($data);
            }
        }

        // follow up
        if ($doctor->doctor_type == 'general' && $request->follow_up && ($booking->refer_from == null)) {

            // create a referral unique code
            $transfer_ref = create_ref();

            if (!$booking->update([
                'status' => 'reappoint',
                'refer_to' => $transfer_ref
            ])) {
                return $this->returnError([helperTrans('api.Failed to book a follow up')]);
            };
            // return $transfer_ref;

            unset($booking->id, $booking->created_at, $booking->updated_at, $booking->price);

            $booking->id = getNextSharedId();
            $booking->desc = $request->desc;
            $booking->day = $request['follow_up']['day'];
            $booking->time = $request['follow_up']['time'];
            $booking->status = 'follow up';
            $booking->refer_to = null;
            $booking->refer_from = $transfer_ref;

            $new_booking = Booking::create($booking->toArray());


            if (!$new_booking) {
                return $this->returnError([helperTrans('api.Failed to create new booking')]);
            }
        }

        //referral
        if ($doctor->doctor_type == 'general' && $request->referral && ($booking->refer_from == null)) {

            $transfer_ref = create_ref();

            if (!$booking->update([
                'status' => 'referral',
                'refer_to' => $transfer_ref
            ])) {
                return $this->returnError([helperTrans('api.Failed to book a referral')]);
            };

            unset($booking->id, $booking->created_at, $booking->updated_at, $booking->price);

            $booking->id = getNextSharedId();
            $booking->doctor_id = $request->referral['doctor_id'];
            $booking->desc = $request->desc;
            $booking->day = $request->day;
            $booking->time = $request->time;
            $booking->status = 'transfer';
            $booking->refer_to = null;
            $booking->refer_from = $transfer_ref;

            $new_booking = Booking::create($booking->toArray());

            if (!$new_booking) {
                return $this->returnError([helperTrans('api.Failed to create new booking')]);
            }
            $doctor_info = ['title' => 'Booking assign', 'body' => "Doctor {$doctor->name} assign booking to you"];
            run_push_notification($request->referral['doctor_id'], 'doctor', $doctor_info);
            // Notification::create([
            //     'title' => 'Booking assign',
            //     'body' => "Doctor {$doctor->name} assign booking to you",
            //     'user_type' => 'doctor',
            //     'user_id' => $request->referral['doctor_id'],
            //     'type' => 'booking',
            //     'foreign_id' => $new_booking->id,
            //     'main_service_id' => $new_booking->main_service_id ?? '',
            //     'date' => date('Y-m-d'),
            // ]);
        }

        // specialized
        if ($doctor->doctor_type == 'general' && $request->specialized && ($booking->refer_from == null)) {
            $specialized = $request->specialized;
            $booking_details = $specialized['booking_details'] ?? '';
            $is_new_booking = $specialized['booking'];

            $transfer_ref = create_ref();


            if ($is_new_booking) {
                $booking->update([
                    'status' => 'specialized',
                    'refer_to' => $transfer_ref,
                ]);
            } else {
                $booking->update([
                    'status' => 'specialized',
                    'recommended_doctor_id' => $specialized['doctor_id']
                ]);
            }


            // if (!$booking->update([
            //     'status' => 'specialized',
            //     'refer_to' => $request->specialized['booking'] ? $transfer_ref : null,
            //     'recommend_doctor_id' => $reuest->specialized['booking'] ? $specialized['doctor_id'] : null
            // ])) {
            //     return $this->returnError([helperTrans('api.Failed to book a specialized')]);
            // };

            // specialized booking
            if ($request->specialized['booking']) {

                $new_booking = SpecializationBooking::create([
                    'id'   => getNextSharedId(),
                    'date' => $visit_data->date,
                    'time' => $visit_data->time,
                    'price' => $visit_data->price,
                    'visit' => $booking_details['visit'],
                    // 'status' => 'pending', // default not_paid
                    'doctor_id' => $specialized['doctor_id'],
                    'patient_id' => $booking->patient_id,
                    'branch_id' => $visit_data->branch_id ?? null,
                    'refer_from' => $transfer_ref,
                    "phone" => $visit_data->phone ?? null,
                    "latitude" => $visit_data->latitude ?? null,
                    "longitude" => $visit_data->longitude ?? null,
                    "location" => $visit_data->location ?? null,
                    "governorate_id" => $visit_data->governorate_id ?? null,
                    "city_id" => $visit_data->city_id ?? null,
                    "street" => $visit_data->street ?? null,

                ]);
                if (!$new_booking) {
                    return $this->returnError([helperTrans('api.Failed to create new specialized booking')]);
                }
            }
        }


        $patient_info = ['title' => 'Booking Reply', 'body' => "Doctor {$doctor->name} has replyed your booking"];
        run_push_notification($booking->patient_id, 'patient', $patient_info);

        // notifications
        // Notification::create([
        //     'title' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'body' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'user_type' => 'patient',
        //     'user_id' => $booking->patient_id,
        //     'type' => 'accept',
        //     'date' => date('Y-m-d'),
        //     'foreign_id' => $booking->id,
        //     'main_service_id' => $booking->main_service_id ?? '2',

        // ]);

        return  $this->returnData(ReplayingBookingResource::make($replaying), [helperTrans('api.replaying added successfully')]);
    }

    public function make_new_replying(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'desc' => 'nullable',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replaying = ReplyingBooking::where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();

        if (!$replaying) {
            $replaying = ReplyingBooking::create([
                'booking_id' => $booking->id,
                'patient_id' => $booking->patient_id,
                'doctor_id' => $doctor->id,
                'desc' => $request->desc,
            ]);
        } else {
            $replaying->update([
                'desc' => $request->desc,
            ]);
        }

        // Notification::create([
        //     'title' => helperTrans('api.Doctor Report'),
        //     'body' => $$request->desc ?? helperTrans('api.Doctor Report'),
        //     'user_type' => 'patient',
        //     'user_id' => $booking->patient_id,
        //     'type' => 'accept',
        //     'date' => date('Y-m-d'),
        //     'foreign_id' => $booking->id,
        //     'main_service_id' => $booking->main_service_id,

        // ]);


        return $this->returnSuccessMessage([helperTrans('api.replaying added successfully')]);
    }

    public function make_replying_laboratory(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'laboratory' => 'nullable|array',
                'laboratory.*.laboratory_id' => 'required_with:laboratory|nullable|exists:laboratories,id',
                'laboratory.*.analysis_id' => 'required_with:laboratory|nullable|exists:analyses,id',
                'laboratory.*.note' => 'required_with:laboratory|nullable|string',


            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replaying = ReplyingBooking::where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();

        if (!$replaying) {

            $replaying = ReplyingBooking::create([
                'booking_id' => $booking->id,
                'patient_id' => $booking->patient_id,
                'doctor_id' => $doctor->id,
                'desc' => null,
            ]);
        }

        ReplyingBookingAnalysis::where('booking_id', $booking->id)->where('replying_booking_id', $replaying->id)->delete();


        if ($request->laboratory) {
            $analysisData = $request->input('laboratory');
            foreach ($analysisData as $analysData) {
                $data = [];

                $data['booking_id'] = $booking->id;
                $data['patient_id'] = $booking->patient_id;
                $data['doctor_id'] = $booking->doctor_id;
                $data['replying_booking_id'] = $replaying->id;
                $data['laboratory_id'] = $analysData['laboratory_id'];
                $data['analysis_id'] = $analysData['analysis_id'];
                $data['note'] = $analysData['note'];

                ReplyingBookingAnalysis::create($data);
            }
        }


        // Notification::create([
        //     'title' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'body' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'user_type' => 'patient',
        //     'user_id' => $booking->patient_id,
        //     'type' => 'accept',
        //     'date' => date('Y-m-d'),
        //     'foreign_id' => $booking->id,
        //     'main_service_id' => $booking->main_service_id,

        // ]);


        return $this->returnSuccessMessage([helperTrans('api.laboratory replaying added successfully')]);
    }

    public function make_replying_radiology_center(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'radiology_center' => 'nullable|array',
                'radiology_center.*.radiology_center_id' => 'required_with:radiology_center|nullable|exists:radiology_centers,id',
                'radiology_center.*.radiology_id' => 'nullable|exists:radiologies,id',
                'radiology_center.*.note' => 'nullable|string',


            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replaying = ReplyingBooking::where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();

        if (!$replaying) {

            $replaying = ReplyingBooking::create([
                'booking_id' => $booking->id,
                'patient_id' => $booking->patient_id,
                'doctor_id' => $doctor->id,
                'desc' => null,
            ]);
        }

        ReplyingBookingRadiology::where('booking_id', $booking->id)->where('replying_booking_id', $replaying->id)->delete();


        if ($request->radiology_center) {
            $radiologyData = $request->input('radiology_center');
            foreach ($radiologyData as $radiologData) {
                $data = [];
                $data['booking_id'] = $booking->id;
                $data['patient_id'] = $booking->patient_id;
                $data['doctor_id'] = $booking->doctor_id;
                $data['replying_booking_id'] = $replaying->id;
                $data['radiology_center_id'] = $radiologData['radiology_center_id'];
                $data['radiology_id'] = $radiologData['radiology_id'];
                $data['note'] =  $radiologData['note'] ?? null;

                ReplyingBookingRadiology::create($data);
            }
        }

        // Notification::create([
        //     'title' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'body' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'user_type' => 'patient',
        //     'user_id' => $booking->patient_id,
        //     'type' => 'accept',
        //     'date' => date('Y-m-d'),
        //     'foreign_id' => $booking->id,
        //     'main_service_id' => $booking->main_service_id,

        // ]);

        return $this->returnSuccessMessage([helperTrans('api.radiology center replaying added successfully')]);
    }


    public function make_replying_pharmacy(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'pharmacy' => 'nullable|array',
                'pharmacy.*.pharmacy_id' => 'required_with:pharmacy|nullable|exists:pharmacies,id',
                'pharmacy.*.medication_unit_id' => 'required_with:pharmacy|nullable|exists:medication_units,id',
                'pharmacy.*.medication_way_id' => 'required_with:pharmacy|nullable|exists:medication_ways,id',
                'pharmacy.*.note' => 'required_with:pharmacy|nullable|string',


            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replaying = ReplyingBooking::where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();

        if (!$replaying) {

            $replaying = ReplyingBooking::create([
                'booking_id' => $booking->id,
                'patient_id' => $booking->patient_id,
                'doctor_id' => $doctor->id,
                'desc' => null,
            ]);
        }

        ReplyingBookingMedical::where('booking_id', $booking->id)->where('replying_booking_id', $replaying->id)->delete();


        if ($request->pharmacy) {
            $medicalsData = $request->input('pharmacy');
            foreach ($medicalsData as $medicalData) {
                $data = [];

                $data['booking_id'] = $booking->id;
                $data['patient_id'] = $booking->patient_id;
                $data['doctor_id'] = $booking->doctor_id;
                $data['replying_booking_id'] = $replaying->id;
                $data['pharmacy_id'] = $medicalData['pharmacy_id'];
                $data['medication_unit_id'] = $medicalData['medication_unit_id'];
                $data['medication_way_id'] = $medicalData['medication_way_id'];
                $data['note'] = $medicalData['note'];

                ReplyingBookingMedical::create($data);
            }
        }


        // Notification::create([
        //     'title' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'body' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'user_type' => 'patient',
        //     'user_id' => $booking->patient_id,
        //     'type' => 'accept',
        //     'date' => date('Y-m-d'),
        //     'foreign_id' => $booking->id,
        //     'main_service_id' => $booking->main_service_id,

        // ]);

        return $this->returnSuccessMessage([helperTrans('api.pharmacy replaying added successfully')]);
    }



    public function make_replying_diagnoses(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'diagnoses' => 'required|array',
                'diagnoses.*.diagnosis_id' => 'required|exists:diagnoses,id',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replaying = ReplyingBooking::where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();

        if (!$replaying) {

            $replaying = ReplyingBooking::create([
                'booking_id' => $booking->id,
                'patient_id' => $booking->patient_id,
                'doctor_id' => $doctor->id,
                'desc' => null,
            ]);
        }

        //ReplyingBookingDiagnosis::where('booking_id', $booking->id)->where('replying_booking_id', $replaying->id)->delete();
        if ($request->diagnoses) {

            $diagnosesData = $request->input('diagnoses');
            foreach ($diagnosesData as $diagnosisData) {
                $row = ReplyingBookingDiagnosis::where('booking_id', $booking->id)->where('replying_booking_id', $replaying->id)->where('diagnosis_id', $diagnosisData['diagnosis_id'])->first();
                if ($row == null) {
                    $data = [];
                    $data['booking_id'] = $booking->id;
                    $data['patient_id'] = $booking->patient_id;
                    $data['doctor_id'] = $booking->doctor_id;
                    $data['replying_booking_id'] = $replaying->id;
                    $data['diagnosis_id'] = $diagnosisData['diagnosis_id'];
                    ReplyingBookingDiagnosis::create($data);
                }
            }
        }


        // Notification::create([
        //     'title' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'body' => helperTrans('api.Doctor Replying For Your Booking'),
        //     'user_type' => 'patient',
        //     'user_id' => $booking->patient_id,
        //     'type' => 'accept',
        //     'date' => date('Y-m-d'),
        //     'foreign_id' => $booking->id,
        //     'main_service_id' => $booking->main_service_id,

        // ]);

        return $this->returnSuccessMessage([helperTrans('api.diagnoses replaying added successfully')]);
    }

    public function delete_replying_diagnoses(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'diagnosis_id' => 'required|exists:diagnoses,id',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replaying = ReplyingBooking::where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();

        if (!$replaying) {

            $replaying = ReplyingBooking::create([
                'booking_id' => $booking->id,
                'patient_id' => $booking->patient_id,
                'doctor_id' => $doctor->id,
                'desc' => null,
            ]);
        }

        $delete_diagnosis = ReplyingBookingDiagnosis::where('booking_id', $booking->id)->where('replying_booking_id', $replaying->id)->where('diagnosis_id', $request->diagnosis_id)->first();
        if ($delete_diagnosis == null) {
            return $this->returnSuccessMessage([helperTrans('api.This diagnosis does not exist')]);
        }
        return $this->returnSuccessMessage([helperTrans('api.diagnoses replaying deleted successfully')]);
    }


    public function show_replying_laboratory(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replay = ReplyingBooking::with(['replyingBookingAnalysis'])->where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();

        if (!$replay) {

            return $this->returnError([helperTrans('api.Main Replaying not found')]);
        }


        return $this->returnData(ReplayingBookingResource::make($replay), [helperTrans('api.Booking Replay Data')], 200);
    }


    public function show_replying_pharmacy(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replay = ReplyingBooking::with(['replyingBookingMedicals'])->where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();


        if (!$replay) {

            return $this->returnError([helperTrans('api.Main Replaying not found')]);
        }


        return $this->returnData(ReplayingBookingResource::make($replay), [helperTrans('api.Booking Replay Data')], 200);
    }

    public function show_replying_radiology_center(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replay = ReplyingBooking::with(['replyingBookingRadiology'])->where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();

        if (!$replay) {

            return $this->returnError([helperTrans('api.Main Replaying not found')]);
        }


        return $this->returnData(ReplayingBookingResource::make($replay), [helperTrans('api.Booking Replay Data')], 200);
    }

    public function show_replying_diagnoses(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();

        $booking = Booking::where('status', 'active')->where('doctor_id', $doctor->id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replay = ReplyingBooking::with(['replyingBookingDiagnoses'])->where('booking_id', $request->booking_id)->where('patient_id', $booking->patient_id)->where('doctor_id', $doctor->id)->first();


        if (!$replay) {

            return $this->returnError([helperTrans('api.Main Replaying not found')]);
        }


        return $this->returnData(ReplayingBookingResource::make($replay), [helperTrans('api.Booking Replay Data')], 200);
    }

    public function show_replying(Request $request)
    {


        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => ['required', new ExistsInBothBookingTables()],

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        // $doctor = auth('doctor')->user();
        // $patient = auth('patient')->user();



        $booking = Booking::whereIn('status', ['complete', 'reappoint', 'referral', 'specialized'])->find($request->booking_id);
        if (!$booking) {
            $booking = SpecializationBooking::where('status', 'complete')->find($request->booking_id);
        }

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replay = ReplyingBooking::where('booking_id', $request->booking_id)->first();


        if (!$replay) {

            return $this->returnError([helperTrans('api.Main Replaying not found')]);
        }


        return $this->returnData(ReplayingBookingResource::make($replay), [helperTrans('api.Booking Replay Data')], 200);
    }

    public function end_booking(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();
        $booking = Booking::where('doctor_id', $doctor->id)->find($request->booking_id);
        if (!$booking) {
            return $this->returnError([helperTrans('api.Booking not Found')]);
        }

        $booking->update([
            'status' => 'complete',
        ]);

        return $this->returnSuccessMessage([helperTrans('api.booking completed')]);
    }


    public function make_agora_room(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'type' => 'required|in:audio,video',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $doctor = auth('doctor')->user();
        $booking = Booking::where('doctor_id', $doctor->id)->where('status', 'active')->find($request->booking_id);
        if (!$booking) {
            return $this->returnError([helperTrans('api.Booking not Found')]);
        }

        $room = AgoraRoom::where('doctor_id', $doctor->id)->where('booking_id', $booking->id)->where('patient_id', $booking->patient_id)->where('name', '!=', null)->first();


        if (!$room) {
            $name = $doctor->id . $booking->patient_id . strtotime(now());
            $room = AgoraRoom::create([
                'booking_id' => $booking->id,
                'patient_id' => $booking->patient_id,
                'doctor_id' => $doctor->id,
                'name' => $name,
            ]);
        }

        $body = helperTrans('api.you receive ') . $request->type . 'from Doctor';
        $parent_ides = [$booking->patient_id];
        $notificationObject = [
            'title' => helperTrans('api.Room Notification'),
            'body' => $body,
            'type' => $request->type,
            'name' => $room->name,
            'foreign_id' => $booking->id,
        ];
        //$this->sendFCMNotification($parent_ides, 'patient', $notificationObject);
        FacadesNotification::send(Patient::find($booking->patient_id), new PushFireBaseNotification($notificationObject));

        return $this->returnData(AgoraRoomResource::make($room), [helperTrans('api.Room Data')], 200);
    }


    public function select_providers(Request $request)
    {

        $selectProviders = SelectProvider::get();

        return $this->returnData(SelectProviderResource::collection($selectProviders), [helperTrans('api.Select Providers Data')], 200);
    }


    public function online_doctors(Request $request)
    {

        $doctor = auth('doctor')->user();

        $mainService = MainService::where('slug', 'general-consultation')->first();
        if (!$mainService) {

            return  $this->returnErrorNotFound([helperTrans('api.Main Service Not Found')]);
        }
        $category_ides = Category::where('main_service_id', $mainService->id)->pluck('id')->toArray();
        $online_doctor_ides = FirebaseToken::where('user_type', 'doctor')->pluck('user_id')->toArray();
        $online_doctors = Doctor::whereIn('id', $online_doctor_ides)->where('id', '!=', $doctor->id)->where('status', true)->get();
        //dd($online_doctors);
        return $this->returnData(DoctorResource::collection($online_doctors), [helperTrans('api.online Doctors')]);
    }

    public function book_follow_up(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'day' => 'required|date',
                'time' => 'required|date_format:H:i',
                'desc' => 'required'
            ],
            []
        );

        if ($validator->fails()) {
            return $this->returnErrorValidation($validator->errors(), 403);
        }

        $doctor = auth('doctor')->user();

        if (!$doctor) {
            return $this->returnError([helperTrans('api.Account Not Found')]);
        }

        $booking = Booking::where('id', $request->booking_id)->where('doctor_id', $doctor->id)->where('status', 'active')->first();

        // $mainService = MainService::where('slug', 'general-consultation')->first();

        if (!$booking) {
            return  $this->returnErrorNotFound([helperTrans('api.Booking Not Found')]);
        }

        // create a referral unique code
        $transfer_ref = Carbon::now()->format('YmdHis') . Str::random(5);

        if (!$booking->update([
            'status' => 'reappoint',
            'tran_ref' => $transfer_ref
        ])) {
            return $this->returnError([helperTrans('api.Failed to book a follow up')]);
        };
        // return $transfer_ref;

        unset($booking->id, $booking->created_at, $booking->updated_at);


        $booking->desc = $request->desc;
        $booking->day = $request->day;
        $booking->time = $request->time;
        $booking->status = 'follow up';
        $booking->tran_ref = null;
        $booking->referral_code = $transfer_ref;

        $new_booking = Booking::create($booking->toArray());
        $new_booking->id = getNextSharedId();
        $new_booking->save();

        if (!$new_booking) {
            return $this->returnError([helperTrans('api.Failed to create new booking')]);
        }

        return  $this->returnData(BookingResource::make($new_booking), [helperTrans('api.Follow up booked successfully')], 200);
    }

    public function complete_bookings(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'patient_id' => 'required|exists:patients,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $bookings = Booking::with(['doctor.specialization', 'docs', 'mainService'])->where('patient_id', $request->patient_id)->where('status', 'complete')->get();


        return  $this->returnData(BookingResource::collection($bookings), [helperTrans('api.Bookings Data')], 200);
    }

    public function booking_replay_by_patient_id(Request $request)
    {


        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'patient_id' => 'required|exists:patients,id',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }



        $booking = Booking::where('patient_id', $request->patient_id)->find($request->booking_id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $replay = ReplyingBooking::with(['doctor', 'replyingBookingAnalysis', 'replyingBookingMedicals', 'replyingBookingRadiology', 'replyingBookingDiagnoses'])->where('booking_id', $booking->id)->first();

        if (!$replay) {
            return $this->returnError([helperTrans('api.There is No Replaying')]);
        }


        return $this->returnData(ReplayingBookingResource::make($replay), [helperTrans('api.Booking Replay Data')], 200);
    }

    public function show_replying_pdf(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => ['required', new ExistsInBothBookingTables()],
            ],
            []
        );

        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $booking = Booking::find($request->booking_id);

        if (!$booking) {
            $booking = SpecializationBooking::find($request->booking_id);
            if (!$booking) {
                return $this->returnError([helperTrans('api.booking not found')]);
            }
        }

        $replay = ReplyingBooking::with(['doctor', 'replyingBookingAnalysis', 'replyingBookingMedicals', 'replyingBookingRadiology', 'replyingBookingDiagnoses'])->where('booking_id', $booking->id)->first();

        if (!$replay) {
            return $this->returnError([helperTrans('api.There is No Replaying')]);
        }

        $data = ['replay' => $replay, 'booking' => $booking, 'type' => 'diagnoses'];

        // Log::info(view('Pdf.reply', $data)->render());

        // return Browsershot::html(view('Pdf.reply', $data)->render())
        //     ->format('A4')->save('example.pdf');

        // return view('Pdf.reply', $data);

        if (!View::exists('Pdf.reply')) {
            Log::error("View 'Pdf.reply' not found.");
            return $this->returnError(['Server error: PDF template missing.']);
        }

        $html = view('Pdf.reply', $data)->render();

        $pdfDirectory = storage_path('app/public/prescriptions');
        if (!file_exists($pdfDirectory)) {
            mkdir($pdfDirectory, 0775, true); // Create directory if it doesn't exist
        }
        $pdfPath = $pdfDirectory . '/example-' . $booking->id . '-' . time() . '.pdf';


        Browsershot::html($html)
            ->format('A4')
            ->margins(10, 10, 10, 10, 'mm') // Set margins explicitly (top, right, bottom, left)
            ->showBackground() // Important for background colors and images
            // ->setOption('args', ['--no-sandbox', '--disable-setuid-sandbox']) // Uncomment if running in restricted environments like Docker and understand security implications
            // ->setDelay(1000) // Add a delay in milliseconds if assets need more time to load
            // ->waitUntilNetworkIdle() // Alternative to delay, waits for network activity to cease
            ->save($pdfPath);

        $downloadFilename = 'prescription-' . $booking->id . '.pdf';
        return response()->download($pdfPath, $downloadFilename);
    }
}
