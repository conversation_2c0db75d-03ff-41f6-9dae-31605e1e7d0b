<?php

namespace App\Http\Controllers\Api\V1\Patient;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Http\Resources\RelativeResource;
use App\Http\Traits\Api_Trait;
use App\Models\Patient;
use App\Models\PatientSubscribe;
use App\Models\Relative;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class RelativeController extends Controller
{
    use Api_Trait;
    //
    public function relatives(Request $request)
    {
        $patient = auth('patient')->user();
        $relatives = Relative::with(['city', 'nationality', 'country'])->where('patient_id', $patient->id)->get();
        return $this->returnData(RelativeResource::collection($relatives), [helperTrans('api.Relatives Data')]);
    }
    public function add_relative(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'type' => 'required',
                'gender' => 'required|in:male,female',
                'is_alcoholic' => 'required|in:0,1',
                'is_smoking' => 'required|in:0,1',
                'birth_date' => 'required|date',
                'name' => 'required|string',
                'id_number' => 'nullable|unique:relatives,id_number',
                'card_id' => 'nullable|unique:relatives,card_id',


                'country_id' => 'required|exists:nationalities,id',
                'city_id' => 'required|exists:cities,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();
        $existCardId = Patient::where('card_id', $request->card_id)->exists();
        // throw new DoctoriaException($existCardId);
        if ($request->card_id && !$existCardId) return $this->returnErrorValidation('This Card Number Already Not Exist', 403);
        if($patient->subscribes()?->first()?->package?->code==10){
            return $this->returnErrorValidation('Max Number of Relatives Reached', 403);
        }
        $row = Relative::create([
            'type' => $request->type,
            'card_id' => $request->card_id,
            'gender' => $request->gender,
            'is_alcoholic' => $request->is_alcoholic,
            'is_smoking' => $request->is_smoking,
            'birth_date' => $request->birth_date,
            'first_name' => $request->name,
            'last_name' => $request->last_name,
            'id_number' => $request->id_number,
            'address' => $request->address,
            'nationality_id' => $request->nationality_id,
            'country_id' => $request->country_id,
            'city_id' => $request->city_id,
            'patient_id' => $patient->id,
        ]);

        if($request->card_id && $existCardId){
            $active_subscibe= PatientSubscribe::where('patient_id', $patient->id)->where('status', 'active')->first();
            if(!$active_subscibe){
                $expire_subscibe= PatientSubscribe::where('patient_id', $patient->id)->where('status', 'expire')->first();
                $expire_subscibe->update([
                    'status' => 'active'
                ]);
            }

        }

        $relative = Relative::with(['city', 'nationality', 'country'])->where('patient_id', $patient->id)->find($row->id);


        return $this->returnData(RelativeResource::make($relative), [helperTrans('api.Relative added successfully')]);
    }

    public function update_relative(Request $request, $relative_id)
    {
        $validator = Validator::make(
            $request->all(),
            [

                // 'relative_id' => 'required|exists:relatives,id',

                'type' => 'nullable',
                'gender' => 'in:male,female',
                'is_alcoholic' => 'in:0,1',
                'is_smoking' => 'in:0,1',
                'birth_date' => 'date',
                'first_name' => 'string',
                'last_name' => 'string',
                'id_number' => 'unique:relatives,id_number,' . $request->relative_id,
                'card_id' => 'nullable|unique:relatives,card_id,' . $request->relative_id,
                'address' => 'string',
                'nationality_id' => 'exists:nationalities,id',
                'country_id' => 'exists:nationalities,id',
                'city_id' => 'exists:cities,id',
                'relative_id' => 'exists:relatives,id',



            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();

        $relative = Relative::where('patient_id', $patient->id)->where('id', $request->relative_id)->first();

        if (!$relative) {
            return $this->returnError([helperTrans('api.relative not found')]);
        }


        $data = $validator->validated();
        $data['first_name'] = $request->name ?? $data['first_name'];

        $relative->update($data);
        // $relative->update([
        //     'type' => $request->type,
        //     'gender' => $request->gender,
        //     'is_alcoholic' => $request->is_alcoholic,
        //     'is_smoking' => $request->is_smoking,
        //     'birth_date' => $request->birth_date,
        //     'first_name' => $request->first_name,
        //     'last_name' => $request->last_name,
        //     'id_number' => $request->id_number,
        //     'address' => $request->address,
        //     'nationality_id' => $request->nationality_id,
        //     'country_id' => $request->country_id,
        //     'city_id' => $request->city_id,

        // ]);

        $relative = Relative::with(['city', 'nationality', 'country'])->where('patient_id', $patient->id)->find($request->relative_id);


        return $this->returnData(RelativeResource::make($relative), [helperTrans('api.Relative updated successfully')]);
    }

    public function delete_relative(Request $request)
    {

        // $validator = Validator::make(
        //     $request->all(),
        //     [
        //         'relative_id' => 'required|exists:relatives,id',

        //     ],
        //     []
        // );
        // if ($validator->fails()) {
        //     return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        // }


        $patient = auth('patient')->user();

        $relative = Relative::where('patient_id', $patient->id)->find($request->relative_id);

        if (!$relative) {
            return   $this->returnError([helperTrans('api.relative not found')]);
        }
        $relative->delete();

        return $this->returnData(null, [helperTrans('api.Relative deleted successfully')]);
    }
}
