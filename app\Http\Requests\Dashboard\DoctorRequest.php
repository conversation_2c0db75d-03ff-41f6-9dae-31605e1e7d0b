<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class DoctorRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    public function store()
    {
        return [
            'code' => [
                'required',
                'string',
                'max:50',
                Rule::unique('doctors', 'code')
            ],
            'nickname_ar' => 'required|string|max:255',
            'nickname_en' => 'required|string|max:255',
            'name_ar' => 'required|string|max:255',
            'name_en' => 'required|string|max:255',
            'gender' => 'required|in:male,female',
            'specialization_id' => 'required|integer|exists:specializations,id',
            'sub_specialization_id' => 'nullable|integer|exists:specializations,id',
            'doctor_level_id' => 'nullable|integer|exists:doctor_levels,id',
            'governorate_id' => 'required|integer|exists:governorates,id',
            'city_id' => 'required|integer|exists:cities,id',
            'doctor_type' => 'nullable|string|in:provider,hospital,clinic',
            'longitude' => 'nullable|numeric',
            'latitude' => 'nullable|numeric',
            'experience_years' => 'nullable|integer|min:0|max:50',
            'location_ar' => 'nullable|string|max:500',
            'location_en' => 'nullable|string|max:500',
            'about_ar' => 'nullable|string|max:1000',
            'about_en' => 'nullable|string|max:1000',
            'phone' => 'nullable|string|max:20',
            'email' => 'nullable|email|max:255',
        ];
    }

    public function update()
    {
        $doctorId = $this->route('doctor') ? $this->route('doctor')->id : null;

        return [
            'code' => [
                'sometimes',
                'required',
                'string',
                'max:50',
                Rule::unique('doctors', 'code')->ignore($doctorId)
            ],
            'nickname_ar' => 'sometimes|required|string|max:255',
            'nickname_en' => 'sometimes|required|string|max:255',
            'name_ar' => 'sometimes|required|string|max:255',
            'name_en' => 'sometimes|required|string|max:255',
            'gender' => 'sometimes|required|in:male,female',
            'specialization_id' => 'sometimes|required|integer|exists:specializations,id',
            'sub_specialization_id' => 'sometimes|nullable|integer|exists:specializations,id',
            'doctor_level_id' => 'sometimes|nullable|integer|exists:doctor_levels,id',
            'governorate_id' => 'sometimes|required|integer|exists:governorates,id',
            'city_id' => 'sometimes|required|integer|exists:cities,id',
            'doctor_type' => 'sometimes|nullable|string|in:provider,hospital,clinic',
            'longitude' => 'sometimes|nullable|numeric',
            'latitude' => 'sometimes|nullable|numeric',
            'experience_years' => 'sometimes|nullable|integer|min:0|max:50',
            'location_ar' => 'sometimes|nullable|string|max:500',
            'location_en' => 'sometimes|nullable|string|max:500',
            'about_ar' => 'sometimes|nullable|string|max:1000',
            'about_en' => 'sometimes|nullable|string|max:1000',
            'phone' => 'sometimes|nullable|string|max:20',
            'email' => 'sometimes|nullable|email|max:255',
        ];
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        if ($this->isMethod('POST')) {
            return $this->store();
        }
        if ($this->isMethod('PUT') || $this->isMethod('PATCH')) {
            return $this->update();
        }

        return [];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'code.required' => 'Doctor code is required',
            'code.unique' => 'Doctor code must be unique',
            'nickname_ar.required' => 'Arabic nickname is required',
            'nickname_en.required' => 'English nickname is required',
            'name_ar.required' => 'Arabic Doctor name is required',
            'name_en.required' => 'English Doctor name is required',
            'gender.required' => 'Gender is required',
            'gender.in' => 'Gender must be either male or female',
            'specialization_id.required' => 'Specialization is required',
            'specialization_id.exists' => 'Selected specialization does not exist',
            'governorate_id.required' => 'Governorate is required',
            'governorate_id.exists' => 'Selected governorate does not exist',
            'city_id.required' => 'City is required',
            'city_id.exists' => 'Selected city does not exist',
            'sub_specialization_id.exists' => 'Selected sub specialization does not exist',
            'doctor_level_id.exists' => 'Selected doctor level does not exist',
            'doctor_type.in' => 'Doctor type must be provider, hospital, or clinic',
            'longitude.numeric' => 'Longitude must be a valid number',
            'latitude.numeric' => 'Latitude must be a valid number',
            'experience_years.integer' => 'Experience years must be a valid number',
            'experience_years.min' => 'Experience years cannot be negative',
            'experience_years.max' => 'Experience years cannot exceed 50',
            'location_ar.max' => 'Arabic location cannot exceed 500 characters',
            'location_en.max' => 'English location cannot exceed 500 characters',
            'about_ar.max' => 'Arabic about cannot exceed 1000 characters',
            'about_en.max' => 'English about cannot exceed 1000 characters',
            'phone.max' => 'Phone number cannot exceed 20 characters',
            'email.email' => 'Email must be a valid email address',
            'email.max' => 'Email cannot exceed 255 characters',
        ];
    }
}
