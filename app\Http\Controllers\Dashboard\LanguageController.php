<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\CityRequest;
use App\Http\Requests\Dashboard\GovernorateRequest;
use App\Http\Requests\Dashboard\LanguageRequest;
use App\Http\Resources\CityResource;
use App\Http\Resources\GovernorateResource;
use App\Http\Resources\LanguageResource;
use App\Http\Traits\Api_Trait;
use App\Models\City;
use App\Models\Governorate;
use App\Models\Language;

class LanguageController extends Controller
{

    use Api_Trait;

    public function index()
    {
        $languages = Language::latest()->get();

        return $this->returnData(
            LanguageResource::collection($languages),
            [helperTrans('api.languages data')],
            200
        );
    }

    public function show(Language $language)
    {
        return $this->returnData(
            new LanguageResource($language),
            [helperTrans('api.language show')],
            200
        );
    }

    public function store(LanguageRequest $request)
    {
        $language = Language::create($request->validated());

        return $this->returnData(
            new LanguageResource($language),
            [helperTrans('api.language created')],
            201
        );
    }

    public function update(LanguageRequest $request, Language $language)
    {

        $language->update($request->validated());

        return $this->returnData(
            new LanguageResource($language),
            [helperTrans('api.language updated')],
            200
        );
    }

    public function destroy(Language $language)
    {
        $language->delete();

        return response()->json([
            'status' => true,
            helperTrans('api.language deleted'),
            200
        ]);
    }
}
