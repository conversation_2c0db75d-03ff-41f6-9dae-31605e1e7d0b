<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractWallet extends Model
{
    use HasFactory;
    protected $table = 'contract_wallets';
    protected $fillable = [
        'contract_id',
        'wallet_name',
        'wallet_mobile_number',
        'wallet_cheque_name',
    ];
    protected $guarded = [];
    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }
}
