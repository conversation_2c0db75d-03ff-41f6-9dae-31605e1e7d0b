<?php

namespace App\Http\Requests\Api\V2;

use App\Http\Requests\Api\BaseFormRequest;

class PatientRequest extends BaseFormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $patientId = auth('patient')->id();
        return [
            'name' => 'required|string|max:255',
            'nickname' => 'nullable|string|max:255',
            'email' => 'nullable|email|max:255|unique:patients,email,' . $patientId,
            'phone' => 'nullable|string|max:20|unique:patients,phone,' . $patientId . '|unique:doctors,phone',
            'gender' => 'nullable|in:male,female',
            'postcode' => 'nullable|string|max:20',
            'refer_code' => 'nullable|string|max:255|unique:patients,refer_code,' . $patientId,
            'address' => 'nullable|string',
            'location' => 'nullable|string|max:300',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,svg|max:2048',
            'nationality_id' => 'nullable|exists:nationalities,id',
            'city_id' => 'nullable|exists:cities,id',
            'marital_status' => 'nullable|string|max:255',
            'occupation' => 'nullable|string|max:255',
            'n_children' => 'nullable|integer|min:0',
            'residence' => 'nullable|string|max:255',
            'is_smoking' => 'nullable|boolean',
            'is_alcoholic' => 'nullable|boolean',
            'athlete' => 'nullable|boolean',
        ];
    }
}
