<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Doctor;
use App\Models\DoctorBranch;
use App\Models\SpecializationBooking;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;

class SpecializationBookingController extends Controller
{
    public function index()
    {
        return view('Admin.CRUDS.specialization_booking.index');
    }

    public function getData()
    {
        $bookings = SpecializationBooking::with(['patient', 'doctor'])->get();

        return DataTables::of($bookings)
            ->setRowAttr([
                'style' => function ($row) {
                    return $row->read_at == null ? 'background-color: #f8d7da;' : '';
                }
            ])
            ->addColumn('mark_read', function ($row) {
                if ($row->read_at)
                    return '<button class="btn btn-sm btn-secondary disabled">Readed</button>';
                else
                    return '<button class="btn btn-sm btn-primary mark-read" data-id="' . $row->id . '">Mark as Read</button>';
            })
            ->addColumn('patient', function ($row) {
                return $row->patient ? $row->patient->name : '-';
            })
            ->addColumn('patient_phone', function ($row) {
                return $row->patient ? $row->patient->phone : '-';
            })
            ->addColumn('doctor', function ($row) {
                return $row->doctor ? $row->doctor->name : '-';
            })
            ->addColumn('status', function ($row) {
                $statusClass = [
                    'pending' => 'warning',
                    'active' => 'info',
                    'complete' => 'success',
                    'cancel' => 'danger'
                ];
                return '<span class="badge bg-' . ($statusClass[$row->status] ?? 'secondary') . '">' . $row->status . '</span>';
            })
            ->addColumn('visit', function ($row) {
                return $row->visit;
            })
            ->addColumn('date', function ($row) {
                return $row->date;
            })
            ->addColumn('time', function ($row) {
                return $row->time;
            })
            ->addColumn('price', function ($row) {
                return $row->price;
            })
            ->addColumn('action', function ($row) {
                $action = '<a href="' . route('specialization_bookings.show', $row->id) . '" class="btn btn-primary btn-sm me-2"><i class="fa fa-eye"></i></a>';
                $action .= '<a href="' . route('specialization_bookings.edit', $row->id) . '" class="btn btn-primary btn-sm"><i class="fa fa-edit"></i></a>';
                return $action;
            })
            ->rawColumns(['status', 'action', 'mark_read'])
            ->make(true);
    }

    public function show($id)
    {
        $booking = SpecializationBooking::with(['patient', 'doctor', 'relative', 'replying'])->findOrFail($id);
        return view('Admin.CRUDS.specialization_booking.show', compact('booking'));
    }

    public function edit($id)
    {
        $booking = SpecializationBooking::with(['patient', 'doctor', 'relative', 'replying'])->findOrFail($id);
        $doctors = Doctor::all();
        $branches = DoctorBranch::all();
        return view('Admin.CRUDS.specialization_booking.edit', compact('booking', 'doctors', 'branches'));
    }

    public function update(Request $request, $id)
    {
        $booking = SpecializationBooking::findOrFail($id);

        $validated = $request->validate([
            'status' => 'required|in:pending,active,complete,cancel',
            'visit' => 'required|in:home,offline,online',
            'doctor_id' => 'required|exists:doctors,id',
            'branch_id' => 'nullable|exists:doctor_branches,id',
            'date' => 'required|date',
            'time' => 'required',
            'price' => 'required|numeric|min:0',
            'description' => 'nullable|string',
        ]);

        $booking->update($validated);

        return redirect()->route('specialization_bookings.index')
            ->with('success', 'Booking updated successfully');
    }

    public function getDoctorBranches($doctorId)
    {
        $doctor = Doctor::with('doctor_branch')->findOrFail($doctorId);
        return response()->json($doctor->doctor_branch);
    }

    public function markAsRead($id)
    {
        try {
            $booking = SpecializationBooking::findOrFail($id);

            if (is_null($booking->read_at)) {
                $booking->read_at = now();
                $booking->save();

                return response()->json([
                    'status' => true,
                    'message' => 'Booking marked as read successfully'
                ]);
            }

            return response()->json([
                'status' => true,
                'message' => 'Booking was already marked as read'
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'status' => false,
                'message' => $e->getMessage()
            ], 500);
        }
    }
}
