<?php

namespace App\Http\Resources\Api\V2;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DoctorStatisticsResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            "patient_count" =>  $this->patients()->count(),
            "experience_years" => $this->experience_years,
            "average_rating" =>  get_doctor_rate($this->id),
            "reviews_count" => $this->reviews->count(),
        ];
    }
}
