<?php

namespace App\Http\Resources\Api\V2;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DoctorResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     *
     */
    public function toArray(Request $request): array
    {

        $res = [
            'id' => (int)$this->id,
            'code' => $this->code ?? null,
            'city' => new CityResource($this->city),
            'government' => new GovernorateResource($this->governorate),
            'nickname' => (is_json($this->nickname) && is_array($decoded = json_decode($this->nickname, true)) && isset($decoded[$request->header('lang')]))
                ? $decoded[$request->header('lang')]
                : $this->nickname,
            'doctor_type' => $this->doctor_type,
            'branch_count' => $this->provider_branches()->count(),
            'phone' => $this->phone,
            'specialization' => new SpecializationResource($this->specialization),
            'gender' => $this->gender,
            'level' => new DoctorLevelResource($this->doctor_level),
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'image' => new_get_file($this->image, 'person'),
            'location' => $this->doctor_type != 'specialized' ?
                $this->provider_branches()?->first()['name'] ?? ''
                : (
                    (is_json($this->location) &&
                        is_array($decoded = json_decode($this->location, true)) &&
                        isset($decoded[$request->header('lang')]))
                    ? $decoded[$request->header('lang')]
                    : $this->location),
            'about' => $this->getTranslation('about', session_lang()),
            'experience_years' => $this->experience_years,
            'average_review' => get_doctor_rate($this->id),
        ];

        $res['fav'] = false;

        if (auth()->guard('patient')->check()) {
            $patient = auth()->guard('patient')->user();
            $patient_favs = $patient->fav_doctors->pluck('id')->toArray();
            $res['fav'] = in_array($this->id, $patient_favs) ? true : false;
        }

        return $res;
    }
}
