<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class GovernorateResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'name' => $this->getTranslation('name', session_lang()),
            'country_id' => $this->country_id,
            'country_name' => optional($this->country)->getTranslation('name', session_lang()),
            'nationality_id' => $this->nationality_id,
            'nationality_name' => optional($this->nationality)->getTranslation('name', session_lang()),
        ];
    }
}
