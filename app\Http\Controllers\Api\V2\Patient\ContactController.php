<?php

namespace App\Http\Controllers\Api\V2\Patient;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\Contact;
use App\Http\Resources\Api\V2\ContactResource;
use App\Http\Requests\Api\V2\ContactRequest;
use App\Http\Traits\Api_Trait;

class ContactController extends Controller
{
    use Api_Trait;
    public function index()
    {
        $contacts = Contact::where('patient_id', auth('patient')->id())->get();
        return $this->returnData(ContactResource::collection($contacts), 'Contacts retrieved successfully');
    }

    public function store(ContactRequest $request)
    {
        $validated = $request->validated();
        $contact = Contact::create([
            'patient_id' => auth('patient')->id(),
            'name' => $validated['name'],
            'email' => $validated['email'],
            'message' => $validated['message'],
        ]);
        return $this->returnData(ContactResource::make($contact), 'Contact created successfully');
    }
}
