<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\V2\DoctorLevelResource;
use App\Http\Traits\Api_Trait;
use App\Models\DoctorLevel;
use Illuminate\Http\Request;

class ExperienceController extends Controller
{

    use Api_Trait;
    public function index()
    {
        $experiences = DoctorLevel::get();
        return $this->returnData(
            DoctorLevelResource::collection($experiences),
            [helperTrans('api.experiences data')],
            200
        );
    }

    public function store(Request $request)
    {
        $experience = DoctorLevel::create([
            'name' => [
                'ar' => $request->name['ar'],
                'en' => $request->name['en']
            ]
        ]);

        return $this->returnData(
            new DoctorLevelResource($experience),
            [helperTrans('api.experience created')],
            201
        );
    }

    public function show(DoctorLevel $experience)
    {
        return $this->returnData(
            new DoctorLevelResource($experience),
            [helperTrans('api.experience show')],
            200
        );
    }

    public function update(Request $request, DoctorLevel $experience)
    {
        $experience->update([
            'name' => [
                'ar' => $request->name['ar'],
                'en' => $request->name['en']
            ]
        ]);

        return $this->returnData(
            new DoctorLevelResource($experience),
            [helperTrans('api.experience updated')],
            200
        );
    }

    public function destroy(DoctorLevel $experience)
    {
        $experience->delete();

        return response()->json([
            'status' => true,
            'message' => [helperTrans('api.experience deleted')]
        ]);
    }
}