<?php

namespace App\Http\Resources\Api\V2;

use App\Http\Resources\AdressDetailsResource;
use App\Models\DoctorBranch;
use App\Models\Governorate;
use App\Models\MainService;
use App\Models\PromoCode;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BookingStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {

        return [
            'id' => $this->id,
            'name' => $this->name,
            'booking_type_id' => $this->booking_type_id,
            'notes' => $this->notes,
            'color' => $this->color
        ];
    }
}
