<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\HospitalRequest;
use App\Http\Traits\ResponseTrait;
use App\Http\Traits\Upload_Files;
use App\Models\Governorate;
use App\Models\Hospital;
use App\Models\HospitalBranch;
use Illuminate\Http\Request;
use Yajra\DataTables\DataTables;

class HospitalBranchController extends Controller
{
    //
    use  ResponseTrait,Upload_Files;

    public function index(Request $request)
    {
        $id = $request->id;
        $admins = HospitalBranch::where('hospital_id', $id)->get();
        if ($request->ajax()) {
            $admins = HospitalBranch::where('hospital_id', $id)->get();
            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {
                    return '
                        <button class="btn rounded-pill btn-danger waves-effect waves-light delete" data-id="' . $admin->id . '">
                            <span class="svg-icon svg-icon-3">
                                <i class="las la-trash-alt"></i>
                            </span>
                        </button>
                    ';
                })
                ->editColumn('name', function ($row) {
                    return $row->name;
                })
                ->editColumn('desc', function ($row) {
                    return $row->desc;
                })
                ->editColumn('image', function ($admin) {
                    return '
                        <a data-fancybox="" href="' . get_file($admin->image) . '">
                                <img height="60px" src="' . get_file($admin->image) . '">
                            </a>
                            ';
                })
                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);


        }
        return view('Admin.CRUDS.hospital.branches.index', compact('id'));
    }


    public function create(Request $request)
    {
        $hospitals = Hospital::get();
        $id = $request->query('id');
        return view('Admin.CRUDS.hospital.branches.parts.create',compact('hospitals','id'));
    }

    public function store(HospitalRequest $request)
    {
        $data = $request->validationData();
        if($request->image){
            $image = json_decode($request->image, true);
            $imagedata = $image['image'] ?? null;
        }
        $branch = HospitalBranch::create([
            "hospital_id" => $request['id'],
            "name" => $request['name'],
            'desc' => $request['desc'],
            "image" => $imagedata ?? null,
            "work_from" => $request['work_from'],
            "work_to" => $request['work_to'],
            "location" => $request['location'],
            "about_us" => $request['about_us'],
            "latitude" => $request['latitude'],
            "longitude" => $request['longitude'],
            "website_link" => $request['website_link'],
            "phone" => $request['phone'],
            "governorate_id" => $request['governorate_id'],
            "type" => $request['type'],
        ]);
        return $this->addResponse();
    }


    public function show($id)
    {


        //
    }


    public function edit($id )
    {

        $hospitals = Hospital::get();
        $row=HospitalBranch::findOrFail($id);
        $governorates  = Governorate::get();
        return view('Admin.CRUDS.hospital.parts.edit', compact('row', 'hospitals','governorates'));

    }

    public function update(HospitalRequest $request, $id )
    {

        $row=Hospital::findOrFail($id);
        $data = $request->validationData();

        if ($request->image)
            $data["image"] = $this->uploadFiles('hospitals', $request->file('image'), $row->image);

        $row->update($data);


        return $this->updateResponse();

    }


    public function destroy($id)
    {
        $row = Hospital::findOrFail($id);

        if (file_exists($row->image)) {
            unlink($row->image);
        }

        $row->delete();

        return $this->deleteResponse();
    }//end fun


}
