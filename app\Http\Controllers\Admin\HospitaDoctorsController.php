<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\BranchClinicDoctor;
use Illuminate\Http\Request;
use Yajra\DataTables\Facades\DataTables;
use Maatwebsite\Excel\Facades\Excel;
use App\Imports\HospitalDoctorImport;

class HospitaDoctorsController extends Controller
{


    public function index(Request $request)
    {
        if ($request->ajax()) {
            $clinic_doctors = BranchClinicDoctor::with(['branchClinic', 'doctor', 'day'])->latest();

            return DataTables::of($clinic_doctors)
                ->addColumn('doctor', function ($clinic_doctor) {
                    return json_decode($clinic_doctor->doctor?->nickname)->ar;
                })
                ->addColumn('provider_branch', function ($clinic_doctor) {
                    return $clinic_doctor->branchClinic?->branch?->name;
                }) ->addColumn('provider_branch_governorate', function ($clinic_doctor) {
                    return $clinic_doctor->branchClinic?->branch?->governorate?->name;
                })
                ->addColumn('provider_branch_city', function ($clinic_doctor) {
                    return $clinic_doctor->branchClinic?->branch?->city?->name;
                })
                ->addColumn('provider_branch_location', function ($clinic_doctor) {
                    return $clinic_doctor->branchClinic?->branch?->location;
                })
                ->addColumn('clinic', function ($clinic_doctor) {
                    return $clinic_doctor->branchClinic?->clinic?->name;
                })->addColumn('day', function ($clinic_doctor) {
                    return $clinic_doctor->day?->day??'';
                })

                ->make(true);
        }
        return view('Admin.CRUDS.hospitalDoctors.index');
    }

    public function import_hospital_doctor (Request $request){
        $request->validate([
            'file' => 'required|mimes:xlsx,xls,csv'
        ]);

        Excel::import(new HospitalDoctorImport, $request->file('file'));

        return redirect()->back()->with('success', 'Hospital doctors imported successfully');
    }
}
