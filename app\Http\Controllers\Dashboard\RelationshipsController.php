<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\Dashboard\RelationshipRequest;
use App\Http\Resources\Dashboard\RelationshipResource;
use App\Models\Relationship;
use App\Http\Traits\Api_Trait;
use Illuminate\Http\Request;

class RelationshipsController extends Controller
{
    use Api_Trait;

    /**
     * Display a listing of relationships with filtering and pagination
     */
    public function index(Request $request)
    {
        $relationships = Relationship::query()
            ->when($request->search, function ($query) use ($request) {
                $query->where(function ($q) use ($request) {
                    $q->where('name', 'like', '%' . $request->search . '%')
                        ->orWhere('code', 'like', '%' . $request->search . '%');
                });
            })
            ->when($request->name, function ($query) use ($request) {
                $query->where('name', 'like', '%' . $request->name . '%');
            })
            ->when($request->code, function ($query) use ($request) {
                $query->where('code', 'like', '%' . $request->code . '%');
            })
            ->when($request->has_code !== null, function ($query) use ($request) {
                if ($request->has_code) {
                    $query->whereNotNull('code')->where('code', '!=', '');
                } else {
                    $query->where(function ($q) {
                        $q->whereNull('code')->orWhere('code', '');
                    });
                }
            })
            ->when($request->is_used !== null, function ($query) use ($request) {
                if ($request->is_used) {
                    $query->has('relatives');
                } else {
                    $query->doesntHave('relatives');
                }
            })
            ->withCount('relatives')
            ->orderBy($request->sort_by ?? 'name', $request->sort_direction ?? 'asc')
            ->paginate($request->per_page ?? 25);

        return $this->paginatedResponse(
            RelationshipResource::collection($relationships),
            helperTrans('api.Relationships Data'),
            200
        );
    }

    /**
     * Store a newly created relationship
     */
    public function store(RelationshipRequest $request)
    {
        $relationship = Relationship::create($request->validated());

        return $this->returnData(
            new RelationshipResource($relationship),
            [helperTrans('api.Relationship Created Successfully')],
            201
        );
    }

    /**
     * Display the specified relationship
     */
    public function show($id)
    {
        $relationship = Relationship::withCount('relatives')->find($id);
        if (!$relationship) {
            return $this->returnError(helperTrans('api.Relationship Not Found'), 404);
        }

        return $this->returnData(
            new RelationshipResource($relationship),
            [helperTrans('api.Relationship Data')],
            200
        );
    }

    /**
     * Update the specified relationship
     */
    public function update(RelationshipRequest $request, $id)
    {
        $relationship = Relationship::find($id);
        if (!$relationship) {
            return $this->returnError(helperTrans('api.Relationship Not Found'), 404);
        }

        $relationship->update($request->validated());

        return $this->returnData(
            new RelationshipResource($relationship),
            [helperTrans('api.Relationship Updated Successfully')],
            200
        );
    }

    /**
     * Remove the specified relationship
     */
    public function destroy($id)
    {
        $relationship = Relationship::find($id);
        if (!$relationship) {
            return $this->returnError(helperTrans('api.Relationship Not Found'), 404);
        }

        // Check if relationship is being used by relatives
        if ($relationship->relatives()->count() > 0) {
            return $this->returnError(
                helperTrans('api.Cannot delete relationship as it is being used by relatives'),
                422
            );
        }

        $relationship->delete();

        return $this->returnSuccessMessage(
            helperTrans('api.Relationship Deleted Successfully'),
            200
        );
    }

    /**
     * Get relationships statistics
     */
    public function statistics()
    {
        $stats = [
            'total_relationships' => Relationship::count(),
            'relationships_with_code' => Relationship::whereNotNull('code')->where('code', '!=', '')->count(),
            'relationships_without_code' => Relationship::where(function ($query) {
                $query->whereNull('code')->orWhere('code', '');
            })->count(),
            'used_relationships' => Relationship::has('relatives')->count(),
            'unused_relationships' => Relationship::doesntHave('relatives')->count(),
            'most_used_relationships' => Relationship::withCount('relatives')
                ->orderBy('relatives_count', 'desc')
                ->limit(5)
                ->get()
                ->map(function ($relationship) {
                    return [
                        'id' => $relationship->id,
                        'name' => $relationship->name,
                        'code' => $relationship->code,
                        'relatives_count' => $relationship->relatives_count,
                    ];
                }),
        ];

        return $this->returnData(
            $stats,
            [helperTrans('api.Relationships Statistics')],
            200
        );
    }
}
