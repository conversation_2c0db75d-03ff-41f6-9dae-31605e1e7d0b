APP_NAME=Doctoria
APP_KEY=base64:BHis6h+yZDzCd2SXEcENkFoffyJSn12iTFf8IH/g+T0=
APP_DEBUG=true
APP_ENV=local
# APP_URL=http://doctoriaplus.com
APP_URL=http://127.0.0.1:8000

LOG_CHANNEL=daily
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=database
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=
MAIL_PORT=
MAIL_USERNAME=
MAIL_PASSWORD=
MAIL_ENCRYPTION=tls
MAIL_FROM_ADDRESS=
MAIL_FROM_NAME="${APP_NAME}"

AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=
AWS_DEFAULT_REGION=us-east-1
AWS_BUCKET=
AWS_USE_PATH_STYLE_ENDPOINT=false

PUSHER_APP_ID=
PUSHER_APP_KEY=
PUSHER_APP_SECRET=
PUSHER_HOST=
PUSHER_PORT=443
PUSHER_SCHEME=https
PUSHER_APP_CLUSTER=mt1

VITE_APP_NAME="${APP_NAME}"
VITE_PUSHER_APP_KEY="${PUSHER_APP_KEY}"
VITE_PUSHER_HOST="${PUSHER_HOST}"
VITE_PUSHER_PORT="${PUSHER_PORT}"
VITE_PUSHER_SCHEME="${PUSHER_SCHEME}"
VITE_PUSHER_APP_CLUSTER="${PUSHER_APP_CLUSTER}"

paytabs_profile_id=
paytabs_server_key=
paytabs_currency=EGP
paytabs_region=EGY


FAWRY_MERCHANT_CODE=
FAWRY_SECURITY_KEY=
FAWRY_SANDBOX=

PAYPAL_MODE=sandbox
PAYPAL_SANDBOX_API_USERNAME=
PAYPAL_SANDBOX_API_PASSWORD=
PAYPAL_SANDBOX_API_SECRET=
PAYPAL_SANDBOX_API_CERTIFICATE=
PAYPAL_CURRENCY=USD

ZOOM_ACCOUNT_ID=
ZOOM_CLIENT_KEY=
ZOOM_CLIENT_SECRET=
JWT_SECRET=
TIMEZONE=


FIREBASE_CREDENTIALS=
FIREBASE_PROJECT_ID=


#PayPal API Mode
# Values: sandbox or live (Default: live)
PAYPAL_MODE=sandbox

#PayPal Setting & API Credentials - sandbox
PAYPAL_SANDBOX_CLIENT_ID=
PAYPAL_SANDBOX_CLIENT_SECRET=

#PayPal Setting & API Credentials - live
#PAYPAL_LIVE_CLIENT_ID=
#PAYPAL_LIVE_CLIENT_SECRET=


################# Sms #########################
COMMUNITY_ADS_USERNAME=
COMMUNITY_ADS_PASSWORD=
COMMUNITY_ADS_SMSID=
COMMUNITY_ADS_API_URL=



WHATSAPP_API_URL=
WHATSAPP_API_TOKEN=
WHATSAPP_TEMPLATE=