<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Resources\Json\JsonResource;

class PriceListServiceCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'service_category_id' => $this->service_category->id,
            'service_category_name_ar' => $this->service_category->getTranslation('name', 'ar'),
            'service_category_name_en' => $this->service_category->getTranslation('name', 'en'),
            // 'sub_categories' => $this->service_category->subCategories->map(function ($sub_category) {
            //     return [
            //         'id' => $sub_category->id,
            //         'name_ar' => $sub_category->getTranslation('name', 'ar'),
            //         'name_en' => $sub_category->getTranslation('name', 'en'),
            //     ];
            // }),
            // 'services' => $this->price_list->services->map(function ($service) {
            //     return [
            //         'id' => $service->id,
            //         'name_ar' => $service->getTranslation('name', 'ar'),
            //         'name_en' => $service->getTranslation('name', 'en'),
            //     ];
            // }),
            // 'sub_categories' => ServiceSubCategoryResource::collection($this->service_category->subCategories),
        ];
    }
}
