<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\ResponseTrait;
use App\Http\Traits\Upload_Files;
use App\Models\Delegate;
use App\Models\DoctorContract;
use App\Models\Hospital;
use App\Models\HospitalConstact;
use App\Models\ProviderCategory;
use App\Models\RadiologyCenterContracts;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Yajra\DataTables\DataTables;


class SpecializCenterController extends Controller
{

    use  ResponseTrait,Upload_Files;

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $admins = Hospital::query()->where('type','specialize_center')->latest();
            
            return DataTables::of($admins)
                ->addColumn('action', function ($admin) {
                    $edit = '';
                    $delete = '';


                    return '
                    <button ' . $edit . '  class="editBtn btn rounded-pill btn-primary waves-effect waves-light" data-id="' . $admin->id . '"
                        <span class="svg-icon svg-icon-3">
                            <span class="svg-icon svg-icon-3">
                                <i class="las la-edit"></i>
                            </span>
                        </span>
                    </button>
                    <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete" data-id="' . $admin->id . '">
                        <span class="svg-icon svg-icon-3">
                            <span class="svg-icon svg-icon-3">
                                <i class="las la-trash-alt"></i>
                            </span>
                        </span>
                    </button>
                    <a  '.$edit. ' href="'. route('hospital_branches.index', ['id'=>$admin->id]) .'" class="btn rounded-pill btn-primary waves-effect waves-light">Braches</a>
              ';



                })




                ->editColumn('name', function ($row) {
                    return $row->name;
                })

                ->editColumn('desc', function ($row) {
                    return $row->desc;
                })

                ->editColumn('image', function ($admin) {
                    return '
                              <a data-fancybox="" href="' . get_file($admin->image) . '">
                                <img height="60px" src="' . get_file($admin->image) . '">
                            </a>
                             ';
                })



                ->editColumn('created_at', function ($admin) {
                    return date('Y/m/d', strtotime($admin->created_at));
                })
                ->escapeColumns([])
                ->make(true);


        }
        $providerName ='SpecializeCenter';
        return view('Admin.CRUDS.hospital.index',compact('providerName'));
    }
    public function store(Request $request)
    {
        DB::beginTransaction();
        try{
            $data = $request->validationData();
            $workingHours = [];
            if ($request->has('working_days')) {
                foreach ($request->working_days as $day) {
                    $workingHours[$day] = [
                        'start_time' => $request->input('start_time')[$day] ?? null,
                        'end_time' => $request->input('end_time')[$day] ?? null,
                    ];
                }
            }
            $exceptedColumn = [
                'work_time',
                'start_time',
                'end_time',
                'working_days',
                'contract_start_date',
                'contract_end_date',
                'attach_contract',
                'attach_documents',
                'attach_price_list',
                'price_list_discount',
                'contract_note',
                'bank_name',
                'bank_account_number',
                'bank_iban',
                'bank_swift_code',
                'bank_branch_bank',
                'bank_mobile_number',
                'e_wallet_name',
                'e_wallet_mobile_number',
                'instapay_mobile_number',
                'instapay_email',
                'cheque_name',
                'working_hours',
                "price_list_hospital_consulation",
                "price_list_hospital_outpatient",
                "price_list_hospital_inpatient",
                "price_list_hospital_laboratory" ,
                "price_list_hospital_radiology" ,
                "price_list_hospital_discount_other" ,
                "price_list_doctor_visit",
                "price_list_nurse_visit",
                "price_list_hospital_other",
                "nickname_hospital_doctor" ,
                "specialization_hospital_id" ,
                "sub_specialization_id,",
                "price_list_xray",
                "price_list_ct",
                "price_list_mri",
                "price_list_dental",
                "price_list_glasses",
                "price_list_frame",
                "price_list_sessions",

                'clamis_due_date',
                'admin_fees',
                'name_1',
                'email_1',
                'phone_1',
                'name_2',
                'email_2',
                'phone_2',
            ];
            unset(
                $data['category_id'],
                $data['nickname'],
                $data['price_list_hematology'],
                $data['price_list_chemistry'],
                $data['price_list_hormones'],
                $data['price_list_serology'],
                $data['home_care_gross_price'],
                $data['home_care_discount'],
                $data['home_care_net_price'],
                $data['price_list_immunology'],
                $data['price_list_drug'],
                $data['price_list_microbiology'],
                $data['price_list_pathology'],
                $data['price_list_other'],
                $data['service_clinic_gross_price'],
                $data['service_clinic_discount'],
                $data['service_clinic_net_price'],
                $data['service_online_gross_price'],
                $data['service_online_discount'],
                $data['service_online_net_price'],
                $data['price_list_outpatient'],
                $data['price_list_intpatient'],
                $data['price_list_consultation'],
                $data['price_list_laboratory'],
                $data['price_list_radiologe'],
                $data['price_list_exception'],
                $data['gender'],
                $data['specialization_id'],
                $data['price_list_acutemedicine_local'],
                $data['price_list_chronicmedication_local'],
                $data['price_list_cosmetics_local'],
                $data['price_list_otherservice_local'],
                $data['price_list_acutemedicine_imported'],
                $data['price_list_chronicmedication_imported'],
                $data['price_list_cosmetics_imported'],
                $data['price_list_otherservice_imported'],
                $data["price_list_xray"],
                $data["price_list_mri"],
                $data["price_list_ct"],
                $data["price_list_other"],
            );
            if ($request->image){
                $data["image"] = $this->uploadFiles('specializ_center', $request->file('image'), null);
            }
            $data['password'] = bcrypt($request->password);
            if (is_array($data['name'])) {
                $data['name'] = json_encode($data['name']);
            }
            if (is_array($data['address'])) {
                $data['address'] = json_encode($data['address']);
            }
            if (is_array($data['about'])) {
                $data['about'] = json_encode($data['about']);
            }
            $data['delegate'] = $request->has('delegate') ? 1 : 0;
            $datafinal = Arr::except($data, $exceptedColumn);
            $specialize_center = Hospital::create([
                "location" => $datafinal['address'],
                "image" => $data["image"],
                "type" => "specialize_center",
                "provider_type" => "specialize_center",
                "name" => $datafinal['name'],
                "email" => $datafinal['email'],
                "phone" => $datafinal['phone'],
                "private_number" => $datafinal['private_number'],
                "password" => $datafinal['password'],
                "website" => $datafinal['website'],
                "governorate_id" => $datafinal['governorate_id'],
                "experience_id" => $datafinal['governorate_id'],
                "experience_years" => $datafinal['governorate_id'],
                "lang" => $datafinal['lang'],
                "latitude" => $datafinal['latitude'],
                "longitude" => $datafinal['longitude'],
                "is_popular" => "0",
                "desc" => $datafinal['about'],
                "about_us" => $datafinal['about'],
                "delegate" => 0
            ]);
            $code = "specialize_center_$specialize_center->id";
            $specialize_center->update(['code' => $code]);
            $contractData = [
                'working_hours' => json_encode($workingHours),
                'hospital_id' => $specialize_center->id,
                'contract_start_date' => $request->contract_start_date,
                'contract_end_date' => $request->contract_end_date,
                'attach_contract' => $request->attach_contract,
                'attach_documents' => $request->attach_documents,
                'attach_price_list' => $request->attach_price_list,
                'contract_note' => $request->contract_note,
                'clamis_due_date' => $request->clamis_due_date,
                'admin_fees' => $request->admin_fees,
                'bank_name' => $request->bank_name,
                'bank_account_number' => $request->bank_account_number,
                'bank_iban' => $request->bank_iban,
                'bank_swift_code' => $request->bank_swift_code,
                'bank_branch_bank' => $request->bank_branch_bank,
                'bank_mobile_number' => $request->bank_mobile_number,
                'e_wallet_name' => $request->e_wallet_name,
                'e_wallet_mobile_number' => $request->e_wallet_mobile_number,
                'instapay_mobile_number' => $request->instapay_mobile_number,
                'instapay_email' => $request->instapay_email,
                'cheque_name' => $request->cheque_name,
            ];
            $contract = DoctorContract::create($contractData);
            $contractDatadetails = [
                'contract_id' => $contract->id,
                'consultation' => $request->price_list_hospital_consulation,
                'outpatient' => $request->price_list_hospital_outpatient,
                'inpatient' => $request->price_list_hospital_inpatient,
                'laboratory' => $request->price_list_hospital_laboratory,
                'radiologe' => $request->price_list_hospital_radiology,
                'discount_other_service' => $request->price_list_hospital_discount_other,
                'doctor_visit' => $request->price_list_doctor_visit,
                'nurse_visit' => $request->price_list_nurse_visit,
                'other_service' => $request->price_list_hospital_other,
            ];
            HospitalConstact::create($contractDatadetails);
            if ($request->category_id)
            foreach ($request->category_id as $category_id)
            {
                ProviderCategory::create([
                    'provider_id'=>$specialize_center->id,
                    'category_id'=>$category_id,
                    'provider_type'=>'specialize_center',
                ]);
            }
            if ($request->has('delegate')) {
                Delegate::create([
                    'hospital_id ' => $specialize_center->id,
                    'name_1' => $request->name_1,
                    'email_1' => $request->email_1,
                    'phone_1' => $request->phone_1,
                    'name_2' => $request->name_2,
                    'email_2' => $request->email_2,
                    'phone_2' => $request->phone_2,
                ]);
            }
            DB::commit();
            return redirect()->route('specializecenter.index')->with('success', 'created success');
        }catch (\Exception $e){
            DB::rollBack();
            return $this->addResponse($e->getMessage(), false);
        }


    }
}
