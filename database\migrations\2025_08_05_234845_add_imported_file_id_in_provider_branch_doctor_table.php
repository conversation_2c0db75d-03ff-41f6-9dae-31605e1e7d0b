<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('provider_branch_doctor', function (Blueprint $table) {
            $table->foreignId('imported_file_id')->nullable()->after('id')->constrained('imported_files')->onDelete('cascade');
        });
        Schema::table('provider_times', function (Blueprint $table) {
            $table->foreignId('imported_file_id')->nullable()->after('id')->constrained('imported_files')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('provider_branch_doctor', function (Blueprint $table) {
            $table->dropForeign(['imported_file_id']);
            $table->dropColumn('imported_file_id');
        });
        Schema::table('provider_times', function (Blueprint $table) {
            $table->dropForeign(['imported_file_id']);
            $table->dropColumn('imported_file_id');
        });
    }
};
