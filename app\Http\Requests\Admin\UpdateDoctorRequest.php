<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class UpdateDoctorRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        $id = $this->route('id');
        return [
                'email' => "required|email",
                'nickname' => 'required',
                'phone' => 'required',
                'private_number'    => 'required',
                'password'  => 'nullable|min:6',
                'gender'=>'required|in:male,female',
                'specialization_id'=>'required|exists:specializations,id',
                'sub_specialization_id'=>'required|exists:specializations,id',
                'governorate_id'=>'required|exists:governorates,id',
                // 'city_id'=>'required|exists:cities,id',
                // 'lang'=>'required',
                // 'weight'=>'nullable',
                // 'location'=>'required',
                // 'is_popular'=>'required|in:0,1',
                // 'image' => 'nullable|image|mimes:jpeg,png,jpg,gif,webp',
                'latitude'=>'nullable',
                'longitude'=>'nullable',
                'experience_years'=>'nullable',
                // 'contract_start_date' => 'required|date',
                // 'contract_end_date' => 'required|date|after:contract_start_date',
                // 'home_care_net_price' => 'nullable|numeric',
                // 'home_care_gross_price' => 'nullable|numeric',
                // 'home_care_discount' => 'nullable|string',
                // 'service_clinic_gross_price' => 'nullable|numeric',
                // 'service_clinic_net_price' => 'nullable|numeric',
                // 'service_clinic_discount' => 'nullable|string',
                // 'price_list_outpatient' => 'nullable|string',
                // 'price_list_intpatient' => 'nullable|string',
                // 'service_online_gross_price' => 'nullable|numeric',
                // 'service_online_net_price' => 'nullable|numeric',
                // 'service_online_discount' => 'nullable|string',
                // 'attach_contract' => 'nullable|file|mimes:pdf,doc,docx,jpg,png',
                // 'attach_documents' => 'nullable|file|mimes:pdf,doc,docx,jpg,png',
                // 'attach_price_list' => 'nullable|file|mimes:pdf,doc,docx,jpg,png',
                // 'contract_note' => 'nullable|string',
        ];
    }
}
