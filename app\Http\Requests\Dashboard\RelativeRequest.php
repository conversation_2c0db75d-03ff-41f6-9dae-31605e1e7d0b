<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Validation\Rule;

class RelativeRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'phone' => ['nullable', 'string', 'max:20'],
            'status' => ['required', 'in:active,inactive'],
            'external_id' => ['nullable', 'string', 'max:50'],
            'national_id' => ['nullable', 'string', 'max:50'],
            'relationship_id' => ['required', 'exists:relationships,id'],
            'patient_id' => ['required', 'exists:patients,id'],
            'gender' => ['required', 'in:male,female'],
            'is_smoking' => ['boolean'],
            'is_alcoholic' => ['boolean'],
            'join_subscribe' => ['boolean'],
            'birth_date' => ['nullable', 'date'],
            'address' => ['nullable', 'string'],
            'type' => ['nullable', 'string', 'max:255'],
            'nationality_id' => ['nullable', 'exists:nationalities,id'],
            'country_id' => ['nullable', 'exists:nationalities,id'],
            'city_id' => ['nullable', 'exists:cities,id'],
            'area_id' => ['nullable', 'exists:areas,id'],
        ];

        // For update requests, handle unique validation
        if (request()->isMethod('PUT') || request()->isMethod('PATCH')) {
            if (request()->input('national_id')) {
                $rules['national_id'] = [
                    'nullable',
                    'string',
                    'max:50',
                    Rule::unique('relatives', 'national_id')->ignore(request()->route('relative'))
                ];
            }

            if (request()->input('external_id')) {
                $rules['external_id'] = [
                    'nullable',
                    'string',
                    'max:50',
                    Rule::unique('relatives', 'external_id')->ignore(request()->route('relative'))
                ];
            }

            if (request()->input('phone')) {
                $rules['phone'] = [
                    'nullable',
                    'string',
                    'max:20',
                    Rule::unique('relatives', 'phone')->ignore(request()->route('relative'))
                ];
            }
        } else {
            // For create requests, handle unique validation
            if (request()->input('national_id')) {
                $rules['national_id'] = ['nullable', 'string', 'max:50', 'unique:relatives,national_id'];
            }

            if (request()->input('external_id')) {
                $rules['external_id'] = ['nullable', 'string', 'max:50', 'unique:relatives,external_id'];
            }

            if (request()->input('phone')) {
                $rules['phone'] = ['nullable', 'string', 'max:20', 'unique:relatives,phone'];
            }
        }

        return $rules;
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => helperTrans('validation.name_required'),
            'name.string' => helperTrans('validation.name_string'),
            'name.max' => helperTrans('validation.name_max'),
            'phone.string' => helperTrans('validation.phone_string'),
            'phone.max' => helperTrans('validation.phone_max'),
            'phone.unique' => helperTrans('validation.phone_unique'),
            'status.required' => helperTrans('validation.status_required'),
            'status.in' => helperTrans('validation.status_in'),
            'external_id.string' => helperTrans('validation.external_id_string'),
            'external_id.max' => helperTrans('validation.external_id_max'),
            'external_id.unique' => helperTrans('validation.external_id_unique'),
            'national_id.string' => helperTrans('validation.national_id_string'),
            'national_id.max' => helperTrans('validation.national_id_max'),
            'national_id.unique' => helperTrans('validation.national_id_unique'),
            'relationship_id.required' => helperTrans('validation.relationship_id_required'),
            'relationship_id.exists' => helperTrans('validation.relationship_id_exists'),
            'patient_id.required' => helperTrans('validation.patient_id_required'),
            'patient_id.exists' => helperTrans('validation.patient_id_exists'),
            'gender.required' => helperTrans('validation.gender_required'),
            'gender.in' => helperTrans('validation.gender_in'),
            'birth_date.date' => helperTrans('validation.birth_date_date'),
            'nationality_id.exists' => helperTrans('validation.nationality_id_exists'),
            'country_id.exists' => helperTrans('validation.country_id_exists'),
            'city_id.exists' => helperTrans('validation.city_id_exists'),
            'area_id.exists' => helperTrans('validation.area_id_exists'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'name' => trim(request()->input('name')),
            'phone' => trim(request()->input('phone')),
            'external_id' => trim(request()->input('external_id')),
            'national_id' => trim(request()->input('national_id')),
            'address' => trim(request()->input('address')),
            'type' => trim(request()->input('type')),
            'is_smoking' => request()->boolean('is_smoking'),
            'is_alcoholic' => request()->boolean('is_alcoholic'),
            'join_subscribe' => request()->boolean('join_subscribe'),
        ]);
    }
}
