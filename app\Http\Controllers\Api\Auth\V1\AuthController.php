<?php

namespace App\Http\Controllers\Api\Auth\V1;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Auth\ChangePasswordRequest;
use App\Http\Requests\Api\Auth\LoginRequest;
use App\Http\Requests\Api\Auth\RegisterRequest;
use App\Http\Requests\Api\Auth\UpdateProfileRequest;
use App\Http\Resources\AuthPatientResource;
use App\Http\Resources\PatientResource;
use App\Http\Traits\Api_Trait;
use App\Http\Traits\Upload_Files;
use App\Models\Patient;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Tymon\JWTAuth\Facades\JWTAuth;
use App\Models\FirebaseToken;


class AuthController extends Controller
{
    use Api_Trait, Upload_Files;

    public function __construct(
        protected $resource = AuthPatientResource::class,
        protected $model = new Patient(),
    ) {}

    public function login(LoginRequest $request)
    {
        if ($token = patient()->attempt($request->all(), 1)) {
            $patient = patient()->user();
            if ($patient->status == 0) {
                return $this->returnError([helperTrans('api.The patient Account Is Inactive')]);
            }
            $patient->token = $token;
            $patient->type = 'patient';
            return $this->returnData($this->resource::make($patient), [helperTrans('api.login successfully')]);
        }
        return $this->returnError([helperTrans('api.No user was found with these credentials')], 422);
    }

    public function signup(RegisterRequest $request)
    {
        if ($request->driver == 'sms') {
            $data = $request->except(['driver', 'otp_type', 'password_confirmation']);
            $data['password'] = Hash::make($request->password);
            $data['status'] = 0;
            $otp_type = 'register';

            $patient = Patient::create($data);
            $token = JWTAuth::fromUser($patient);
            $patient->token = $token;

            $patient->phone_number = $patient->phone;
            $smsService = new SmsService();
            $response = $smsService->sendOtp($patient->phone_number, $request->driver, $otp_type, $patient);
            return $this->returnSuccessMessage([helperTrans('api.check your phone SMS please')]);
        } elseif ($request->driver == 'whatsapp') {
            //  WhatsApp
        }
        return $this->returnSuccessMessage([helperTrans('api.check your  please')]);
    }

    public function logout(Request $request)
    {
        $user = auth('patient')->user();
        if (!$user) {
            return $this->returnError([helperTrans('api.Unauthorized')], 401);
        }
        $token = FirebaseToken::where('type', $request->type)->where('user_type', $request->user_type)->where('user_id', $user->id)->where('token', $request->token)->delete();
        return $this->returnSuccessMessage([helperTrans('api.logged out successfully')]);
    }

    public function myProfile(Request $request)
    {
        $user = auth('patient')->user();
        if (!$user) {
            return $this->returnError([helperTrans('api.Unauthorized')], 401);
        }
        return  $this->returnData(PatientResource::make($user), [helperTrans('api.profile Data')]);
    }
    public function changePassword(ChangePasswordRequest $request)
    {

        /** @var \App\Models\Patient $user */
        $user = auth('patient')->user();

        if (!$user) {
            return $this->returnError([helperTrans('api.Unauthorized')], 401);
        }
        if (!Hash::check($request->old_password, $user->password)) {
            return $this->returnError([helperTrans('api.Old password is incorrect')], 422);
        }
        if (Hash::check($request->new_password, $user->password)) {
            return $this->returnError([helperTrans('api.New password cannot be the same as the old password')], 422);
        }
        $user->password = Hash::make($request->new_password);
        $user->save();
        return  $this->returnData(PatientResource::make($user), [helperTrans('api.change password successfully')]);
    }

    public function updateMyProfile(UpdateProfileRequest $request)
    {
        /**  @var \App\Models\Patient $user */
        $user = auth('patient')->user();
        if (!$user) {
            return $this->returnError([helperTrans('api.Unauthorized')], 401);
        }
        $image = $user->image;
        if ($request->image) {
            $image = $this->uploadFiles($request->user_type . 's', $request->file('image'), null);
        }
        $user->name = $request->name ?? $user->name;
        $user->email = $request->email ?? $user->email;
        $user->image = $image ?? $user->image;
        $user->phone = $request->phone ?? $user->phone;
        $user->location = $request->location ?? $user->location;
        $user->gender = $request->gender ?? $user->gender;
        $user->nickname = $request->nickname ?? $user->nickname;
        $user->save();
        return $this->returnData(PatientResource::make($user), [helperTrans('api.Profile Updated Successfully')]);
    }

    // ToDo::check softdelete required or no

    public function deleteAccount()
    {
        $user = auth('patient')->user();
        $row = Patient::find($user->id);
        if ($row == null) {
            return $this->returnError([helperTrans('api.This Patient Not Exist')], 401);
        }
        $row->delete();
        return $this->returnSuccessMessage([helperTrans('api.deleted successfully')]);
    }
}
