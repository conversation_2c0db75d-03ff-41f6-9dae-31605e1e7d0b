<?php

namespace App\Http\Resources\Api\V2;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DoctorDetailsResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        if (is_json($this->nickname)) {
            $nameData = json_decode($this->nickname, true);

            $name = $request->header('lang') == 'ar' ? $nameData['ar'] : $nameData['en'];
        } else {
            $name = $this->nickname;
        }

        $res = [
            'id' => (int)$this->id,
            'image' => new_get_file($this->image, 'person'),
            'name' => $name,
            'nickname' => $name,
            'doctor_type' => $this->doctor_type,
            'Specialization' => $this->specialization ? $this->specialization->only('name', 'id') : '',
            'location' => $this->doctor_type != 'specialized' ?  $this->provider_branches()?->first()['name'] ?? ''  : ((is_json($this->location) && is_array($decoded = json_decode($this->location, true)) && isset($decoded[$request->header('lang')]))
                ? $decoded[$request->header('lang')]
                : $this->location),
            'branch_count' => $this->provider_branches()->count(),
            'service_price_online' => $this->service_price_online,
            'service_price_home' => $this->service_price_home,
            'latitude' => $this->latitude,
            'longitude' => $this->longitude,
            'about' => $this->about,
        ];

        if (auth()->guard('patient')->check()) {
            $patient = auth()->guard('patient')->user();
            $patient_favs = $patient->fav_doctors->pluck('id')->toArray();
            $res['fav'] = in_array($this->id, $patient_favs) ? true : false;
        }
        return $res;
    }
}
