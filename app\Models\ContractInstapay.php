<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class ContractInstapay extends Model
{
    use HasFactory;
    protected $table = 'contract_instapays';
    protected $fillable = [
        'contract_id',
        'instapay_mobile_number',
        'instapay_username',
        'cheque_name',
    ];
    protected $guarded = [];
    public function contract()
    {
        return $this->belongsTo(Contract::class);
    }
}
