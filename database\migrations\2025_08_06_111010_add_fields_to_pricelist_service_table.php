<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('price_list_service', function (Blueprint $table) {
            $table->foreignId('service_category_id')->after('price_list_id')->nullable()->constrained('service_categories')->onDelete('set null');
            $table->foreignId('service_sub_category_id')->after('price_list_id')->nullable()->onDelete('set null');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('pricelist_service', function (Blueprint $table) {
            //
        });
    }
};
