<?php

namespace App\Http\Controllers\Api\V1\Patient;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Patient\Booking\ApplyPromoCodeRequest;
use App\Http\Requests\Api\Patient\FollowUpResponseRequest;
use App\Http\Requests\Api\Patient\UpdateBookingRequest;
use App\Http\Requests\UpdateInvoceRequest;
use App\Http\Resources\AdressDetailsResource;
use App\Http\Resources\AgoraRoomResource;
use App\Http\Resources\BookingResource;
use App\Http\Resources\PromoCodeResource;
use App\Http\Resources\ReplayingBookingResource;
use App\Http\Traits\Api_Trait;
use App\Http\Traits\Upload_Files;
use App\Models\AdressDetail;
use App\Models\AgoraRoom;
use App\Models\ApiSetting;
use App\Models\Booking;
use App\Models\Doctor;
use App\Models\Notification;
use App\Models\BookingDoc;
use App\Models\MainServicePackage;
use App\Models\Package;
use App\Models\PatientSubscribe;
use App\Models\PromoCode;
use App\Models\Relative;
use App\Models\ReplyingBooking;
use App\Models\Specialization;
use App\Models\SpecializationBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use App\PaymentGateways\Paypal as newPaypal;
use Carbon\Carbon;
use Srmklive\PayPal\Services\PayPal as PayPalClient;
use Paytabscom\Laravel_paytabs\Facades\paypage;
use Spatie\MediaLibrary\MediaCollections\Models\Media;
use App\Exceptions\DoctoriaException;

class BookingController extends Controller
{
    use Api_Trait, Upload_Files;


    public function make_booking(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'main_service_id' => 'required|exists:main_services,id',
                'invoice' => 'nullable',
                'desc' => 'required|string',
                'operation_type' => 'nullable|in:package,insurance,paid_online',
                'specialization_type' => 'nullable|in:nutrition,consultation',
                'relative_id' => 'nullable|exists:relatives,id',
                'files_ids' => 'nullable|array',
                'promo_code_id' => 'nullable|numeric|exists:promo_codes,id',
                'invoice.invoice_id'    => 'unique:invoices,invoice_id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();

        $relativesCount = Relative::whereNotNull('card_id')->where('patient_id', $patient->id)->count() + 1;

        // if ($request->operation_type == 'package') {
        // if($request->relative_id){
        //     $relative = Relative::find($request->relative_id);
        //     if($relative->card_id){
        //         //fetch package count
        //         //fetect booking count for this patient by this subscribe

        //     }
        // }

        if ($request->operation_type == 'package') {
            $patient_subscribes = PatientSubscribe::where('patient_id', $patient->id)->where('status', 'active')->whereHas('package', function ($query) use ($request) {
                $query->whereHas('mainServicesPackage', function ($q) use ($request) {
                    $q->where('main_service_id', $request->main_service_id);
                });
            })->get();

            $booking_test = false;

            foreach ($patient_subscribes as $patient_subscribe) {
                $package = Package::find($patient_subscribe->package_id);

                if ($package) {
                    $all_patient_service_count  = MainServicePackage::where('package_id', $package->id)
                        ->where('main_service_id', $request->main_service_id)->sum('count') * ($relativesCount);
                    $use_patient_service_count = Booking::whereNot('status', 'cancel')->where('operation_type', 'package')
                        ->where('operation_id', $patient_subscribe->id)
                        ->where('main_service_id', $request->main_service_id)
                        ->count();

                    // throw new DoctoriaException([
                    //     'all_patient_service_count' => $all_patient_service_count,
                    //     'use_patient_service_count' => $use_patient_service_count,
                    // ]);
                    if ($use_patient_service_count < $all_patient_service_count) {


                        $booking = Booking::create([
                            'id' => getNextSharedId(),
                            'patient_id' => $patient->id,
                            'relative_id' => $request->relative_id ?? null,
                            'main_service_id' => $request->main_service_id,
                            'operation_type' => 'package',
                            'operation_id' => $patient_subscribe->id,
                            'status' => 'pending',
                            'desc' => $request->desc,
                            'day' => date('Y-m-d'),
                            'time' => date('H:i:s'),
                            'specialization_type' => $request->specialization_type ?? null,
                            'promo_code_id' => $request->promo_code_id ?? null,
                            'invoice_id' => $request->invoice_id ?? null,
                        ]);



                        if ($request['files_ids'] && count($request['files_ids']) > 0) {
                            foreach ($request['files_ids'] as $file_id) {
                                $media = Media::find($file_id);
                                if ($media) {
                                    $media->model_type = Booking::class;
                                    $media->model_id = $booking->id;
                                    $media->save();
                                }
                            }
                        }
                        $doctor_info = ['title' => 'New Booking', 'body' => "There is a new booking"];
                        send_notification_to_general_doctors($doctor_info);

                        $doctors = Doctor::where('doctor_type', 'general')->get();


                        $booking_test = true;

                        // $all_package_count = count($package->mainServicesPackage);
                        // $use_package_count = 0;

                        // foreach ($package->mainServicesPackage as $mainServicePackage) {
                        //     $all_package_service_count = MainServicePackage::where('package_id', $package->id)->where('main_service_id', $mainServicePackage->main_service_id)->first()->count ?? 0;
                        //     $use_package_service_count = Booking::whereNot('status', 'cancel')->where('operation_type', 'package')->where('operation_id', $patient_subscribe->id)->where('main_service_id', $mainServicePackage->main_service_id)->count();
                        //     if ($all_package_service_count == $use_package_service_count) {
                        //         $use_package_count++;
                        //     }
                        // }

                        $use_patient_service_count = Booking::whereNot('status', 'cancel')->where('operation_type', 'package')
                        ->where('operation_id', $patient_subscribe->id)
                        ->where('main_service_id', $request->main_service_id)
                        ->count();

                        if ($all_patient_service_count == $use_patient_service_count) {
                            $patient_subscribe->update([
                                'status' => 'expire',
                            ]);
                        }

                        break;
                    }
                }
            }

            if ($booking_test) {
                // $doctors = Doctor::whereHas('categories', function ($query) {
                //     $query->where('category_id', 15);
                // })->get();

                // if ($booking->specialization_type) {
                //     $body = $booking->specialization_type;
                // } else {
                //     $body = 'General Consultation';
                // }
                // foreach ($doctors as $doctor) {
                //     Notification::create([
                //         'title' => helperTrans('api.Booking ') . $body,
                //         'body' => 'You have New booking - ' . $body,
                //         'user_type' => 'patient',
                //         'user_id' => $doctor->id,
                //         'type' => 'booking',
                //         'foreign_id' => $booking->id,
                //         'main_service_id' => $booking->main_service_id,
                //         'date' => date('Y-m-d'),
                //     ]);
                // }

                $bookingResource = new BookingResource($booking);
                return $this->returnData($bookingResource, [helperTrans('api.booking successfully')]);
            } else {
                return $this->returnError([helperTrans('api.you don`t have package active for this service')]);
            }
        } else {
            // dd( $request->invoice['payment_data']);
            $booking = Booking::create([
                'id'   => getNextSharedId(),
                'patient_id' => $patient->id,
                'relative_id' => $request->relative_id ?? null,
                'main_service_id' => $request->main_service_id,
                'operation_type' => $request->operation_type,
                //'operation_id'=>$patient_subscribe->id,
                'desc' => $request->desc,
                'day' => date('Y-m-d'),
                'time' => date('H:i:s'),
                'specialization_type' => $request->specialization_type ?? null,
                'booking_for' => $request->relative_id ? 'for_relative' : 'for_me',
                'promo_code_id' => $request->promo_code_id ?? null,
                'price' => price_after_promocode(ApiSetting::get('general_consultation_price', 100), $request->promo_code_id)
            ]);

            if ($booking->price == 0) {
                $booking->update([
                    'status' => 'pending',
                ]);
                $doctor_info = ['title' => 'New Booking', 'body' => "There is a new booking"];
                send_notification_to_general_doctors($doctor_info);
            }

            if ($request->invoice) {

                $booking->invoices()->create([
                    'invoice_id' => $request->invoice['invoice_id'] ?? null,
                    'invoice_key' => $request->invoice['invoice_key'] ?? null,
                    'payment_data' => $request->invoice['payment_data'] ?? null,
                ]);
            }

            if ($request['files_ids'] && count($request['files_ids']) > 0) {
                foreach ($request['files_ids'] as $file_id) {
                    $media = Media::find($file_id);
                    if ($media) {
                        $media->model_type = Booking::class;
                        $media->model_id = $booking->id;
                        $media->save();
                    }
                }
            }
        }

        if ($booking) {
            $promoCode = PromoCode::find($request->promo_code_id);
            if ($promoCode && $promoCode->is_active) {
                $promoCode->current_redemptions++;
                $promoCode->save();

                // If the usage limit per patient is reached, set the is_active to false
                if ($promoCode && $promoCode->current_redemptions == $promoCode->max_redemptions) {
                    $promoCode->is_active = false;
                    $promoCode->save();
                }
            }
        }
        if ($booking->doctor) {
            run_push_notification($booking->doctor->id, 'doctor', [
                'title' => 'new Booking',
                'body' => 'There is a new booking',
                'type' => 'new_booking',
            ]);
        }

        run_push_notification($booking->patient->id, 'patient', [
            'title' => 'Booking Request',
            'body' => 'Booking has been created successfully',
            'type' => 'new_booking',
        ]);

        $bookingResource = new BookingResource($booking);

        return $this->returnData($bookingResource, [helperTrans('api.booking successfully, please complete payment')]);
    }

    public function update_booking(UpdateBookingRequest $request)
    {

        $booking = Booking::where('status', 'not_paid')->find($request->booking_id);
        if (!$booking) {
            $booking = SpecializationBooking::where('status', 'not_paid')->find($request->booking_id);
            if (!$booking) {
                return  $this->returnError([helperTrans('api.Booking Not Found Or Allowed ')]);
            }
        }

        if ($request->payment_method == 'cash') {
            if ($booking instanceof SpecializationBooking && $booking->visit == "offline") {

                $booking->update([
                    'status' => 'pending',
                    'booking_type' => 'cash'
                ]);
                return $this->returnData(new BookingResource($booking), [helperTrans('api.Saved Successfully')]);
            } else {
                return  $this->returnError([helperTrans('api.Not Allowed')]);
            }
        }

        if ($booking->visit == 'home') {


            $booking->update([
                "phone" => $request->phone,
                "latitude" => $request->latitude,
                "longitude" => $request->longitude,
                "location" => $request->location,
                "governorate_id" => $request->governorate_id,
                "city_id" => $request->city_id,
                "street" => $request->street
            ]);
        }


        if ($booking->invoices()->doesntExist()) {

            $booking->invoices()->create([
                'invoice_id' => $request->invoice['invoice_id'] ?? null,
                'invoice_key' => $request->invoice['invoice_key'] ?? null,
                'payment_data' => $request->invoice['payment_data'] ?? null,
            ]);
        } else {
            return  $this->returnError([helperTrans('api.Booking Has Invoice Before')]);
        }

        return $this->returnData(new BookingResource($booking), [helperTrans('api.Invoice Saved Successfully')]);
    }


    public function address_details(Request $request)
    {
        $rules = [
            'noters' => 'nullable|string',
            'floor' => 'nullable|string',
            'building_name' => 'required|string',
            'type' => 'required|in:home,work,hotel,other',
            'street' => 'required|string',
            'phone' => 'required|string',
            'select_manually' => 'required|boolean', // Ensure it's explicitly provided
            'governorate_id' => 'required|exists:governorates,id',
            'city_id' => 'required|exists:cities,id',
            'long' => 'required|numeric',
            'lat' => 'required|numeric',
        ];



        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return $this->returnErrorValidation($validator->errors(), 403);
        }



        $patient = auth('patient')->user();

        $addressDetails = AdressDetail::create([
            'patient_id' => $patient->id,
            'noters' => $request->noters,
            'floor' => $request->floor,
            'building_name' => $request->building_name,
            'type' => $request->type,
            'street' => $request->street,
            'phone' => $request->phone,
            'governorate_id' =>  $request->governorate_id ?? null,
            'city_id' => $request->city_id ?? null,
            'long' => $request->long ?? null,
            'lat' => $request->lat ?? null,
        ]);

        return $this->returnData(new AdressDetailsResource($addressDetails), [helperTrans('api.address_details_save_successfully')]);
    }



    public function updateAddressDetails(Request $request, $id)
    {
        $rules = [
            'noters' => 'nullable|string',
            'floor' => 'nullable|string',
            'building_name' => 'sometimes|string',
            'type' => 'sometimes|in:home,work,hotel,other',
            'street' => 'sometimes|string',
            'phone' => 'sometimes|string',
            'select_manually' => 'required|boolean', // Ensure it's explicitly provided
            'governorate_id' => 'sometimes|exists:governorates,id',
            'city_id' => 'sometimes|exists:cities,id',
            'long' => 'sometimes|numeric',
            'lat' => 'sometimes|numeric',
        ];



        $validator = Validator::make($request->all(), $rules);

        if ($validator->fails()) {
            return $this->returnErrorValidation($validator->errors(), 403);
        }

        // Get authenticated patient
        $patient = auth('patient')->user();

        // Check if address details exist
        $addressDetails = AdressDetail::find($id);

        if (!$addressDetails) {
            return $this->returnError('Address not found', 404);
        }

        // Update address details
        $addressDetails->update([
            'noters' => $request->noters ?? $addressDetails->noters,
            'floor' => $request->floor ?? $addressDetails->floor,
            'building_name' => $request->building_name ?? $addressDetails->building_name,
            'type' => $request->type ?? $addressDetails->type,
            'street' => $request->street ?? $addressDetails->street,
            'phone' => $request->phone ?? $addressDetails->phone,
            'governorate_id' => ($request->governorate_id ?? $addressDetails->governorate_id),
            'city_id' => ($request->city_id ?? $addressDetails->city_id),
            'long' => ($request->long ?? $addressDetails->long),
            'lat' => ($request->lat ?? $addressDetails->lat),
        ]);

        return $this->returnData(new AdressDetailsResource($addressDetails), [helperTrans('api.address_details_updated_successfully')]);
    }


    public function delete_address(Request $request, $id)
    {
        // Check if address details exist
        $addressDetails = AdressDetail::find($id);

        // If not found, return error response
        if (!$addressDetails) {
            return $this->returnError('Address not found', 404);
        }

        // Delete the address
        $addressDetails->delete();

        // Return a success response without the deleted record
        return $this->returnData(null, [helperTrans('api.address_details_deleted_successfully')]);
    }


    public function save_address(Request $request)
    {


        $patient = auth('patient')->user();

        $addressDetails = AdressDetail::where('patient_id', $patient->id)->get();

        return $this->returnData(AdressDetailsResource::collection($addressDetails), [helperTrans('api.address_details_save')]);
    }



    public function handleReturn(Request $request)
    {
        $booking_id = $request->cartId;

        // Fetch the latest booking for the authenticated patient
        $row = Booking::find($booking_id);

        // Check if the booking exists
        if (!$row) {
            return redirect()->route('paytabs.status', ['status' => 'failed']); // Handle the case where no booking is found
        }

        // Update the transaction reference and status based on the response status
        $updateData = ['tran_ref' => $request->tran_ref];
        if ($request->respStatus == 'A') {
            $updateData['status'] = 'pending';
            $row->update($updateData);
            return redirect()->route('paytabs.status', ['status' => 'success']);
        }

        $row->update(array_merge($updateData, ['status' => 'not_paid']));
        return redirect()->route('paytabs.status', ['status' => 'failed', 'booking_id' => $booking_id]);
    }

    public function paytabStatus($status)
    {

        if ($status == 'success')
            return $this->returnSuccessMessage([helperTrans('api.Payment Success')]);
        else
            return $this->returnSuccessMessage([helperTrans('api.Payment Failed')]);
    }

    public function handleCallback(Request $request)
    {
        $status = $request->input('status');
        $bookingId = $request->input('booking_id');
        if ($status == 'failed') {
            $booking = Booking::find($bookingId);
            if ($booking) {
                $newPaymentUrl = $this->createPayment($booking);
                if ($newPaymentUrl) {
                    $newPaymentUrl = new BookingResource($booking);
                    $newPaymentUrl->additional(['payment_url' => $newPaymentUrl]);
                } else {
                    return $this->returnError([helperTrans('api.payment initiation failed')]);
                }
            }
        } else {
        }
    }




    public function bookings(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'status' => 'in:not_paid,active,complete,cancel',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();

        $instant_bookings = Booking::with(['doctor', 'mainService', 'replying'])->where('patient_id', $patient->id);
        $specialized_bookings = SpecializationBooking::with('doctor')->where('patient_id', $patient->id);
        // $instant_bookings->merge($specialized_bookings);
        if ($request->status) {
            $status = ['complete', 'reappoint', 'referral', 'specialized'];

            if ($request->status == 'active')
                $status = ['active', 'pending', 'follow up', 'transfer',];

            if ($request->status == 'cancel')
                $status = ['cancel'];

            if ($request->status == 'not_paid')
                $status = ['not_paid'];


            $instant_bookings = $instant_bookings->whereIn('status', $status)->get();
            $specialized_bookings = $specialized_bookings->whereIn('status', $status)->get();
        }

        $bookings = $instant_bookings->merge($specialized_bookings)->sortByDesc('updated_at');


        return $this->returnData(BookingResource::collection($bookings), [helperTrans('api.Bookings Data')], 200);
    }

    public function show($id)
    {
        $patient = auth('patient')->user();

        $booking = Booking::with(['doctor', 'mainService', 'replying', 'patient', 'relative'])
            ->where('id', $id)
            ->where('patient_id', $patient->id)
            ->get();


        // return $booking;

        return $this->returnData(BookingResource::collection($booking), [helperTrans('api.Booking Data')], 200);
    }

    public function change_status(Request $request)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'status' => 'required|in:cancel',
                'booking_id' => 'required|exists:bookings,id'
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();

        $booking = Booking::findOrFail($request->booking_id);

        $request->status ? $booking->status = $request->status : '';

        return $this->returnData(BookingResource::make($booking), [helperTrans('api.Booking Data')], 200);
    }


    public function get_agora_room(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
                'type' => 'required|in:audio,video',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $patient = auth('patient')->user();
        // return $patient;
        $booking = Booking::where('patient_id', $patient->id)->where('status', 'active')->find($request->booking_id);
        if (!$booking) {
            return  $this->returnError([helperTrans('api.Booking not Found')]);
        }

        $room = AgoraRoom::where('doctor_id', $booking->doctor_id)->where('booking_id', $booking->id)->where('patient_id', $patient->id)->where('name', '!=', null)->first();


        if (!$room) {
            return  $this->returnError([helperTrans('api.Room not Found')]);
        }


        return $this->returnData(AgoraRoomResource::make($room), [helperTrans('api.Room Data')], 200);
    }

    public function booking_replay($id)
    {

        $booking = get_booking($id);

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $reply = ReplyingBooking::with([
            'doctor',
            'replyingBookingAnalysis',
            'replyingBookingMedicals',
            'replyingBookingRadiology',
            'replyingBookingDiagnoses'
        ])->where('booking_id', $booking->id)->first();

        // // If the booking is transfered
        // if ($booking->status === 'transfer') {
        //     $reply->referral_booking = BookingResource::make(Booking::where('referral_code', $booking->tran_ref)->first());
        // }


        // Get the referral booking if exists
        if ($booking->refer_to) {
            if ($booking->status === 'specialized') {
                $booking->referral_specialized_booking = SpecializationBooking::where('refer_from', $booking->refer_to)->first();
            } else {
                $booking->referral_booking = Booking::where('refer_from', $booking->refer_to)->first();
            }
        }
        $response_data = BookingResource::make($booking);

        return $this->returnData($response_data, [helperTrans('api.Booking Data')], 200);


        // return $reply;

        if (!$reply)
            return $this->returnError([helperTrans('api.No replays yet')]);

        return $this->returnData(ReplayingBookingResource::make($reply), [helperTrans('api.Booking Replay Data')], 200);
    }


    public function booking_details(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'booking_id' => 'required|exists:bookings,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $booking = Booking::with([
            'patient',
            'mainService',
            'replying.replyingBookingDiagnoses',
            'replying.replyingBookingAnalysis',
            'replying.replyingBookingMedicals',
            'replying.replyingBookingRadiology',
        ])->find($request->booking_id);


        // Get the referral booking if exists
        if ($booking->refer_to) {
            if ($booking->status === 'specialized') {
                $booking->referral_specialized_booking = SpecializationBooking::where('refer_from', $booking->refer_to)->first();
            } else {
                $booking->referral_booking = Booking::where('refer_from', $booking->refer_to)->first();
            }
        }
        $response_data = BookingResource::make($booking);

        return $this->returnData($response_data, [helperTrans('api.Booking Data')], 200);
    }

    public function createPayment($order)
    {
        $provider = new PayPalClient;
        $provider->setApiCredentials(config('paypal'));
        $paypalToken = $provider->getAccessToken();
        $data = [
            'intent' => 'CAPTURE',
            'purchase_units' => [
                [
                    'amount' => [
                        'currency_code' => 'USD',
                        'value' => $order->price,
                    ],
                ],
            ],
            "application_context" => [
                "brand_name" => $order?->booking?->desc,
                "return_url" => route('paypal.success'),
                "cancel_url" => route('paypal.cancel')
            ]
        ];
        $response = $provider->createOrder($data);
        //dd($response);
        if (isset($response['id']) && $response['id'] != null) {
            foreach ($response['links'] as $link) {
                if ($link['rel'] === 'approve') {
                    return [
                        'payment_url' => $link['href']
                    ];
                }
            }
        } else {
            return null;
        }
    }

    public function success(Request $request)
    {
        // dd($request->all());
        $provider = new PayPalClient();
        $provider->setApiCredentials(config('paypal'));
        $paypalToken = $provider->getAccessToken();
        $response = $provider->capturePaymentOrder($request->token);
        // dd($response);
    }

    public function initiatePayment(Request $request)
    {
        $bookingId = $request->input('bookingId');
        $booking = Booking::find($bookingId);
        if (!$booking) {
            return response()->json(['error' => 'Booking not found.'], 404);
        }
        $paymentUrl = $this->createPayment($booking);
        if ($paymentUrl) {
            return response()->json([
                'data' => $booking,
                'paymentUrl' => $paymentUrl,
                'message' => 'Booking successfully created. Please complete payment.',
                'status' => 200
            ]);
        } else {
            return response()->json(['error' => 'Unable to create PayPal payment.'], 500);
        }
    }

    public function response(FollowUpResponseRequest $request, $id)
    {
        $doctor = auth('doctor')->user();
        $patient = auth('patient')->user();
        if ($doctor && $doctor->doctor_type == 'specialized') {
            $booking = SpecializationBooking::whereIn('status', ['not_paid', 'active', 'pending'])->where('doctor_id', $doctor->id)->find($id);
        } else if ($patient) {
            $booking = Booking::whereIn('status', ['not_paid', 'active', 'pending', 'follow up', 'transfer'])->find($id);
            if (!$booking) {
                $booking = SpecializationBooking::whereIn('status', ['not_paid', 'active', 'pending'])->find($id);
            }
        }

        if (!$booking) {
            return $this->returnError([helperTrans('api.booking not found')]);
        }

        $booking->update([
            'status' => $request->status,
            'cancel_reason' => $request->reason
        ]);

        if ($doctor && $doctor->doctor_type == 'specialized') {

            $patient_info = ['title' => 'Booking Cancelation', 'body' => "Doctor {$doctor->name} cancel your booking"];
            run_push_notification($booking->patient_id, 'patient', $patient_info);

            $doctor_info = ['title' => 'Booking Cancelation', 'body' => "Booking has been canceled successfully"];
            run_push_notification($booking->doctor_id, 'doctor', $doctor_info);
        } else if ($patient) {

            $doctor_info = ['title' => 'Booking Cancelation', 'body' => "Have a booking cancelation"];
            run_push_notification($booking->doctor_id, 'doctor', $doctor_info);

            $patient_info = ['title' => 'Booking Cancelation', 'body' => "Booking has been canceled successfully"];
            run_push_notification($booking->patient_id, 'patient', $patient_info);
            // Notification::create([
            //     'title' => 'Booking Cancelation ',
            //     'body' => ' Have Cancel Booking ',
            //     'user_type' => 'patient',
            //     'user_id' => $booking->doctor_id,
            //     'type' => 'booking',
            //     'foreign_id' => $booking->doctor_id,
            //     'main_service_id' => $$booking->main_service_id ?? null,
            //     'date' => date('Y-m-d'),
            // ]);
        }


        return $this->returnData(BookingResource::make($booking), [helperTrans('api.Response added successfully')]);
    }


    public function rePay($id)
    {
        $booking = Booking::find($id);
        if (!$booking) {
            return response()->json([
                'status' => false,
                'message' => 'Booking not found.'
            ], 404);
        }
        $patient = auth('patient')->user();
        $price = 10;
        $order = (object) [
            'price' => $price,
            'booking' => $booking
        ];
        $pay = paypage::sendPaymentCode('all')
            ->sendTransaction('sale', 'ecom')
            ->sendCart($booking->id, $price, 'Booking Payment')
            ->sendCustomerDetails(
                $patient->name,
                $patient->email,
                $patient->phone,
                null,
                null,
                null,
                null,
                $booking->id,
                request()->ip()
            )
            ->sendURLs(route('paytabs.return'), route('paytabs.callback')) // Define your callback URL
            ->sendLanguage('en')
            ->sendHideShipping(false)
            ->sendFramed(true)
            ->create_pay_page();
        $paymentUrl = $this->createPayment($order);
        $paymentUrl = $this->createPayment($order);
        $booking->payment_url = $pay;
        if ($paymentUrl) {
            $bookingResource = new BookingResource($booking);
            $bookingResource->additional(['payment_url' => $paymentUrl]);
        } else {
            return $this->returnError([helperTrans('api.payment initiation failed')]);
        }
        return $this->returnData($bookingResource, [helperTrans('api.booking successfully, please complete payment')]);
    }

    public function apply_promo_code(ApplyPromoCodeRequest $request)
    {
        $promoCode = $request->validated()['promo-code'];

        $promoCodeData = PromoCode::where('code', $promoCode)
            ->where('is_active', true)
            ->where('start_date', '<=', Carbon::now())
            ->where('end_date', '>=', Carbon::now())
            ->where(function ($query) {
                $query->whereColumn('current_redemptions', '<', 'max_redemptions')
                    ->orWhereNull('max_redemptions');
            })
            ->first();

        if (!$promoCodeData) {
            return $this->returnError([helperTrans('api.Invalid promo code')]);
        }

        //  Get patient usage count of the promo code from both booking and specialization_bookings tables.
        $patient = auth('patient')->user();

        $bookingUsageCount = Booking::where('patient_id', $patient->id)->where('promo_code_id', $promoCodeData->id)->count();
        $specializationUsageCount = SpecializationBooking::where('patient_id', $patient->id)->where('promo_code_id', $promoCodeData->id)->count();

        $totalUsageCount = $bookingUsageCount + $specializationUsageCount;

        // Check if user's usage count exceeds the usage limit per patient
        if ($promoCodeData->usage_limit_per_patient && $totalUsageCount >= $promoCodeData->usage_limit_per_patient) {
            return $this->returnError([helperTrans('api.You have reached the usage limit for this promo code')]);
        }

        $promo_code =   PromoCodeResource::make($promoCodeData);

        return $this->returnData($promo_code, [helperTrans('api.Promo code can be applied')]);
    }
}
