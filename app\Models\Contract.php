<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Contract extends Model
{
    use HasFactory;
    protected $table = 'contracts';
    protected $fillable = [
        'admin_id',
        'provider_id',
        'official_full_name',
        'commercial_registration_number',
        'national_id',
        'tax_card_number',
        'tax_card_due_date',
        'private_phone_number',
        'private_email',
        'representative_full_name',
        'position',
        'phone',
        'email',
        'facebook_link',
        'instagram_link',
        'contract_start_date',
        'contract_end_date',
        'payment_method_id',
        'claims_due_date',
        'admin_fees',
        'need_approve',
        'notes',
    ];
    protected $guarded = [];
    protected $casts = [
        'contract_start_date' => 'date',
        'contract_end_date' => 'date',
        'tax_card_due_date' => 'date',
    ];

    public function provider()
    {
        return $this->belongsTo(Provider::class);
    }
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }
    public function paymentMethod()
    {
        return $this->belongsTo(PaymentMethod::class);
    }
    public function insuranceCompanies()
    {
        return $this->belongsToMany(InsuranceCompany::class, 'contract_insurance_companies', 'contract_id', 'insurance_company_id')->withTimestamps();
    }
    public function otherPlaces()
    {
        return $this->belongsToMany(OtherPlace::class, 'contract_other_places', 'contract_id', 'other_place_id')->withTimestamps();
    }
    public function delegateManagers()
    {
        return $this->belongsToMany(DelegateManager::class, 'contract_delegates', 'contract_id', 'delegate_manager_id')->withTimestamps();
    }
    public function attachments()
    {
        return $this->hasMany(ContractAttachment::class);
    }
    public function banks()
    {
        return $this->hasMany(ContractBank::class);
    }
    public function wallets()
    {
        return $this->hasMany(ContractWallet::class);
    }
    public function instapays()
    {
        return $this->hasMany(ContractInstapay::class);
    }
}
