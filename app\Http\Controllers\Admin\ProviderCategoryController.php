<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\ProviderCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class ProviderCategoryController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        if (request()->ajax()) {
            $categories = ProviderCategory::all();
            return DataTables::of($categories)
                ->addColumn('action', function ($category) {
                    $html = '<div class="dropdown d-inline-block">
                    <button class="btn btn-soft-secondary btn-sm dropdown" type="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="ri-more-fill align-middle"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item edit-item-btn" href="javascript:void(0)" data-id="' . $category->id . '"><i class="ri-pencil-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.edit') . '</a></li>
                        <li>
                            <a class="dropdown-item remove-item-btn" href="javascript:void(0)" data-id="' . $category->id . '">
                                <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.delete') . '
                            </a>
                        </li>
                    </ul>
                </div>';
                    return $html;
                })
                ->addColumn('name_en', function ($category) {
                    return $category->getTranslation('name', 'en');
                })
                ->addColumn('name_ar', function ($category) {
                    return $category->getTranslation('name', 'ar');
                })

                ->rawColumns(['action'])
                ->make(true);
        }

        return view('Admin.CRUDS.provider_categories.index');
    }


    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('Admin.CRUDS.provider_categories.form');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:provider_categories,name',
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $category = ProviderCategory::create($request->all());

        if ($request->ajax()) {
            return response()->json([
                'message' => __('admin.Category created successfully'),
                'redirect' => route('provider_categories.index')
            ]);
        }

        return redirect()->route('provider_categories.index')
            ->with('success', __('admin.Category created successfully'));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(ProviderCategory $providerCategory)
    {
        return view('Admin.CRUDS.provider_categories.form', compact('providerCategory'));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, ProviderCategory $providerCategory)
    {
        $validator = Validator::make($request->all(), [
            'name' => 'required|string|max:255|unique:provider_categories,name,' . $providerCategory->id,
            'status' => 'required|in:active,inactive',
        ]);

        if ($validator->fails()) {
            if ($request->ajax()) {
                return response()->json(['errors' => $validator->errors()], 422);
            }
            return redirect()->back()->withErrors($validator)->withInput();
        }

        $providerCategory->update($request->all());

        if ($request->ajax()) {
            return response()->json([
                'message' => __('admin.Category updated successfully'),
                'redirect' => route('provider_categories.index')
            ]);
        }

        return redirect()->route('provider_categories.index')
            ->with('success', __('admin.Category updated successfully'));
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(ProviderCategory $providerCategory)
    {
        $providerCategory->delete();
        return response()->json(['message' => __('admin.Category deleted successfully')]);
    }

    /**
     * Get provider categories data for DataTables.
     */
    public function getData()
    {
        $query = ProviderCategory::query();

        return DataTables::of($query)
            ->addColumn('actions', function ($category) {
                $actions = '';

                if (auth()->guard('admin')->user()->hasPermission('provider_categories_update')) {
                    $actions .= '<a href="' . route('provider_categories.edit', $category->id) . '" class="btn btn-sm btn-primary me-1">' . __('admin.Edit') . '</a>';
                }

                if (auth()->guard('admin')->user()->hasPermission('provider_categories_delete')) {
                    $actions .= '<button type="button" class="btn btn-sm btn-danger delete-btn" data-id="' . $category->id . '">' . __('admin.Delete') . '</button>';
                }

                return $actions;
            })
            ->editColumn('status', function ($category) {
                return $category->status === 'active'
                    ? '<span class="badge bg-success">' . __('admin.Active') . '</span>'
                    : '<span class="badge bg-danger">' . __('admin.Inactive') . '</span>';
            })
            ->rawColumns(['actions', 'status'])
            ->make(true);
    }
}
