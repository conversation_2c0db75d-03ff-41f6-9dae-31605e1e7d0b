<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Http\Traits\ResponseTrait;
use App\Models\Clinic;
use App\Models\Specialization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;

class ClinicController extends Controller
{
    use ResponseTrait;

    public function index(Request $request)
    {
        if ($request->ajax()) {
            $clinics = Clinic::with('specialization')->select(['id', 'name', 'specialization_id', 'notes', 'created_at'])->latest();
            return DataTables::of($clinics)
                ->addColumn('action', function ($clinic) {
                    $actions = '<a href="' . route('admin.clinics.edit', $clinic->id) . '" class="btn btn-sm btn-primary">' . helperTrans('admin.edit') . '</a>';
                    $actions .= ' <form action="' . route('admin.clinics.destroy', $clinic->id) . '" method="POST" style="display:inline">'
                        . csrf_field() . method_field('DELETE')
                        . ' <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm(\'' . helperTrans('admin.Are you sure?') . '\')">' . helperTrans('admin.delete') . '</button></form>';
                    return $actions;
                })
                ->editColumn('created_at', function($clinic) {
                    return $clinic->created_at ? $clinic->created_at->format('Y-m-d H:i') : '';
                })->addColumn('name_en', function($clinic) {
                    return $clinic->getTranslation('name', 'en');
                })
                ->addColumn('name_ar', function($clinic) {
                    return $clinic->getTranslation('name', 'ar');
                })->addColumn('specialization', function($clinic) {
                    return $clinic->specialization?->name;
                })->filterColumn('name', function($query, $keyword) {
                    $query->where('name', 'like', "%{$keyword}%");
                })
                ->rawColumns(['action'])
                ->make(true);
        }
        return view('Admin.CRUDS.clinics.index');
    }

    public function create()
    {
        $specializations = Specialization::all();
        return view('Admin.CRUDS.clinics.create', compact('specializations'));
    }

    public function store(Request $request)
    {

        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required|array|max:255',
                'name.ar' => 'required|string|max:255',
                'name.en' => 'required|string|max:255',
                'specialization_id' => 'required|exists:specializations,id',
                'notes' => 'nullable|string',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        // $name=json_encode($request->name);

        Clinic::create([
            'name'=>$request->name,
            'specialization_id'=>$request->specialization_id,
            'notes'=>$request->notes
        ]);

        return redirect()->route('admin.clinics.index')
            ->with('success', helperTrans('admin.Clinic created successfully.'));
    }

    public function show(Clinic $clinic)
    {
        return view('Admin.CRUDS.clinics.show', compact('clinic'));
    }

    public function edit(Clinic $clinic)
    {
        $specializations = Specialization::all();
        return view('Admin.CRUDS.clinics.edit', compact('clinic','specializations'));
    }

    public function update(Request $request, Clinic $clinic)
    {
        $validator = Validator::make(
            $request->all(),
            [
                'name' => 'required|array|max:255',
                'name.ar' => 'required|string|max:255',
                'name.en' => 'required|string|max:255',
                'specialization_id' => 'required|exists:specializations,id',
                'notes' => 'nullable|string',

            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }

        $clinic->update($request->only(['name', 'specialization_id', 'notes']));

        return redirect()->route('admin.clinics.index')
            ->with('success', helperTrans('admin.Clinic updated successfully.'));
    }

    public function destroy(Clinic $clinic)
    {
        $clinic->delete();
        return redirect()->route('admin.clinics.index')
            ->with('success', helperTrans('admin.Clinic deleted successfully.'));
    }
}
