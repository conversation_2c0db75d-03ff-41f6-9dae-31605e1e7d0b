<?php

namespace App\Http\Requests\Dashboard;

use App\Http\Requests\Api\BaseFormRequest;
use Illuminate\Validation\Rule;

class RelationshipRequest extends BaseFormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $rules = [
            'name' => ['required', 'string', 'max:255'],
            'code' => ['nullable', 'string', 'max:50'],
        ];

        // For update requests, handle unique name and code validation
        if (request()->isMethod('PUT') || request()->isMethod('PATCH')) {
            if (request()->input('name')) {
                $rules['name'] = [
                    'required',
                    'string',
                    'max:255',
                    Rule::unique('relationships', 'name')->ignore(request()->route('relationship'))
                ];
            }

            if (request()->input('code')) {
                $rules['code'] = [
                    'nullable',
                    'string',
                    'max:50',
                    Rule::unique('relationships', 'code')->ignore(request()->route('relationship'))
                ];
            }
        } else {
            // For create requests, handle unique name and code
            if (request()->input('name')) {
                $rules['name'] = ['required', 'string', 'max:255', 'unique:relationships,name'];
            }

            if (request()->input('code')) {
                $rules['code'] = ['nullable', 'string', 'max:50', 'unique:relationships,code'];
            }
        }

        return $rules;
    }

    /**
     * Get custom error messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'name.required' => helperTrans('validation.name_required'),
            'name.string' => helperTrans('validation.name_string'),
            'name.max' => helperTrans('validation.name_max'),
            'name.unique' => helperTrans('validation.name_unique'),
            'code.string' => helperTrans('validation.code_string'),
            'code.max' => helperTrans('validation.code_max'),
            'code.unique' => helperTrans('validation.code_unique'),
        ];
    }

    /**
     * Prepare the data for validation.
     */
    protected function prepareForValidation()
    {
        $this->merge([
            'name' => trim(request()->input('name')),
            'code' => trim(request()->input('code')),
        ]);
    }
}
