<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // 1. Drop ALL foreign keys referencing services.id
        Schema::table('price_list_service', function ($table) {
            $table->dropForeign('price_list_service_service_id_foreign');
        });

        Schema::table('provider_request_items', function ($table) {
            $table->dropForeign('provider_request_items_service_id_foreign');
        });

        Schema::table('provider_branch_service', function ($table) {
            $table->dropForeign('provider_branch_service_service_id_foreign');
        });

        // 2. Alter the 'id' column on services to be AUTO_INCREMENT
        DB::statement("ALTER TABLE services MODIFY COLUMN id BIGINT UNSIGNED NOT NULL AUTO_INCREMENT;");

        // 3. Re-add the foreign keys
        Schema::table('price_list_service', function ($table) {
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });

        Schema::table('provider_request_items', function ($table) {
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });

        Schema::table('provider_branch_service', function ($table) {
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Rollback: optional, remove AUTO_INCREMENT
        Schema::table('price_list_service', function ($table) {
            $table->dropForeign(['service_id']);
        });

        DB::statement("ALTER TABLE services MODIFY COLUMN id BIGINT UNSIGNED NOT NULL;");

        Schema::table('price_list_service', function ($table) {
            $table->foreign('service_id')->references('id')->on('services')->onDelete('cascade');
        });
    }
};
