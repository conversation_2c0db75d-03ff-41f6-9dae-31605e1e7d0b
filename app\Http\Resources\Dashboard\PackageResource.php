<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PackageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'name' => $this->getTranslations('name'),
            'description' => $this->description,
            'price' => (float)$this->price,
            'image' => $this->image ? get_file($this->image) : null,
            'max_relative' => (int)$this->max_relative,
            'show_in_app' => (bool)$this->show_in_app,
            
            // Relationship counts
            'partners_count' => $this->when($this->relationLoaded('partners'), function () {
                return $this->partners->count();
            }),
            
            'patient_subscribes_count' => $this->when($this->relationLoaded('patientSubscribes'), function () {
                return $this->patientSubscribes->count();
            }),
            
            'package_requests_count' => $this->when($this->relationLoaded('packageRequests'), function () {
                return $this->packageRequests->count();
            }),
            
            'main_services_count' => $this->when($this->relationLoaded('mainServicesPackage'), function () {
                return $this->mainServicesPackage->count();
            }),
            
            'elements_count' => $this->when($this->relationLoaded('elements'), function () {
                return $this->elements->count();
            }),
            
            'services_count' => $this->when($this->relationLoaded('services'), function () {
                return $this->services->count();
            }),
            
            // Relationship data (when loaded)
            'main_services_package' => $this->when($this->relationLoaded('mainServicesPackage'), function () {
                return $this->mainServicesPackage->map(function ($item) {
                    return [
                        'id' => $item->id,
                        'count' => $item->count,
                        'main_service' => $item->mainService ? [
                            'id' => $item->mainService->id,
                            'name' => $item->mainService->getTranslations('name'),
                        ] : null,
                    ];
                });
            }),
            
            'elements' => $this->when($this->relationLoaded('elements'), function () {
                return $this->elements->map(function ($element) {
                    return [
                        'id' => $element->id,
                        'title' => $element->getTranslations('title'),
                        'description' => $element->description,
                        'image' => $element->image ? get_file($element->image) : null,
                    ];
                });
            }),
            
            'services' => $this->when($this->relationLoaded('services'), function () {
                return $this->services->map(function ($service) {
                    return [
                        'id' => $service->id,
                        'name' => $service->getTranslations('name'),
                        'price' => $service->price,
                    ];
                });
            }),
            
            // Timestamps
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
        ];
    }
}
