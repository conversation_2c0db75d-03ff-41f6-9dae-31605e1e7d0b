<?php

namespace App\Http\Controllers\Api\V1\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Traits\Api_Trait;
use App\Models\Doctor;
use App\Models\Patient;
use Illuminate\Support\Str;
use App\Http\Requests\Api\Auth\{ForgetPasswordRequest, ForgotPasswordRequest, NewResetPasswordRequest, ResetPasswordRequest};
use App\Models\OtpCode;
use Illuminate\Support\Facades\{Mail, Hash, Log};
use App\Mail\SendOtpMail;
use Carbon\Carbon;
use App\Services\Otp\OtpServiceManager;
use App\Enums\Auth\Otp\OtpType;
use App\Models\Provider;
use App\Services\Otp\WhatsappOtpService;
use App\Services\SmsService;
use Illuminate\Support\Facades\Validator;

class ResetPasswordController extends Controller
{
    use Api_Trait;

    public function __construct(protected OtpServiceManager $otpServiceManager)
    {
        $this->otpServiceManager = $otpServiceManager;
    }

    protected function getBroker($userType)
    {
        return $userType === 'patient' ? 'patient' : 'doctor';
    }

    public function checkOtp(Request $request)
    {
        if ($request->phone) {
            $patient = Patient::where('phone', $request->phone)->first();
            if (!$patient) {
                return $this->returnErrorDataNotFound([helperTrans('api.Patient not found')]);
            }
            $otpCode = OtpCode::where('otp', $request->otp)
                ->where('phone_number', $patient->phone)
                ->first();
        }
        if (!$otpCode) {
            return $this->returnErrorDataNotFound([helperTrans('api.Otp not found.')]);
        }
        if (Carbon::now()->greaterThan($otpCode->expires_at)) {
            $otpCode->update(['status' => 'invalid']);
            return $this->returnInvalidData([helperTrans('api.OTP has expired')]);
        }
        if ($otpCode->otp_type->value === OtpType::REGISTER->value) {
            $patient = Patient::find($otpCode->otpable_id);
            if ($patient) {
                $patient->status = 1;
                $patient->save();
            } else {
                return $this->returnErrorDataNotFound([helperTrans('api.Patient not found')]);
            }
        }
        return $this->returnData(
            [
                'otp' => $otpCode->otp,
                'is_sent' => $otpCode->is_sent ? 'valid' : 'invalid',
                'otpStatus' => $otpCode->status,
                'otp_type' => $otpCode->otp_type,
                'time_remaining' => $otpCode->time_remaining . ' minutes',
            ],
            ['Get Otp Successfully']
        );
    }


    public function resendOtp(Request $request)
    {
        $patient = Patient::where('phone', $request->phone)->first();
        if (!$patient) {
            return $this->returnErrorDataNotFound([helperTrans('api.Patient not found')]);
        }
        $otp_type = $request->otp_type;
        if ($request->driver == 'sms') {
            $patient->phone_number = $patient->phone;
            $smsService = new SmsService();
            $response = $smsService->sendOtp($patient->phone_number, $request->driver, $otp_type, $patient);
        } else {
            return 'error';
        }
        return $this->returnSuccessMessage([helperTrans('api.check your phone SMS please')]);
    }

    public function forgotPassword(ForgetPasswordRequest $request)
    {
        // $user = match ($request->user_type) {
        //     'patient' => Patient::where('phone', $request->phone)->first(),
        //     'provider' => Provider::where('phone', $request->phone)->first(),
        //     default => null
        // };

        $user = Patient::where('phone', $request->phone)->first();
        if (!$user) {
            return $this->returnErrorDataNotFound([helperTrans('api.User not found')]);
        }

        $otp_type = 'forgot-password';
        if ($request->driver === 'sms') {
            $user->phone_number = $user->phone;

            $smsService = new SmsService();
            $response = $smsService->sendOtp($user->phone_number, $request->driver, $otp_type, $user);

            return $this->returnSuccessMessage([helperTrans('api.check your phone SMS please')]);
        } elseif ($request->driver === 'whatsapp') {
            // Handle WhatsApp OTP
            try {
                $whatsappService = new WhatsappOtpService();
                $whatsappService->sendOtp($user, $request->driver, $otp_type);

                return $this->returnSuccessMessage([helperTrans('api.check your WhatsApp for the OTP')]);
            } catch (\Exception $e) {
                Log::error('Failed to send OTP via WhatsApp: ' . $e->getMessage());
                return $this->returnErrorDataNotFound(['Failed to send OTP via WhatsApp.']);
            }
        }

        return $this->returnErrorDataNotFound('Invalid driver.');
    }

    public function resetPassword(NewResetPasswordRequest $request)
    {

        $user = Patient::where('phone', $request['phone'])->where('status', 1)->first();
        if (!$user) {
            return $this->returnErrorDataNotFound([helperTrans('api.patient Not Found.')]);
        }
        $user->password = Hash::make($request['password']);
        $user->save();
        return $this->returnSuccessDataMessage([helperTrans('api.Password reset successfully')]);
    }
}
