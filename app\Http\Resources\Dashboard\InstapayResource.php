<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class InstapayResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'instapay_mobile_number' => $this->instapay_mobile_number,
            'instapay_username' => $this->instapay_username,
            'cheque_name' => $this->cheque_name,
        ];
    }
}
