<?php
namespace App\Http\Controllers\Admin;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
class CreateController extends Controller {
    public function __invoke($param = null) {
        if ($param) {
            switch ($param) {
                case 'doctor':
                    return redirect()->route('doctors.create');
                case 'hospital':
                    return redirect()->route('hospital.create');
                default:
                    abort(404);
            }
        } else {
            return view('Admin.CRUDS.createProvider.index');
        }
    }
}
