<?php

namespace App\Http\Controllers\Api\V1;

use App\Http\Controllers\Controller;
use App\Http\Resources\BookingResource;
use App\Http\Traits\Api_Trait;
use App\Models\Booking;
use App\Models\SpecializationBooking;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class BookingGeneralController extends Controller
{
    use Api_Trait;
    public function booking_details(Request $request, $booking_id)
    {
        if (!$booking_id) {
            return $this->returnErrorValidation([trans('api.booking_id_required')], 403);
        }

        $booking = Booking::with([
            'patient',
            'doctor',
            'mainService',
            'relative',
            'replying.replyingBookingDiagnoses',
            'replying.replyingBookingAnalysis',
            'replying.replyingBookingMedicals',
            'replying.replyingBookingRadiology',
        ])->find($request->booking_id ?? $booking_id);
        if (!$booking) {

            $booking = SpecializationBooking::with([
                'patient',
                'doctor',
                'relative',
            ])->find($request->booking_id ?? $booking_id);

            if (!$booking) {
                return $this->returnErrorDataNotFound([helperTrans('api.booking not found')]);
            }
        }


        $response_data = BookingResource::make($booking);

        return $this->returnData($response_data, [helperTrans('api.Booking Data')], 200);
    }
}
