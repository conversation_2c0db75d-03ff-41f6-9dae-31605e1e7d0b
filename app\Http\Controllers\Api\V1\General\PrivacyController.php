<?php

namespace App\Http\Controllers\Api\V1\General;

use App\Http\Controllers\Controller;
use App\Models\Privacy;
use App\Http\Traits\Api_Trait;
use App\Http\Resources\PrivacyResource;

class PrivacyController extends Controller
{
    use Api_Trait;
    public function index()
    {
        $parents = Privacy::with('children')->parent()->get();
        return $this->returnData(PrivacyResource::collection($parents), [helperTrans('api.privacy data')], 200);
    }
}
