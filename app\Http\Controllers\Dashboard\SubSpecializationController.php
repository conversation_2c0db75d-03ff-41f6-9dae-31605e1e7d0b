<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\SpecializationResource;
use App\Http\Traits\Api_Trait;
use App\Models\Specialization;
use Illuminate\Http\Request;

class SubSpecializationController extends Controller
{

    use Api_Trait;
    public function index()
    {
        $specializations = Specialization::whereNotNull('parent_id')->get();
        return $this->returnData(
            SpecializationResource::collection($specializations),
            [helperTrans('api.specializations data')],
            200
        );
    }

    public function store(Request $request)
    {
        $specialization = Specialization::create([
            'name' => $request->name,
            'parent_id' => $request->parent_id,
        ]);

        return $this->returnData(
            new SpecializationResource($specialization),
            [helperTrans('api.specialization created')],
            201
        );
    }

    public function show(Specialization $sub_specialization)
    {
        return $this->returnData(
            new SpecializationResource($sub_specialization),
            [helperTrans('api.specialization show')],
            200
        );
    }

    public function update(Request $request, Specialization $sub_specialization)
    {
         $sub_specialization->update([
            'name' => $request->name,
            'parent_id' => $request->parent_id,
        ]);

        return $this->returnData(
            new SpecializationResource($sub_specialization),
            [helperTrans('api.specialization updated')],
            200
        );
    }

    public function destroy(Specialization $sub_specialization)
    {
        $sub_specialization->delete();

        return response()->json([
            'status' => true,
            'message' => [helperTrans('api.specialization deleted')]
        ]);
    }
}