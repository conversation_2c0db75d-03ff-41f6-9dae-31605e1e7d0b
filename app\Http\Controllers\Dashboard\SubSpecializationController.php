<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Dashboard\SubSpecializationResource;
use App\Http\Resources\SpecializationResource;
use App\Http\Traits\Api_Trait;
use App\Models\Specialization;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class SubSpecializationController extends Controller
{

    use Api_Trait;
    public function index(Request $request)
    {
        //get all sub specializations depending on parameter of specialization id
        $validator = Validator::make(
            $request->all(),
            [
                'specialization_id' => 'nullable|exists:specializations,id',
            ],
            []
        );
        if ($validator->fails()) {
            return $this->returnErrorValidation(collect($validator->errors())->flatten(1), 403);
        }
        $specializations = Specialization::whereNotNull('parent_id');
        if ($request->specialization_id) {
            $specializations->where('parent_id', $request->specialization_id);
        }
        $specializations = $specializations->get();
        return $this->returnData(
            SubSpecializationResource::collection($specializations),
            [helperTrans('api.specializations data')],
            200
        );
    }

    public function store(Request $request)
    {
        $data = $request->only(['name', 'parent_id']); // name should be a translatable array

        $specialization = Specialization::create([
            'name' => $data['name'],
            'parent_id' => $data['parent_id'] ?? null,
        ]);

        return $this->returnData(
            new SubSpecializationResource($specialization),
            [helperTrans('api.specialization created')],
            200
        );
    }

    public function show(Specialization $sub_specialization)
    {
        return $this->returnData(
            new SubSpecializationResource($sub_specialization),
            [helperTrans('api.specialization show')],
            200
        );
    }

    public function update(Request $request, Specialization $sub_specialization)
    {
        $data = $request->only(['name', 'parent_id']); // name should be a translatable array

        $sub_specialization->update([
            'name' => $data['name'],
            'parent_id' => $data['parent_id'] ?? null,
        ]);

        return $this->returnData(
            new SubSpecializationResource($sub_specialization),
            [helperTrans('api.specialization updated')],
            200
        );
    }

    public function destroy(Specialization $sub_specialization)
    {
        $sub_specialization->delete();

        return response()->json([
            'status' => true,
            'message' => [helperTrans('api.specialization deleted')]
        ]);
    }
}
