<?php

namespace App\Http\Controllers\Api\V1\Patient;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Review\ReviewRequest;
use App\Http\Traits\Api_Trait;
use App\Models\Booking;
use App\Models\Doctor;
use App\Models\Patient;
use App\Models\Review;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class ReviewDoctorController extends Controller
{
    use Api_Trait;

    public function show(Request $request)
    {

        $data['total_rate'] = get_doctor_rate($request->doctor_id);
        if ($data) {
            return $this->returnData($data, 'Doctor Review');
        } else {
            return $this->returnError('400', [helperTrans('api.review not found')]);
        }
    }

    public function store(ReviewRequest $request)
    {
        DB::beginTransaction();
        try {
            $patientId = auth('patient')->user()->id;
            $booking = get_booking($request->booking_id, ['complete', 'reappoint', 'referral', 'specialized']);

            if (!$booking) {
                return $this->returnError([helperTrans('api.Booking not Found')]);
            }

            $old_review = Review::where([
                'booking_id' => $booking->id,
            ])->get();

            if (count($old_review)) {
                return $this->returnError([helperTrans('api.Booking Reviewed Before')]);
            }

            $review = Review::create([
                'doctor_id' => $booking->doctor_id,
                'patient_id' => $patientId,
                'booking_id' => $request->booking_id,
                'rating' => $request->rating,
                'comment' => $request->comment,
            ]);

            $averageRating = Review::where('doctor_id', $request->doctor_id)->avg('rating');

            $booking->doctor->update(['total_rate' => round($averageRating, 1)]);

            $review['total_rate'] = get_doctor_rate($booking->doctor_id);
            DB::commit();
            return $this->returnData($review, [helperTrans('api.added successfully')]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->returnError($e, [helperTrans('api.something went wrong')]);
        }
    }
}
