<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Resources\Json\JsonResource;

class ServiceCategoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'name_ar' =>   $this->getTranslation('name', 'ar'),
            'name_en' =>  $this->getTranslation('name', 'en'),
            'sub_categories' => ServiceSubCategoryResource::collection($this->subCategories),

        ];
    }
}
