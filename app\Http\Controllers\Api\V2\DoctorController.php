<?php

namespace App\Http\Controllers\Api\V2;

use App\Http\Controllers\Controller;
use App\Http\Resources\Api\V2\DoctorDetailsResource;
use App\Http\Resources\Api\V2\DoctorLevelResource;
use App\Http\Resources\Api\V2\DoctorResource;
use App\Http\Resources\Api\V2\DoctorReviewsResource;
use App\Http\Resources\Api\V2\DoctorStatisticsResource;
use App\Http\Traits\Api_Trait;
use App\Models\City;
use App\Models\Doctor;
use App\Models\DoctorLevel;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class Doctor<PERSON>ontroller extends Controller
{
    use Api_Trait;


    public function levels()
    {
        $levels = DoctorLevel::select('id', 'name')->get();
        return $this->returnData(DoctorLevelResource::collection($levels), [helperTrans('api.doctors levels data')], 200);
    }

    public function doctors(Request $request)
    {
        $per_page = $request->per_page ?? 10;
        $doctors = Doctor::where('status', 1)->where('doctor_type', '<>', 'general');

        if ($request->specialization_id) {
            $specialization_id   = $request->input('specialization_id');
            $doctors->where('specialization_id', $specialization_id);
        }



        if ($request->gender) {
            $doctors->where('gender', $request->gender);
        }
        if ($request->title) {
            $doctors->where('doctor_level_id', $request->title);
        }

        if (!empty($rquest->fees)) {
            $doctors->whereHas('provider_branches', function ($query) use ($request) {
                $query->whereBetween('price', $request->fees);
            });
        }
        if ($request->ll && $request->lg) {
            $lat = $request->ll;
            $lng = $request->lg;

            // Use HAVERSINE with fallback NULL
            $doctors->selectRaw("
                doctors.*,
                (
                    CASE 
                        WHEN latitude IS NOT NULL AND longitude IS NOT NULL THEN (
                            6371 * acos(
                                cos(radians(?)) * 
                                cos(radians(latitude)) * 
                                cos(radians(longitude) - radians(?)) + 
                                sin(radians(?)) * 
                                sin(radians(latitude))
                            )
                        )
                        ELSE NULL
                    END
                ) as distance
            ", [$lat, $lng, $lat]);

            // Sort by distance (NULLs last)
            $doctors->orderByRaw('distance IS NULL') // FALSE (0) comes first (valid coords), TRUE (1) last
                ->orderBy('distance');
        }

        $doctors = $doctors->paginate($per_page);
        return $this->paginatedResponse(DoctorResource::collection($doctors), helperTrans('api.doctors data'));
    }

    public function generalDoctors(Request $request)
    {
        $per_page = $request->per_page ?? 10;
        $user = auth('doctor')->user();
        $doctors = Doctor::where('status', 1)->where('doctor_type', 'general');
        if ($user) {
            $doctors = $doctors->where('id', '!=', $user->id);
        }
        $doctors = $doctors->paginate($per_page);


        return $this->paginatedResponse(DoctorResource::collection($doctors), helperTrans('api.doctors data'));
    }

    public function doctorDetails($id)
    {
        $doctor = Doctor::with(['specialization', 'times', 'doctor_branch', 'reviews'])->where('id', $id)->where('status', true)->first();
        if (!$doctor) {
            return $this->returnError([helperTrans('api.doctor not found')], 404);
        }
        $data = [
            "basic_information" => new  DoctorDetailsResource($doctor),
            "statistics" => new DoctorStatisticsResource($doctor),
            'doctor_branches' => $doctor->provider_branches(),
            'reviews' => DoctorReviewsResource::collection($doctor->reviews),

        ];
        // dd('gg');
        return $this->returnData($data, [helperTrans('api.doctor data')], 200);
    }
}
