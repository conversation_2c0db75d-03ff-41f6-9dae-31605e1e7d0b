<?php

namespace Database\Seeders;

use App\Models\PaymentMethod;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Schema;

class PaymentMethodSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $paymentMethods = [
            [
                'id' => 1,
                'name' => 'Cash',
                'notes' => 'Cash',
            ],
            [
                'id' => 2,
                'name' => 'Online',
                'notes' => 'Online',
            ],
            [
                'id' => 3,
                'name' => 'Both',
                'notes' => 'Both',
            ],
        ];

        Schema::disableForeignKeyConstraints();
        PaymentMethod::truncate();
        Schema::enableForeignKeyConstraints();
        PaymentMethod::insert($paymentMethods);
    }
}
