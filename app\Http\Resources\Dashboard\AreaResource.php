<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AreaResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'name' => $this->getTranslations('name'),
            'name_current' => $this->name, // Current locale name

            // Individual language names for easier access
            'name_ar' => $this->getTranslation('name', 'ar'),
            'name_en' => $this->getTranslation('name', 'en'),

            // Display name based on current locale
            'display_name' => $this->name,

            // City relationship
            'city_id' => $this->city_id,
            'city' => $this->city ? [
                'id' => $this->city->id,
                'name' => $this->city->name,
                'name_ar' => $this->city->getTranslation('name', 'ar'),
                'name_en' => $this->city->getTranslation('name', 'en'),
                'governorate_id' => $this->city->governorate_id,
                'governorate_name' => $this->city->governorate ? $this->city->governorate->name : null,
            ] : null,

            // Governorate through city
            'governorate' => $this->city && $this->city->governorate ? [
                'id' => $this->city->governorate->id,
                'name' => $this->city->governorate->name,
                'name_ar' => $this->city->governorate->getTranslation('name', 'ar'),
                'name_en' => $this->city->governorate->getTranslation('name', 'en'),
            ] : null,

            // Check if translations exist
            'has_arabic_name' => !empty($this->getTranslation('name', 'ar')),
            'has_english_name' => !empty($this->getTranslation('name', 'en')),

            // Available translations
            'available_translations' => $this->getAvailableTranslations(),

            // Location information
            'has_city' => !is_null($this->city_id),
            'location_display' => $this->getLocationDisplay(),

            // Timestamps
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'created_at_human' => $this->created_at?->diffForHumans(),
            'updated_at_human' => $this->updated_at?->diffForHumans(),
        ];
    }

    /**
     * Get available translations for the area
     *
     * @return array
     */
    private function getAvailableTranslations()
    {
        $translations = [];
        $nameTranslations = $this->getTranslations('name');

        foreach ($nameTranslations as $locale => $translation) {
            if (!empty($translation)) {
                $translations[] = $locale;
            }
        }

        return $translations;
    }

    /**
     * Get location display string
     *
     * @return string|null
     */
    private function getLocationDisplay()
    {
        if (!$this->city) {
            return $this->name;
        }

        $parts = [$this->name];

        if ($this->city) {
            $parts[] = $this->city->name;
        }

        if ($this->city && $this->city->governorate) {
            $parts[] = $this->city->governorate->name;
        }

        return implode(', ', array_filter($parts));
    }
}
