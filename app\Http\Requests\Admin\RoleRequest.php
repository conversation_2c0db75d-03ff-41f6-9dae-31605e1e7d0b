<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class RoleRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        if ($this->id) {

            $rules['name'] = 'required';

        } else {
        
            $rules = [
                'name' => 'required',
                'permissions' => 'required',
            ];
    
        }//end of if

        return $rules;

    }//end of rules

}//end of request
