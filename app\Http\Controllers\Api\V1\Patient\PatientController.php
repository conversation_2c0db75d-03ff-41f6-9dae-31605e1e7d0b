<?php

namespace App\Http\Controllers\Api\V1\Patient;

use App\Http\Controllers\Controller;
use App\Http\Requests\Api\PatientRequest;
use Illuminate\Http\Request;
use App\Http\Traits\Api_Trait;
use App\Http\Resources\PatientResource;
use Illuminate\Support\Arr;
use App\Models\Patient;

class PatientController extends Controller
{
    use Api_Trait;
    public function profile(Request $request)
    {
        $patient = auth('patient')->user();
        return $this->returnData(PatientResource::make($patient), 'Profile data retrieved successfully');
    }
    public function update_profile(PatientRequest $request)
    {
        /** @var Patient $patient */

        $patient = auth('patient')->user();
        $validated = $request->validated();
        $data = Arr::except($validated, 'image');
        if (isset($validated['image'])) {
            $data['image'] = uploadFile('patients', $validated['image'], $patient->image);
        }
        $patient->update($data);
        return $this->returnData(PatientResource::make($patient), 'Profile updated successfully');
    }
}
