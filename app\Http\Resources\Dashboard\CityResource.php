<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CityResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'name' => $this->getTranslation('name', session_lang()),
            'governorate_id' => $this->governorate_id,
            'nationality_id' => $this->nationality_id,
            'governorate_name' => optional($this->governorate)->getTranslation('name', session_lang()) ?? '',
            'nationality_name' => optional($this->nationality)->getTranslation('name', session_lang()) ?? '',
        ];
    }
}
