<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Provider;
use App\Models\Service;
use App\Models\ProviderService;
use App\Models\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Yajra\DataTables\Facades\DataTables;
use Illuminate\Support\Facades\Storage;

class ProviderServiceController extends Controller
{
    public function index(Provider $provider, Request $request)
    {
        if (request()->ajax()) {
            $providerServices = ProviderService::with(['service', 'provider'])
                ->where('provider_id', $provider->id)->latest();

            return DataTables::of($providerServices)
                ->addColumn('action', function ($providerService) use ($provider) {
                    $html = '<div>
                            <a class="dropdown-item remove-item-btn" href="javascript:void(0)" data-id="' . $providerService->id . '">
                                <i class="ri-delete-bin-fill align-bottom me-2 text-muted"></i> ' . helperTrans('admin.delete') . '
                            </a>
                </div>';
                    return $html;
                })
                ->addColumn('name_en', function ($providerService) {
                    return $providerService->service->getTranslation('name', 'en');
                })
                ->addColumn('name_ar', function ($providerService) {
                    return $providerService->service->getTranslation('name', 'ar');
                })
                ->addColumn('price', function ($providerService) {
                    return $providerService->price;
                })->editColumn('created_at', function ($providerService) {
                    return $providerService->created_at->format('Y-m-d H:i');
                })->editColumn('discount', function ($providerService) {
                    return $providerService->discount;
                })
                ->editColumn('code', function ($providerService) {
                    return $providerService->service->code;
                })
                ->filter(function ($query) use ($request) {
                    if ($search = $request->get('search')['value'] ?? null) {
                        $locale = app()->getLocale();

                        $query->whereHas('service', function ($q) use ($search, $locale) {
                            $q->where("name", 'like', "%{$search}%")->orWhere('code', 'like', "%{$search}%");
                        });
                    }
                })

                ->rawColumns(['action'])
                ->make(true);
        }

        // Get available services for the provider's category
        $serviceCategoryId = ServiceCategory::where('provider_category_id', $provider->provider_category_id)->first()->id;
        $services = Service::where('service_category_id', $serviceCategoryId)->get();

        return view('Admin.CRUDS.new_providers.services.index', compact('provider', 'services'));
    }

    public function create(Provider $provider)
    {
        $serviceCategories = ServiceCategory::all();
        return view('Admin.CRUDS.new_providers.services.create', compact('provider', 'serviceCategories'));
    }

    public function store(Request $request, Provider $provider)
    {
        $validated = $request->validate([
            'service_id' => 'required|exists:services,id',
            'price' => 'required|numeric|min:0',
        ]);

        // Check if service already exists for this provider
        $existingService = ProviderService::where('provider_id', $provider->id)
            ->where('service_id', $validated['service_id'])
            ->first();

        if ($existingService) {
            return response()->json([
                'status' => false,
                'message' => helperTrans('admin.service_already_exists')
            ], 422);
        }

        // Create new provider service
        ProviderService::create([
            'provider_id' => $provider->id,
            'service_id' => $validated['service_id'],
            'price' => $validated['price']
        ]);

        return response()->json([
            'status' => true,
            'message' => helperTrans('admin.service_created_successfully')
        ]);
    }

    public function edit(Provider $provider, ProviderService $service)
    {
        $serviceCategories = ServiceCategory::all();
        return view('Admin.CRUDS.new_providers.services.edit', compact('provider', 'service', 'serviceCategories'));
    }

    public function update(Request $request, Provider $provider, ProviderService $service)
    {
        $validated = $request->validate([
            'name.ar' => 'required|string|max:255',
            'name.en' => 'required|string|max:255',
            'service_category_id' => 'required|exists:service_categories,id',
            'price' => 'required|numeric|min:0',
            'discount' => 'nullable|numeric|min:0|max:100',
            'duration' => 'required|integer|min:1',
            'status' => 'required|in:active,inactive',
            'description.ar' => 'nullable|string',
            'description.en' => 'nullable|string',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048'
        ]);

        $service->service_category_id = $validated['service_category_id'];
        $service->name = $validated['name'];
        $service->description = $validated['description'] ?? ['ar' => '', 'en' => ''];
        $service->price = $validated['price'];
        $service->discount = $validated['discount'] ?? 0;
        $service->duration = $validated['duration'];
        $service->status = $validated['status'] === 'active';

        if ($request->hasFile('image')) {
            // Delete old image if exists
            if ($service->image) {
                Storage::disk('public')->delete($service->image);
            }
            $service->image = $request->file('image')->store('services', 'public');
        }

        $service->save();

        return redirect()
            ->route('admin.provider_services.index', ['provider' => $provider->id])
            ->with('success', helperTrans('admin.service_updated_successfully'));
    }

    public function destroy(Provider $provider, ProviderService $service)
    {
        if ($service->image) {
            Storage::disk('public')->delete($service->image);
        }

        $service->delete();

        return response()->json([
            'status' => true,
            'message' => helperTrans('admin.service_deleted_successfully')
        ]);
    }

    public function getServicesData(Provider $provider)
    {
        $services = $provider->services()->select(['services.id', 'services.name', 'services.duration', 'services.status', 'provider_service.price']);

        return DataTables::of($services)
            ->addIndexColumn()
            ->addColumn('action', function ($service) use ($provider) {
                return '
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-info edit-service" data-id="' . $service->id . '">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-danger delete-service" data-id="' . $service->id . '">
                            <i class="fas fa-trash"></i>
                        </button>
                    </div>';
            })
            ->rawColumns(['action'])
            ->make(true);
    }

    /**
     * Update the price of a provider service.
     */
    public function updatePrice(Request $request, Provider $provider, $serviceId)
    {
        $validator = Validator::make($request->all(), [
            'price' => 'required|numeric|min:0',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $providerService = ProviderService::findOrFail($serviceId);
        // Update the pivot table price
        $providerService->update(['price' => $request->price]);

        return response()->json([
            'status' => true,
            'message' => helperTrans('admin.price_updated_successfully')
        ]);
    }

    public function updateDiscount(Request $request, Provider $provider, $serviceId)
    {
        $validator = Validator::make($request->all(), [
            'discount' => 'required|numeric|min:0|max:100',
        ]);

        if ($validator->fails()) {
            return response()->json(['errors' => $validator->errors()], 422);
        }

        $providerService = ProviderService::findOrFail($serviceId);
        // Update the pivot table price
        $providerService->update(['discount' => $request->discount]);

        return response()->json([
            'status' => true,
            'message' => helperTrans('admin.discount_updated_successfully')
        ]);
    }
}
