<?php

namespace App\Http\Resources\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DoctoriaEmployeeResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'name' => $this->name,
            'phone' => $this->phone,
            'email' => $this->email,
            'position' => $this->position,
            
            // Computed attributes
            'display_name' => $this->display_name,
            'full_contact' => $this->full_contact,
            
            // Contact information availability
            'has_phone' => !empty($this->phone),
            'has_email' => !empty($this->email),
            'has_position' => !empty($this->position),
            
            // Formatted contact information
            'formatted_phone' => $this->phone ? $this->formatPhone($this->phone) : null,
            'formatted_email' => $this->email ? strtolower($this->email) : null,
            
            // Timestamps
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            'created_at_human' => $this->created_at?->diffForHumans(),
            'updated_at_human' => $this->updated_at?->diffForHumans(),
        ];
    }

    /**
     * Format phone number for display
     *
     * @param string $phone
     * @return string
     */
    private function formatPhone($phone)
    {
        // Remove all non-numeric characters except +
        $cleaned = preg_replace('/[^+0-9]/', '', $phone);
        
        // If it starts with +, keep it
        if (str_starts_with($cleaned, '+')) {
            return $cleaned;
        }
        
        // If it's a local number, format it nicely
        if (strlen($cleaned) >= 10) {
            return preg_replace('/(\d{3})(\d{3})(\d{4})/', '$1-$2-$3', $cleaned);
        }
        
        return $cleaned;
    }
}
