<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\SpecializationResource;
use App\Http\Traits\Api_Trait;
use App\Models\Specialization;
use Illuminate\Http\Request;

class SpecializationController extends Controller
{

    use Api_Trait;
    public function index()
    {
        $specializations = Specialization::get();
        return $this->returnData(
            SpecializationResource::collection($specializations),
            [helperTrans('api.specializations data')],
            200
        );
    }

    public function store(Request $request)
    {
        $specialization = Specialization::create([
            'name' => $request->name,
        ]);

        return $this->returnData(
            new SpecializationResource($specialization),
            [helperTrans('api.specialization created')],
            201
        );
    }

    public function show(Specialization $specialization)
    {
        return $this->returnData(
            new SpecializationResource($specialization),
            [helperTrans('api.specialization show')],
            200
        );
    }

    public function update(Request $request, Specialization $specialization)
    {
        $specialization->update([
            'name' => $request->name,
        ]);

        return $this->returnData(
            new SpecializationResource($specialization),
            [helperTrans('api.specialization updated')],
            200
        );
    }

    public function destroy(Specialization $specialization)
    {
        $specialization->delete();

        return response()->json([
            'status' => true,
            'message' => [helperTrans('api.specialization deleted')]
        ]);
    }
}