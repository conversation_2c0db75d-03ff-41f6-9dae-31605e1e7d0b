<?php

namespace App\Http\Controllers\Admin\GeneralMainSetting;

use App\Enums\Branch\BranchFromType;
use App\Enums\Branch\BranchStatus;
use App\Enums\Branch\BranchType;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\AddBranchRequest;
use Illuminate\Http\Request;
use App\Http\Traits\{ResponseTrait, Upload_Files};
use App\Models\{Branch, Day, Doctor, DoctorBranch, Governorate, ProviderTime};
use Illuminate\Support\Facades\{Storage, DB, Hash};
use Yajra\DataTables\DataTables;

class BranchController extends Controller
{
    use  ResponseTrait, Upload_Files;
    public function index(Request $request)
    {
        if ($request->ajax()) {
            $branchs = DoctorBranch:: // تأكد من وجود العلاقة هنا
                // ->where('doctor_id', $id)
                latest();
            return DataTables::of($branchs)
                ->addColumn('action', function ($branch) {
                    $delete = '';
                    return '
                            <button ' . $delete . '  class="btn rounded-pill btn-danger waves-effect waves-light delete"
                                    data-id="' . $branch->id . '">
                            <span class="svg-icon svg-icon-3">
                                <span class="svg-icon svg-icon-3">
                                    <i class="las la-trash-alt"></i>
                                </span>
                            </span>
                            </button>
                       ';
                })
                ->editColumn('status', function ($branch) {
                    $status = $branch->status;
                    $badgeClass = $status === 'active' ? 'badge-success' : 'badge-danger';
                    return '<span class="badge ' . $badgeClass . '">' . $status . '</span>';
                })
                ->editColumn('created_at', function ($branch) {
                    return date('Y/m/d', strtotime($branch->created_at));
                })
                ->escapeColumns([])
                ->make(true);
        }
        return view('Admin.CRUDS.branch.index');
    }

    public function create(Request $request)
    {
        $doctor_id = $request->segment(4);
        $doctor = Doctor::find($doctor_id);
        //dd($doctor);
        $governorates = Governorate::get();
        $activeBranches = Branch::active()->get();
        $this->createBranch($doctor_id);
        return view('Admin.CRUDS.branch.create', compact('governorates', 'activeBranches', 'doctor_id', 'doctor'));
    }

    public function createBranch($doctor_id)
    {
        $doctor = Doctor::find($doctor_id);
        $governorates = Governorate::get();
        $activeBranches = Branch::active()->get();
        // dd([$doctor_id, $doctor]);
        return view('Admin.CRUDS.branch.create', compact('governorates', 'activeBranches', 'doctor_id', 'doctor'));
    }


    public function store(AddBranchRequest $request)
    {
        $validated = $request->validated();
        DB::transaction(function () use ($request) {
            $workingHours = [];
            if ($request->has('working_days')) {
                foreach ($request->working_days as $day) {
                    $workingHours[$day] = [
                        'start_time' => $request->input('start_time')[$day] ?? null,
                        'end_time' => $request->input('end_time')[$day] ?? null,
                    ];
                }
            }
            $exceptedColumn = [
                'contract_start_date',
                'contract_end_date',
                'attach_contract',
                'attach_documents',
                'attach_price_list',
                'price_list_discount',
                'contract_note',
                'home_care_gross_price',
                'home_care_discount',
                'home_care_net_price',
                'service_clinic_gross_price',
                'service_clinic_discount',
                'service_clinic_net_price',
                'service_online_gross_price',
                'service_online_discount',
                'service_online_net_price',
                'price_list_outpatient',
                'price_list_intpatient',
                'clamis_due_date',
                'admin_fees',
                'bank_name',
                'bank_account_number',
                'bank_iban',
                'bank_swift_code',
                'bank_branch_bank',
                'bank_mobile_number',
                'e_wallet_name',
                'e_wallet_mobile_number',
                'instapay_mobile_number',
                'instapay_email',
                'cheque_name',
            ];
            $data = $request->except(array_merge($exceptedColumn, [
                'store',
                'status',
                'email',
                'tel',
                'whatsapp',
                'password',
                'notes',
                'address',
                'working_days',
                'start_time',
                'end_time'
            ]));

            if ($request->hasFile('image')) {
                $data["image"] = $this->uploadFiles('branchs', $request->file('image'), null);
            }

            $dataDoctor = json_decode($request->doctor_id);
            if ($dataDoctor) {
                $data['doctor_id'] = $dataDoctor;
            }
            $data['password'] = bcrypt($request->password);
            $data['status'] =  true ? 1 : 0;
            $data['type'] = $request->has('type') ? BranchType::SUBBRANCH : BranchType::Master;
            $data['from_type'] = BranchFromType::BRANCH;
            $data['location'] = $request->address;
            $data['price'] = $request->service_clinic_gross_price;
            try {
                $doctorId = $dataDoctor;
                // dd($request['address']);
                $branch = DoctorBranch::create([
                    "doctor_id" => $doctorId,
                    "name" => json_encode($request['name']),
                    "phone" => $request['phone'],
                    "governorate_id" => $request['governorate_id'],
                    "latitude" => $request['latitude'],
                    "longitude" => $request['longitude'],
                    "about" => $request['about'],
                    "location" => json_encode($request['address']),
                    "price" =>  $request['service_clinic_gross_price'],
                    "status" => (int) $request['status'],
                    "email" =>  $request['email'],
                    "whatapp" => $request['whatsapp'],
                    "tel" => $request['tel'],
                    "password" => Hash::make($request['password'])
                ]);
                // $branch = DoctorBranch::create($data);
                foreach ($workingHours as $key => $workingtime) {
                    $dayId = Day::where('day->en', strtolower($key))->first()->id;
                    ProviderTime::create([
                        'provider_id' => $branch->id,
                        'provider_type' => 'doctor_branch',
                        'day_id' => $dayId,
                        'from_time' => $workingtime['start_time'] ?? null,
                        'to_time' => $workingtime['end_time'] ?? null,
                        'type' => 'offline',
                        // 'doctor_branches_id' => $branch->id ?? null
                    ]);
                }
                // $branch = Branch::create($data);
            } catch (\Exception $e) {
                dd($e->getMessage());
                // return redirect()->back()->with('fail' , 'Failed to create branch: ' . $e->getMessage());
            }
            $contractData = [
                'working_hours' => json_encode($workingHours),
                'branch_id' => $branch->id,
                'contract_start_date' => $request->contract_start_date,
                'contract_end_date' => $request->contract_end_date,
                'attach_contract' => $request->attach_contract,
                'attach_documents' => $request->attach_documents,
                'attach_price_list' => $request->attach_price_list,
                'home_care_gross_price' => $request->home_care_gross_price,
                'home_care_discount' => $request->home_care_discount,
                'service_home_care_price' => $request->service_home_care_price,
                'home_care_net_price' => $request->home_care_net_price,
                'service_clinic_price' => $request->service_clinic_price,
                'service_clinic_gross_price' => $request->service_clinic_gross_price,
                'service_clinic_discount' => $request->service_clinic_discount,
                'service_clinic_net_price' => $request->service_clinic_net_price,
                'service_online_gross_price' => $request->service_online_gross_price,
                'service_online_discount' => $request->service_online_discount,
                'service_online_net_price' => $request->service_online_net_price,
                'price_list_outpatient' => $request->price_list_outpatient,
                'price_list_intpatient' => $request->price_list_intpatient,
                'contract_note' => $request->contract_note,
                'clamis_due_date' => $request->clamis_due_date,
                'admin_fees' => $request->admin_fees,
                'bank_name' => $request->bank_name,
                'bank_account_number' => $request->bank_account_number,
                'bank_iban' => $request->bank_iban,
                'bank_swift_code' => $request->bank_swift_code,
                'bank_branch_bank' => $request->bank_branch_bank,
                'bank_mobile_number' => $request->bank_mobile_number,
                'e_wallet_name' => $request->e_wallet_name,
                'e_wallet_mobile_number' => $request->e_wallet_mobile_number,
                'instapay_mobile_number' => $request->instapay_mobile_number,
                'instapay_email' => $request->instapay_email,
                'cheque_name' => $request->cheque_name,
            ];
            // $branch->info()->create($contractData);
        });
        return redirect()->route('admin.doctors_branches.index', $request['doctor_id'])->with('success', 'created success');
    }

    // public function store(AddBranchRequest $request)
    // {
    //     $validated = $request->validated();
    //     $doctorId = json_decode($request['doctor_id'])->id;  // This extracts the 'id' value
    //     $branch = DoctorBranch::create([
    //         "doctor_id" => $doctorId,
    //         "name" => $request['name'],
    //         "phone" => $request['phone'],
    //         "governorate_id" => $request['governorate_id'],
    //         "latitude" => $request['latitude'],
    //         "longitude" => $request['longitude'],
    //         "about" => $request['about'],
    //         "location" => $request['location'],
    //         "price" => 100
    //     ]);
    //     return redirect()->back()->with('success', 'created success');
    // }



}
