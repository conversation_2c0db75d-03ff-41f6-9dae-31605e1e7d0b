<?php

namespace App\Http\Resources\Dashboard;

use App\Http\Resources\CityResource;
use App\Http\Resources\CountryResource;
use App\Http\Resources\GovernorateResource;
use App\Http\Resources\ProviderTimeResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Carbon;

class ProviderBranchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => (int)$this->id,
            'name_ar' => $this->getTranslation('name', 'ar'),
            'name_en' => $this->getTranslation('name', 'en'),
            'phone' => $this->phone,
            'longitude' => $this->longitude,
            'latitude' => $this->latitude,
            'location_ar' => $this->getTranslation('location', 'ar'),
            'location_en' => $this->getTranslation('location', 'en'),
            'image' => get_file($this->provider->image),
            'degree' => $this->degree,
            'status' => $this->status,
            'work_number' => $this->work_number,
            'whatsapp_number' => $this->whatsapp_number,
            'tel1' => $this->tel1,
            'tel2' => $this->tel2,
            'email' => $this->email,
            'area_ar' => $this->getTranslation('area', 'ar'),
            'area_en' => $this->getTranslation('area', 'en'),
            'website_link' => $this->website_link,
            'directors' => DirectorResource::collection($this->directors),
            'imported_file_id' => $this->imported_file_id,
            'country' => CountryResource::make($this->country),
            'city' => CityResource::make($this->city),
            'governorate' => GovernorateResource::make($this->governorate),
            'times' => ProviderTimeResource::collection($this->times()->get()),
            'created_at' => Carbon::parse($this->created_at)->toDateString(),
        ];
    }
}
