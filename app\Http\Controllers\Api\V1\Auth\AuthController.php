<?php

namespace App\Http\Controllers\Api\V1\Auth;

use App\Exceptions\DoctoriaException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\Auth\ChangePasswordRequest;
use App\Http\Requests\Api\Auth\LoginRequest;
use App\Http\Requests\Api\Auth\LogoutRequest;
use App\Http\Requests\Api\Auth\RegisterRequest;
use App\Http\Requests\Api\Auth\UpdateProfileRequest;
use App\Http\Resources\AuthDoctorResource;
use App\Http\Resources\AuthPatientResource;
use App\Http\Resources\DoctorResource;
use App\Http\Resources\PatientResource;
use App\Http\Resources\ProviderResource;
use App\Http\Traits\Api_Trait;
use App\Http\Traits\Upload_Files;
use App\Models\Patient;
use App\Services\SmsService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Tymon\JWTAuth\Facades\JWTAuth;
use App\Models\FirebaseToken;
use App\Models\Relative;
use App\Services\Otp\WhatsappOtpService;

class AuthController extends Controller
{
    use Api_Trait, Upload_Files;

    public function __construct(
        protected $resource = AuthPatientResource::class,
        protected $model = new Patient(),
    ) {}

    public function login(LoginRequest $request)
    {
        try {

            // Try Login as a patient
            /** @var \App\Models\Patient $patient */
            $patient = patient();
            if ($patient && $token = $patient->attempt($request->all(), 1)) {
                $patient = patient()->user();
                if ($patient->status == 0) {
                    return $this->returnError([helperTrans('api.The patient Account Is Inactive')]);
                }
                $patient->token = $token;
                $patient->type = 'patient';
                return $this->returnData($this->resource::make($patient), [helperTrans('api.login successfully')]);
            }

            // Try Login as a doctor
            /** @var \App\Models\Doctor $doctor */
            $doctor = doctor();
            if ($doctor && $token = $doctor->attempt($request->all(), 1)) {
                $doctor = doctor()->user();
                if ($doctor->status == 0) {
                    return $this->returnError([helperTrans('api.The doctor Account Is Inactive')]);
                }
                $doctor->token = $token;
                $doctor->type = $doctor->doctor_type;
                return $this->returnData($this->resource::make($doctor), [helperTrans('api.login successfully')]);
            }

            // Try Login as a provider
            $provider = provider();

            if ($provider && $token = $provider->attempt($request->all(), 1)) {
                $provider = provider()->user();
                // if ($provider->status == 0) {
                //     return $this->returnError([helperTrans('api.The provider Account Is Inactive')]);
                // }
                $provider->token = $token;
                $provider->type = "provider";
                return $this->returnData($this->resource::make($provider), [helperTrans('api.login successfully')]);
            }
            return $this->returnError([helperTrans('api.Phone Number Or Password Is Incorrect')], 422);
        } catch (\Throwable $th) {
            return $this->returnError([helperTrans('api.Phone Number Or Password Is Incorrect')], 422);
        }
    }

    public function signup(RegisterRequest $request)
    {
        if ($request->driver == 'sms') {
            $data = $request->except(['driver', 'otp_type', 'password_confirmation']);
            $data['password'] = Hash::make($request->password);
            $data['status'] = 0;
            $otp_type = 'register';

            if (!empty($request->card_id)) {

                $patient = Patient::where('card_id', $request->card_id)->first();

                if ($patient) {

                    if (empty($patient->password)) {
                        $patient->update([
                            'phone' => $request->phone,
                            'password' => $data['password']
                        ]);
                        Relative::where('card_id', $request->card_id)->first()?->delete();
                    } else {
                        return $this->returnError([helperTrans('api.This Card ID is already registered')], 422);
                    }
                } else {
                    return $this->returnError([helperTrans('api.This Card ID is not valid')], 422);
                }
            } else {

                $patient = Patient::create($data);
            }



            $token = JWTAuth::fromUser($patient);
            $patient->token = $token;

            $patient->phone_number = $patient->phone;
            $smsService = new SmsService();
            $response = $smsService->sendOtp($patient->phone_number, $request->driver, $otp_type, $patient);
            return $this->returnSuccessMessage([helperTrans('api.check your phone SMS please')]);
        } elseif ($request->driver == 'whatsapp') {
            //  WhatsApp
            $data = $request->except(['driver', 'otp_type', 'password_confirmation']);
            $data['password'] = Hash::make($request->password);
            $data['status'] = 0;
            $otp_type = 'register';

            if (!empty($request->card_id)) {

                $patient = Patient::where('card_id', $request->card_id)->first();

                if ($patient) {

                    if (empty($patient->password)) {
                        $patient->update([
                            'phone' => $request->phone,
                            'password' => $data['password']
                        ]);
                        Relative::where('card_id', $request->card_id)->first()?->delete();
                    } else {
                        return $this->returnError([helperTrans('api.This Card ID is already registered')], 422);
                    }
                } else {
                    return $this->returnError([helperTrans('api.This Card ID is not valid')], 422);
                }
            } else {

                $patient = Patient::create($data);
            }



            $token = JWTAuth::fromUser($patient);
            $patient->token = $token;

            $patient->phone_number = $patient->phone;
            $whatsappService = new WhatsappOtpService();
            $response = $whatsappService->sendOtp($patient, $request->driver, $otp_type);
            return $this->returnSuccessMessage([helperTrans('api.check your whatsapp please')]);
        }
        return $this->returnSuccessMessage([helperTrans('api.check your  please')]);
    }
    public function check_card_id(Request $request)
    {
        $patient = Patient::where('card_id', $request->card_id)->first();
        if ($patient) {
            if ($patient->status == 1) {
                return $this->returnError([helperTrans('api.This Card ID is already registered')], 422);
            }
            return $this->returnData(PatientResource::make($patient), [helperTrans('api.This Card ID is valid')]);
        } else {
            return $this->returnError([helperTrans('api.This Card ID is not valid')], 422);
        }
    }

    public function logout(Request $request)
    {

        /** @var \App\Models\Patient $patient */
        $patient = auth('patient')->user();

        /** @var \App\Models\Doctor $doctor */
        $doctor = auth('doctor')->user();

        $provider = auth('provider')->user();

        if (!$patient && !$doctor && !$provider) {
            return $this->returnError([helperTrans('api.Unauthorized')], 401);
        }

        if ($patient) {
            $user = $patient;
            $user_type = 'patient';
        } else if ($doctor) {
            $user = $doctor;
            $user_type = 'doctor';
        } else if ($provider) {
            $user = $provider;
            $user_type = 'provider';
        }
        // $user->token = null;
        // $user->currentAccessToken()->delete();
        // $user->tokens()->delete();


        //$token = FirebaseToken::where('user_type', $user_type)->where('user_id', $user->id)->where('token', $request->device_token)->delete();

        return $this->returnSuccessMessage([helperTrans('api.logged out successfully')]);
    }

    public function myProfile(Request $request)
    {
        $user = auth('patient')->user();
        if (!$user) {
            return $this->returnError([helperTrans('api.Unauthorized')], 401);
        }
        return  $this->returnData(PatientResource::make($user), [helperTrans('api.profile Data')]);
    }

    public function changePassword(ChangePasswordRequest $request)
    {

        /** @var \App\Models\Patient $patient */
        $patient = auth('patient')->user();

        /** @var \App\Models\Doctor $doctor */
        $doctor = auth('doctor')->user();

        /** @var \App\Models\Provider $provider */
        $provider = auth('provider')->user();

        if (!$patient && !$doctor && !$provider) {
            return $this->returnError([helperTrans('api.Unauthorized')], 401);
        }

        $user = $patient ?? $doctor ?? $provider;

        if (!Hash::check($request->old_password, $user->password)) {
            return $this->returnError([helperTrans('api.Old password is incorrect')], 422);
        }
        if (Hash::check($request->new_password, $user->password)) {
            return $this->returnError([helperTrans('api.New password cannot be the same as the old password')], 422);
        }

        $user->password = Hash::make($request->new_password);

        $user->save();
        $resource = null;
        // Dynamically choose resource class
        if ($patient) {
            $resource = new PatientResource($user);
        } elseif ($doctor) {
            $resource = new DoctorResource($user);
        } elseif ($provider) {
            $resource = new ProviderResource($user);
        }

        return $this->returnData($resource, [helperTrans('api.change password successfully')]);
        // return  $this->returnData(PatientResource::make($user), [helperTrans('api.change password successfully')]);
    }

    public function updateMyProfile(UpdateProfileRequest $request)
    {
        /**  @var \App\Models\Patient $user */
        $user = auth('patient')->user();
        if (!$user) {
            return $this->returnError([helperTrans('api.Unauthorized')], 401);
        }
        $image = $user->image;
        if ($request->image) {
            $image = $this->uploadFiles($request->user_type . 's', $request->file('image'), null);
        }
        $user->name = $request->name ?? $user->name;
        $user->email = $request->email ?? $user->email;
        $user->image = $image ?? $user->image;
        $user->phone = $request->phone ?? $user->phone;
        $user->location = $request->location ?? $user->location;
        $user->gender = $request->gender ?? $user->gender;
        $user->nickname = $request->nickname ?? $user->nickname;
        $user->save();
        return $this->returnData(PatientResource::make($user), [helperTrans('api.Profile Updated Successfully')]);
    }

    // ToDo::check softdelete required or no

    public function deleteAccount()
    {
        $user = auth('patient')->user();
        $row = Patient::find($user->id);
        if ($row == null) {
            return $this->returnError([helperTrans('api.This Patient Not Exist')], 401);
        }
        $row->delete();
        return $this->returnSuccessMessage([helperTrans('api.deleted successfully')]);
    }


    public function removedevicetoken(Request $request)
    {
        $device_token = FirebaseToken::where('token', $request->device_token)->delete();
        return $this->returnSuccessMessage([helperTrans('api.remove device token out successfully')]);
    }
}
