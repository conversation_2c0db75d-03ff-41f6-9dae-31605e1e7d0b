<?php

namespace App\Http\Middleware\Api;

use App\Http\Controllers\Controller;
use App\Http\Traits\Api_Trait;
use Closure;
use Exception;
use Illuminate\Http\Request;

class ApiProviderLogin extends Controller
{
    use Api_Trait;

    public function handle(Request $request, Closure $next)
    {
        try {
            /** @var \App\Models\provider $provider */
            $provider = auth('provider')->user();

            if (!$provider)
                return $this->returnError([helperTrans('api.Unauthorized')], 401);

            // if (!$provider->status) {
            //     // provider()->logout();
            //     $provider->setToken(request()->bearerToken())->invalidate();
            //     return $this->returnError([helperTrans('api.Doctoria Blocked This Account')], 401);
            // }
        } catch (Exception $e) {
            return $this->returnError([helperTrans('api.Token is invalid or expired')], 401);
        }

        return $next($request);
    }
}
