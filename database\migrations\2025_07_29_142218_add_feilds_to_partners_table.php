<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('partners', function (Blueprint $table) {
            $table->foreignId('country_id')->nullable()->constrained('countries')->nullOnDelete();
            $table->string('contact_person_position')->nullable()->after('contact_person');
            $table->foreignId('doctoria_employee_id')->nullable()->constrained('doctoria_employees')->nullOnDelete()->after('contact_person_position');
            $table->date('contract_start_date')->nullable();
            $table->date('contract_end_date')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('partners', function (Blueprint $table) {
            $table->dropForeign(['country_id', 'doctoria_employee_id']);
            $table->dropColumn('country_id');
            $table->dropColumn('doctoria_employee_id');
            $table->dropColumn('contract_start_date');
            $table->dropColumn('contract_end_date');
        });
    }
};
