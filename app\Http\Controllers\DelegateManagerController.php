<?php

namespace App\Http\Controllers;

use App\Http\Resources\Dashboard\DelegateManagerResource;
use App\Http\Traits\Api_Trait;
use App\Models\DelegateManager;
use Illuminate\Http\Request;

class DelegateManagerController extends Controller
{
    use Api_Trait;
    public function index()
    {
        $delegate_managers = DelegateManager::get();
        return $this->returnData(DelegateManagerResource::collection($delegate_managers), [helperTrans('api.Delegate Managers Data')], 200);
    }
    public function store(Request $request)
    {
        $delegate_manager = DelegateManager::create($request->all());
        return $this->returnData(new DelegateManagerResource($delegate_manager), [helperTrans('api.Delegate Manager created')], 201);
    }
    public function show(DelegateManager $delegateManager)
    {
        if (!$delegateManager) {
            return $this->returnError(helperTrans('api.Delegate Manager Not Found'), 404);
        }
        return $this->returnData(new DelegateManagerResource($delegateManager), [helperTrans('api.Delegate Manager Data')], 200);
    }
    public function update(Request $request, DelegateManager $delegateManager)
    {
        if (!$delegateManager) {
            return $this->returnError(helperTrans('api.Delegate Manager Not Found'), 404);
        }
        $delegateManager->update($request->all());
        return $this->returnData(new DelegateManagerResource($delegateManager), [helperTrans('api.Delegate Manager updated')], 200);
    }
    public function destroy(DelegateManager $delegateManager)
    {
        if (!$delegateManager) {
            return $this->returnError(helperTrans('api.Delegate Manager Not Found'), 404);
        }
        $delegateManager->delete();
        return $this->returnData(new DelegateManagerResource($delegateManager), [helperTrans('api.Delegate Manager deleted')], 200);
    }
}
